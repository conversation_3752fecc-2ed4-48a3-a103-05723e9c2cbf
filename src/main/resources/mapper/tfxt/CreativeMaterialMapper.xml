<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.tfxt.CreativeMaterialMapper">


	<select id="selectCreativeMaterials" resultType="com.wbgame.pojo.jettison.vo.CreativeMaterialVo">
	    select id,fileName,day,a.signature,signature2,type,format,height,wide,round(size*1024,2) size,sum(spend) spend,sum(spend7) spend7,sum(spend30) spend30 from dn_jettison_material a left join
		dn_batch_material_spend b on a.signature2 = b.signature
	    where 1=1
		<if test="tag_app_name != null and tag_app_name != ''">
		  and tag_app_name = #{tag_app_name}
		</if>
		<if test="ad_platform != null and ad_platform != ''">
			and ad_platform in (${ad_platform})
		</if>
		<if test="start_time != null and start_time != ''">
			and day between #{start_time} and #{end_time}
		</if>
		<if test="artist != null and artist != ''">
			and artist = #{artist}
		</if>
		<if test="creatives != null and creatives != ''">
			and creatives = #{creatives}
		</if>
		<if test="immortal3d != null and immortal3d != ''">
			and immortal3d = #{immortal3d}
		</if>
		<if test="producer3d != null and producer3d != ''">
			and producer3d = #{producer3d}
		</if>
		<if test="product_part != null and product_part != ''">
			and product_part = #{product_part}
		</if>
		<if test="label1 != null and label1 != ''">
			and label1 = #{label1}
		</if>
		<if test="label2 != null and label2 != ''">
			and label2 = #{label2}
		</if>
		<if test="label3 != null and label3 != ''">
			and label3 = #{label3}
		</if>
		<if test="format != null and format != ''">
			and format = #{format}
		</if>
		<if test="wide != null and wide != ''">
			and wide = #{wide}
		</if>
		<if test="height != null and height != ''">
			and height = #{height}
		</if>
		<if test="size != null and size != ''">
			and size <![CDATA[ <= ]]> #{size}
		</if>
		<if test="type != null and type != ''">
			and type = #{type}
		</if>
		<if test="fileName != null and fileName != ''">
			and fileName like concat('%',#{fileName},'%')
		</if>
		<if test="pathName != null and pathName != ''">
			and pathName like concat('%',#{pathName},'%')
		</if>
		group by a.signature2
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by spend desc
			</otherwise>
		</choose>
	</select>

    <select id="selectVivoMaterialRule" resultType="com.wbgame.pojo.jettison.vo.VivoMaterialRule">
		select * from dn_vivo_material_rule
		where ad_type = #{ad_type} and media_type = #{media_type} and place_type = #{place_type}
		 <if test="display_mode != null">
			 and display_mode = #{display_mode}
		 </if>
		<if test="material_norm_id != null">
			and material_norm_id = #{material_norm_id}
		</if>
	</select>

    <select id="selectOppoMaterialRule" resultType="com.wbgame.pojo.jettison.vo.OppoMaterialRule">
		select * from dn_oppo_material_rule
		where extension_type = #{extension_type} and extension_flow = #{extension_flow}
		and flow_scene = #{flow_scene}
		<if test="global_spec_id != null">
			and global_spec_id = #{global_spec_id}
		</if>
	</select>

    <select id="selectHonorMaterialRule" resultType="com.wbgame.pojo.jettison.vo.HonorMaterialRule">
		select * from dn_honor_batch_rule
		where promotion_purpose = #{promotion_purpose} and traffic_type = #{traffic_type}
		<if test="ad_placement_id != null">
			and ad_placement_id = #{ad_placement_id}
		</if>
		<if test="ad_creative_spec_id != null">
			and ad_creative_spec_id = #{ad_creative_spec_id}
		</if>
	</select>

</mapper>