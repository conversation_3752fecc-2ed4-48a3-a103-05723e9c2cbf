<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.adb.UmengMonitorMapper">

    <select id="selectUmengTaskLog" resultType="com.wbgame.pojo.UmengMonitorVo">
        select ds,synds,task,msg,status,`time`,total,DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') update_time,DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') create_time
        from data_ym.umeng_task_log
        <where>1=1
        <if test="synStartTime != null and synStartTime != ''">
            and synds between #{synStartTime} and #{synEndTime}
        </if>
        <if test="startTime != null and startTime != ''">
            and ds between #{startTime} and #{endTime}
        </if>
        <if test="ds != null and ds != ''">
            and ds = #{ds}
        </if>
        <if test="task != null and task != ''">
            and task = #{task}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        </where>
        <choose>
            <when test="order != null and order != ''">
                order by ${order}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getTasks" resultType="java.lang.String">
        select task from data_ym.umeng_task_log group by task
    </select>



    <select id="getUmengAdIncomeList" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
        <include refid="getUmengAdIncomeSql"/>
    </select>

    <select id="getUmengAdIncomeSum" parameterType="java.util.Map" resultType="com.wbgame.pojo.advert.UmengAdIncomeReportVo">
        select
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,

        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        round(sum(b.video_income)/sum(b.actnum),3) video_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),3) native_new_plaque_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),3) native_new_banner_arpu,
        round(sum(b.banner_income)/sum(b.actnum),3) banner_arpu,
        round(sum(b.total_income)/sum(b.actnum),2) dau_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) msg_arpu,
        round(sum(b.splash_income)/sum(b.actnum),3) splash_arpu,
        round(sum(b.plaque_income)/sum(b.actnum),3) plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) plaque_video_arpu,
        round(sum(b.system_splash_income)/sum(b.actnum),3) system_splash_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) suspend_icon_arpu,
        round((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),3) total_splash_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),3) total_plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) total_plaque_video_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),3) total_banner_arpu,
        round(sum(b.video_income)/sum(b.actnum),3) total_video_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) total_native_msg_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,



        round((sum(banner_income)*1000/sum(banner_show)),2) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),2) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),2) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),2) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),2) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),2) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),2) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),2) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),2) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),2) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),2) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),2) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),2) native_new_banner_ecpm,

        round(sum(total_income),2) total_income,
        round(sum(native_plaque_income),2) native_plaque_income,
        round(sum(video_income),2) video_income,
        round(sum(native_msg_income),2) native_msg_income,
        round(sum(suspend_icon_income),2) suspend_icon_income,
        round(sum(splash_income),2) splash_income,
        round(sum(banner_income),2) banner_income,
        round(sum(plaque_video_income),2) plaque_video_income,
        round(sum(system_splash_income),2) system_splash_income,
        round(sum(native_banner_income),2) native_banner_income,
        round(sum(native_new_banner_income)+sum(native_banner_income),2) native_new_banner_income,
        round(sum(native_splash_income),2) native_splash_income,
        round(sum(plaque_income),2) plaque_income,
        round(sum(native_new_plaque_income)+sum(native_plaque_income),2) native_new_plaque_income,

        concat(ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0),'%') splash_ctr,
        concat(ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0),'%') native_splash_ctr,
        concat(ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0),'%') plaque_ctr,
        concat(ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0),'%') native_plaque_ctr,
        concat(ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0),'%') native_new_plaque_ctr,
        concat(ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0),'%') plaque_video_ctr,
        concat(ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0),'%') banner_ctr,
        concat(ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0),'%') native_banner_ctr,
        concat(ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0),'%') native_new_banner_ctr,
        concat(ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0),'%') video_ctr,
        concat(ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0),'%') native_msg_ctr,
        concat(ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0),'%') suspend_icon_ctr,
        concat(IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0),'%') all_total_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        CONVERT(sum(b.t_duration)/sum(b.actnum),SIGNED)  daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        CONCAT(IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0),'%') all_total_fill,
        CONCAT(IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2),0),'%') total_system_splash_fill,
        CONCAT(IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2),0),'%') total_splash_fill,
        CONCAT(IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2),0),'%') total_native_splash_fill,
        CONCAT(IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2),0),'%') total_plaque_fill,
        CONCAT(IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2),0),'%') total_native_new_plaque_fill,
        CONCAT(IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2),0),'%') total_plaque_video_fill,
        CONCAT(IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2),0),'%') total_banner_fill,
        CONCAT(IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2),0),'%') total_native_new_banner_fill,
        CONCAT(IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2),0),'%') total_video_fill,
        CONCAT(IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2),0),'%') total_native_msg_fill,
        CONCAT(IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2),0),'%') total_suspend_icon_fill,

        concat(IFNULL(round(sum(b.show_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_splash_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_plaque_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_banner_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_video_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_video_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_msg_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_icon_ad_active_cnt,

        concat(IFNULL(round(sum(b.click_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_splash_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_plaque_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_banner_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_video_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_video_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_msg_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_icon_ad_active_cnt,
        concat(IFNULL(round(sum(b.show_total_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') show_total_ad_active_cnt,
        concat(IFNULL(round(sum(b.click_total_ad_active_cnt)/sum(b.actnum)*100,2), 0),'%') click_total_ad_active_cnt

        from
        (
        SELECT
            c.tdate,c.appkey,c.channel,c.media,c.gameName,c.temp_id,c.temp_name,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
            c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
            c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
            c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
            c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
            c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.avgnum,c.banner_click,
            c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
            c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
            c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
            c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
            c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
            c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,c.banner_request,c.plaque_request,
            c.splash_request,c.video_request,c.native_banner_request,c.native_plaque_request,c.native_splash_request,
            c.plaque_video_request,c.native_msg_request,c.system_splash_request,c.native_new_plaque_request,
            c.native_new_banner_request,c.suspend_icon_request,c.banner_fill,c.plaque_fill,c.splash_fill,c.video_fill,
            c.native_banner_fill,c.native_plaque_fill,c.native_splash_fill,c.plaque_video_fill,c.native_msg_fill,
            c.system_splash_fill,c.native_new_plaque_fill,c.native_new_banner_fill,c.suspend_icon_fill,
            c.show_splash_ad_active_cnt, c.show_plaque_ad_active_cnt, c.show_banner_ad_active_cnt, c.show_video_ad_active_cnt,
            c.show_msg_ad_active_cnt, c.show_icon_ad_active_cnt, c.click_splash_ad_active_cnt, c.click_plaque_ad_active_cnt,
            c.click_banner_ad_active_cnt, c.click_video_ad_active_cnt, c.click_msg_ad_active_cnt, c.click_icon_ad_active_cnt,
            c.show_total_ad_active_cnt, c.click_total_ad_active_cnt,
            <choose>
                <when test="dataSource != null and dataSource == 3">
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','uc') THEN i.activate_add
                        WHEN c.channel IN ('h5_huawei') THEN j.activate_add
                        ELSE f.new_users
                    END AS addnum,
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','uc') THEN i.active_user
                        WHEN c.channel IN ('h5_huawei') THEN j.active_user
                        ELSE f.act_users
                    END AS actnum,
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','h5_huawei','uc') THEN g.active_time
                        ELSE TIME_TO_SEC(d.duration) * d.act_num
                    END AS t_duration
                </when>
                <otherwise>
                    c.actnum,c.addnum,
                    d.duration daily_duration,
                    TIME_TO_SEC(d.duration) * d.act_num t_duration
                </otherwise>
            </choose>
        FROM (select tdate,appid,appname,appkey,actnum,total_income,dau_arpu,banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,video_ecpm,plaque_video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
            native_plaque_show,native_splash_show,video_show,plaque_video_show,banner_income,plaque_income,splash_income,native_banner_income,native_plaque_income,native_splash_income,video_income,plaque_video_income,msg_pv,msg_arpu,native_msg_ecpm,native_msg_show,native_msg_income,plaque_video_pv,plaque_video_arpu,addnum,avgnum,banner_click,
            plaque_click,splash_click,native_banner_click,native_plaque_click,native_splash_click,video_click,plaque_video_click,native_msg_click,system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,system_splash_arpu,native_new_plaque_arpu,
            native_new_banner_arpu,suspend_icon_arpu,system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,native_plaque_pv,native_banner_pv,
            media,gameName,temp_id,temp_name,source,active_temp_id,active_temp_name,banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,native_splash_request,plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,native_new_banner_request,suspend_icon_request,
            banner_fill,plaque_fill,splash_fill,video_fill,native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,show_msg_ad_active_cnt,
            show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,click_total_ad_active_cnt,show_total_ad_active_cnt,ad_violation_type,large_ver,
            <choose>
                <when test="dataSource != null and dataSource == 1">
                    CASE
                    WHEN channel = 'uc' THEN 'h5_oppo'
                    ELSE channel
                    END channel
                </when>
                <otherwise>
                    channel
                </otherwise>
            </choose>
            from ${tableName}
            ) c
        LEFT JOIN data_ym.umeng_user_channel_total d
        ON c.tdate = d.tdate
        AND REPLACE(c.appkey,'_2','') = d.app_key
        AND c.channel = d.install_channel
        left join (SELECT a.appid, a.channel, a.packagename FROM( SELECT appid, channel, MAX(createTime) AS max_createTime FROM adv_platform_app_info WHERE bindEndTime >= CURRENT_DATE GROUP BY appid, channel) AS latest_app INNER JOIN adv_platform_app_info a ON latest_app.appid = a.appid AND latest_app.channel = a.channel AND latest_app.max_createTime = a.createTime) h
        on c.appid = h.appid and c.channel = h.channel
        <if test="dataSource != null and dataSource == 3">
            LEFT JOIN dnwx_bi.ads_dim_users_info_3d_hourly f
            ON c.tdate=f.tdate
            AND c.appid = f.appid
            AND c.channel = f.download_channel
            LEFT JOIN (
            select tdate,appid,channel,sum(active_time) AS active_time from (SELECT tdate, appid, CASE WHEN channel IN( 'uc') THEN channel WHEN provider IN ( 'oppo', 'vivo', 'huawei') THEN concat( 'h5_', provider ) ELSE channel END AS channel, active_time FROM dnwx_bi.ads_wechat_add_active_daily WHERE provider IN ( 'oppo', 'vivo', 'huawei', 'uc' ) and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}) a1
            group by tdate,appid,channel) g
            ON c.tdate = g.tdate
            AND c.appid = g.appid
            AND c.channel = g.channel

            LEFT JOIN (
            select tdate,appid,channel,sum(new_users) AS activate_add,sum(act_users) AS active_user from (SELECT a.*, CASE WHEN app_category IN ('46') THEN 'h5_vivo' WHEN app_category IN ('46') THEN 'h5_vivo' WHEN download_channel IN ('uc') THEN download_channel WHEN app_category IN ('51') THEN 'h5_oppo' ELSE download_channel END AS channel
            FROM ads_dim_users_info_4d_hourly a LEFT JOIN app_info b ON a.appid = b.id WHERE appid IN (SELECT id FROM app_info WHERE app_category IN ('51','46')) and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}) a2
            GROUP BY tdate,appid,channel) i
            ON c.tdate = i.tdate
            AND c.appid = i.appid
            AND c.channel = i.channel

            LEFT JOIN (
            SELECT tdate,appid,'h5_huawei' channel,sum(new_users) activate_add,sum(act_users) active_user FROM ads_dim_users_info_2d_hourly
            WHERE tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date} GROUP BY tdate,appid) j
            ON c.tdate = j.tdate
            AND c.appid = j.appid
            AND c.channel = j.channel
        </if>

        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="large_ver != null and large_ver != '' ">
            and c.large_ver like concat('%',#{large_ver},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="match_str != null and match_str != ''">
            and ${match_str}
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag_rev != ''">
                    AND CONCAT(c.appid,'#',c.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(c.appid,'#',c.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        <if test="packagename != null and packagename != ''">
            and h.packagename like concat('%',#{packagename},'%')
        </if>
        <if test="ad_violation_type != null and ad_violation_type != ''">
            <foreach collection="ad_violation_type.split(',')" item="type" open="and (" close=")" separator=" or ">
                FIND_IN_SET(${type},REPLACE(ad_violation_type, '|', ','))
            </foreach>
        </if>
        ) b
    </select>

    <sql id="getUmengAdIncomeSql">
        select
        b.tdate,'${dataSource}' as dataSource
        <if test="group != null and group != '' and group.contains('appkey') ">
            ,b.appid,b.appkey
        </if>
        <if test="group != null and group != '' and group.contains('channel') ">
            ,b.channel
        </if>
        <if test="group != null and group != '' and group.contains('media')">
            ,b.media
        </if>
        <if test="group != null and group != '' and group.contains('source') ">
            ,case b.source
                when '2' then '自统计'
                else '媒体'
            end source
        </if>
        ,b.appname, b.gameName, b.temp_id, b.temp_name,b.large_ver,b.active_temp_id, b.active_temp_name,b.packagename,b.appid_tag,b.ad_violation_type,
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,
        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show) + sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click) + sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        round(sum(b.video_income)/sum(b.actnum),3) video_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),3) native_new_plaque_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),3) native_new_banner_arpu,
        round(sum(b.banner_income)/sum(b.actnum),3) banner_arpu,
        round(sum(b.total_income)/sum(b.actnum),2) dau_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) msg_arpu,
        round(sum(b.splash_income)/sum(b.actnum),3) splash_arpu,
        round(sum(b.plaque_income)/sum(b.actnum),3) plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) plaque_video_arpu,
        round(sum(b.system_splash_income)/sum(b.actnum),3) system_splash_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) suspend_icon_arpu,
        round((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),3) total_splash_arpu,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),3) total_plaque_arpu,
        round(sum(b.plaque_video_income)/sum(b.actnum),3) total_plaque_video_arpu,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),3) total_banner_arpu,
        round(sum(b.video_income)/sum(b.actnum),3) total_video_arpu,
        round(sum(b.native_msg_income)/sum(b.actnum),3) total_native_msg_arpu,
        round(sum(b.suspend_icon_income)/sum(b.actnum),3) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,



        IFNULL(round((sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
                +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))/sum(b.actnum), 2), 0) all_total_pv,
        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
                +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/sum(b.actnum),2), 0) all_total_click,

        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0) all_total_ctr,

        round((sum(banner_income)*1000/sum(banner_show)),2) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),2) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),2) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),2) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),2) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),2) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),2) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),2) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),2) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),2) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),2) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),2) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),2) native_new_banner_ecpm,

        ifnull(round(sum(total_income),2),0) total_income,
        round(sum(native_plaque_income),2) native_plaque_income,
        round(sum(video_income),2) video_income,
        round(sum(native_msg_income),2) native_msg_income,
        round(sum(suspend_icon_income),2) suspend_icon_income,
        round(sum(splash_income),2) splash_income,
        round(sum(banner_income),2) banner_income,
        round(sum(plaque_video_income),2) plaque_video_income,
        round(sum(system_splash_income),2) system_splash_income,
        round(sum(native_banner_income),2) native_banner_income,
        round((sum(native_new_banner_income)+sum(native_banner_income)),2) native_new_banner_income,
        round(sum(native_splash_income),2) native_splash_income,
        round(sum(plaque_income),2) plaque_income,
        round((sum(native_new_plaque_income)+sum(native_plaque_income)),2) native_new_plaque_income,

        ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0) splash_ctr,
        ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0) native_splash_ctr,
        ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0) plaque_ctr,
        ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0) native_plaque_ctr,
        ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0) native_new_plaque_ctr,
        ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0) plaque_video_ctr,
        ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0) banner_ctr,
        ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0) native_banner_ctr,
        ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0) native_new_banner_ctr,
        ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0) video_ctr,
        ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0) native_msg_ctr,
        ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0) suspend_icon_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        CONVERT(sum(b.t_duration)/sum(b.actnum),SIGNED)  daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0) all_total_fill,
        IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2), 0) total_system_splash_fill,
        IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2), 0) total_splash_fill,
        IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2), 0) total_native_splash_fill,
        IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2), 0) total_plaque_fill,
        IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2), 0) total_native_new_plaque_fill,
        IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2), 0) total_plaque_video_fill,
        IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2), 0) total_banner_fill,
        IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2), 0) total_native_new_banner_fill,
        IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2), 0) total_video_fill,
        IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2), 0) total_native_msg_fill,
        IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2), 0) total_suspend_icon_fill,

        IFNULL(round(sum(b.show_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_splash_ad_active_cnt,
        IFNULL(round(sum(b.show_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_plaque_ad_active_cnt,
        IFNULL(round(sum(b.show_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_banner_ad_active_cnt,
        IFNULL(round(sum(b.show_video_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_video_ad_active_cnt,
        IFNULL(round(sum(b.show_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_msg_ad_active_cnt,
        IFNULL(round(sum(b.show_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_icon_ad_active_cnt,

        IFNULL(round(sum(b.click_splash_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_splash_ad_active_cnt,
        IFNULL(round(sum(b.click_plaque_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_plaque_ad_active_cnt,
        IFNULL(round(sum(b.click_banner_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_banner_ad_active_cnt,
        IFNULL(round(sum(b.click_video_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_video_ad_active_cnt,
        IFNULL(round(sum(b.click_msg_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_msg_ad_active_cnt,
        IFNULL(round(sum(b.click_icon_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_icon_ad_active_cnt,
        IFNULL(round(sum(b.show_total_ad_active_cnt)/sum(b.actnum)*100,2), 0) show_total_ad_active_cnt,
        IFNULL(round(sum(b.click_total_ad_active_cnt)/sum(b.actnum)*100,2), 0) click_total_ad_active_cnt
        from
        (
        SELECT
            REPLACE(c.appkey,'_2','') appkey,c.channel,c.media,c.gameName,c.temp_id,c.temp_name,c.large_ver,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
            c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
            c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
            c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
            c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
            c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.avgnum,c.banner_click,
            c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
            c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
            c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
            c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
            c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
            c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,c.source,c.active_temp_id, c.active_temp_name,c.banner_request,c.plaque_request,
            c.splash_request,c.video_request,c.native_banner_request,c.native_plaque_request,c.native_splash_request,
            c.plaque_video_request,c.native_msg_request,c.system_splash_request,c.native_new_plaque_request,
            c.native_new_banner_request,c.suspend_icon_request,c.banner_fill,c.plaque_fill,c.splash_fill,c.video_fill,
            c.native_banner_fill,c.native_plaque_fill,c.native_splash_fill,c.plaque_video_fill,c.native_msg_fill,
            c.system_splash_fill,c.native_new_plaque_fill,c.native_new_banner_fill,c.suspend_icon_fill,
            c.show_splash_ad_active_cnt, c.show_plaque_ad_active_cnt, c.show_banner_ad_active_cnt, c.show_video_ad_active_cnt,
            c.show_msg_ad_active_cnt, c.show_icon_ad_active_cnt, c.click_splash_ad_active_cnt, c.click_plaque_ad_active_cnt,
            c.click_banner_ad_active_cnt, c.click_video_ad_active_cnt, c.click_msg_ad_active_cnt, c.click_icon_ad_active_cnt,
            c.show_total_ad_active_cnt, c.click_total_ad_active_cnt,h.packagename,h.appid_tag,c.ad_violation_type,
        <choose>
                <when test="dataSource != null and dataSource == 3">
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','uc') THEN i.activate_add
                        WHEN c.channel IN ('h5_huawei') THEN j.activate_add
                        ELSE f.new_users
                    END addnum,
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','uc') THEN i.active_user
                        WHEN c.channel IN ('h5_huawei') THEN j.active_user
                        ELSE f.act_users
                    END actnum,
                    CASE
                        WHEN c.channel IN ('h5_oppo', 'h5_vivo','h5_huawei','uc') THEN g.active_time
                        ELSE TIME_TO_SEC(d.duration) * d.act_num
                    END AS t_duration,
                </when>
                <otherwise>
                    c.actnum,c.addnum,
                    d.duration daily_duration,
                    TIME_TO_SEC(d.duration) * d.act_num t_duration,
                </otherwise>
            </choose>
        aa.id appid,aa.app_name appname,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{start_date},'至',#{end_date}) as tdate
            </when>
            <when test="group != null and group != '' and group.contains('tdate')">
                c.tdate
            </when>
            <when test="group != null and group != '' and group.contains('week')">
                DATE_FORMAT(c.tdate, '%x-%v') as tdate,
                DATE_FORMAT(c.tdate, '%x-%v') as week
            </when>
            <when test="group != null and group != '' and group.contains('month')">
                DATE_FORMAT(c.tdate,'%Y-%m') as tdate,
                DATE_FORMAT(c.tdate,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{start_date},'至',#{end_date}) as tdate
            </otherwise>
        </choose>
        FROM (select tdate,appid,appname,appkey,actnum,total_income,dau_arpu,banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,video_ecpm,plaque_video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
                     native_plaque_show,native_splash_show,video_show,plaque_video_show,banner_income,plaque_income,splash_income,native_banner_income,native_plaque_income,native_splash_income,video_income,plaque_video_income,msg_pv,msg_arpu,native_msg_ecpm,native_msg_show,native_msg_income,plaque_video_pv,plaque_video_arpu,addnum,avgnum,banner_click,
                     plaque_click,splash_click,native_banner_click,native_plaque_click,native_splash_click,video_click,plaque_video_click,native_msg_click,system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,system_splash_arpu,native_new_plaque_arpu,
                     native_new_banner_arpu,suspend_icon_arpu,system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,native_plaque_pv,native_banner_pv,
                     media,gameName,temp_id,temp_name,source,active_temp_id,active_temp_name,banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,native_splash_request,plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,native_new_banner_request,suspend_icon_request,
                     banner_fill,plaque_fill,splash_fill,video_fill,native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,show_msg_ad_active_cnt,
                     show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,click_total_ad_active_cnt,show_total_ad_active_cnt,ad_violation_type,large_ver,
                <choose>
                    <when test="dataSource != null and dataSource == 1">
                        CASE
                        WHEN channel = 'uc' THEN 'h5_oppo'
                        ELSE channel
                        END channel
                    </when>
                    <otherwise>
                        channel
                    </otherwise>
                </choose>
              from ${tableName}) c
        left join app_info aa on REPLACE(c.appkey,'_2','') = aa.umeng_key
        LEFT JOIN data_ym.umeng_user_channel_total d ON c.tdate = d.tdate
        AND REPLACE(c.appkey,'_2','') = d.app_key
        AND c.channel = d.install_channel
        left join (SELECT a.appid, a.channel, a.packagename,a.appid_tag FROM(SELECT appid, channel, MAX(createTime) AS max_createTime FROM adv_platform_app_info WHERE bindEndTime >= CURRENT_DATE GROUP BY appid, channel) AS latest_app INNER JOIN adv_platform_app_info a ON latest_app.appid = a.appid AND latest_app.channel = a.channel AND latest_app.max_createTime = a.createTime) h
        on c.appid = h.appid and c.channel = h.channel
        <if test="dataSource != null and dataSource == 3">
            LEFT JOIN dnwx_bi.ads_dim_users_info_3d_hourly f
            ON c.tdate=f.tdate
            AND c.appid = f.appid
            AND c.channel = f.download_channel
            LEFT JOIN (
            select tdate,appid,channel,sum(active_time) AS active_time from (SELECT tdate, appid, CASE WHEN channel IN( 'uc') THEN channel WHEN provider IN ( 'oppo', 'vivo', 'huawei') THEN concat( 'h5_', provider ) ELSE channel END AS channel, active_time FROM dnwx_bi.ads_wechat_add_active_daily WHERE provider IN ( 'oppo', 'vivo', 'huawei', 'uc' ) and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}) a1
            group by tdate,appid,channel) g
            ON c.tdate = g.tdate
            AND c.appid = g.appid
            AND c.channel = g.channel

            LEFT JOIN (
            select tdate,appid,channel,sum(new_users) AS activate_add,sum(act_users) AS active_user from (SELECT a.*, CASE WHEN app_category IN('45','57') THEN 'h5_huawei' WHEN app_category IN ('46') THEN 'h5_vivo' WHEN app_category IN ('46') THEN 'h5_vivo' WHEN download_channel IN ('uc') THEN download_channel WHEN app_category IN ('51') THEN 'h5_oppo' ELSE download_channel END AS channel
            FROM ads_dim_users_info_4d_hourly a LEFT JOIN app_info b ON a.appid = b.id WHERE appid IN (SELECT id FROM app_info WHERE app_category IN ('45','57','51','46')) and tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date}) a2
            GROUP BY tdate,appid,channel) i
            ON c.tdate = i.tdate
            AND c.appid = i.appid
            AND c.channel = i.channel

            LEFT JOIN (
            SELECT tdate,appid,'h5_huawei' channel,sum(new_users) activate_add,sum(act_users) active_user FROM ads_dim_users_info_2d_hourly
            WHERE tdate <![CDATA[>=]]> #{start_date} and tdate <![CDATA[<=]]> #{end_date} GROUP BY tdate,appid) j
            ON c.tdate = j.tdate
            AND c.appid = j.appid
            AND c.channel = j.channel
        </if>

        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="large_ver != null and large_ver != '' ">
            and c.large_ver like concat('%',#{large_ver},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="match_str != null and match_str != ''">
            and ${match_str}
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag_rev != ''">
                    AND CONCAT(c.appid,'#',c.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(c.appid,'#',c.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        <if test="packagename != null and packagename != ''">
            and h.packagename like concat('%',#{packagename},'%')
        </if>
        <if test="ad_violation_type != null and ad_violation_type != ''">
            <foreach collection="ad_violation_type.split(',')" item="type" open="and (" close=")" separator=" or ">
                FIND_IN_SET(${type},REPLACE(ad_violation_type, '|', ','))
            </foreach>
        </if>
        ) b

        <if test="group != null and group != '' ">
            group by ${group}
        </if>

        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by b.tdate asc,b.addnum+0 desc
            </otherwise>
        </choose>
    </sql>



    <insert id="insertUmengAdIncomeList" parameterType="java.util.List">
        replace into umeng_ad_income (
        tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,gameName,
        banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
        video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
        native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
        video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
        native_plaque_show,native_splash_show,video_show,banner_income,
        plaque_income,splash_income,native_banner_income,native_plaque_income,
        native_splash_income,video_income,
        plaque_video_show,plaque_video_ecpm,plaque_video_income,
        msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
        plaque_video_pv, plaque_video_arpu,addnum,avgnum,
        banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
        native_splash_click,video_click,plaque_video_click,native_msg_click,
        system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
        system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
        system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
        system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
        system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
        system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
        native_plaque_pv,native_banner_pv,temp_id,temp_name,source,active_temp_id,active_temp_name,
        banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,
        native_splash_request,plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,
        native_new_banner_request,suspend_icon_request,banner_fill,plaque_fill,splash_fill,video_fill,
        native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,
        system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,
        show_splash_ad_active_cnt,show_plaque_ad_active_cnt,show_banner_ad_active_cnt,show_video_ad_active_cnt,
        show_msg_ad_active_cnt,show_icon_ad_active_cnt,click_splash_ad_active_cnt,click_plaque_ad_active_cnt,
        click_banner_ad_active_cnt,click_video_ad_active_cnt,click_msg_ad_active_cnt,click_icon_ad_active_cnt,
        show_total_ad_active_cnt,click_total_ad_active_cnt,ad_violation_type,large_ver
        ) values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
            #{li.total_income},#{li.dau_arpu},#{li.gameName},#{li.banner_pv},#{li.plaque_pv},
            #{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
            #{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
            #{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
            #{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
            #{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
            #{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
            #{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
            #{li.native_splash_income},#{li.video_income},
            #{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
            #{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
            #{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
            #{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
            #{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
            #{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
            #{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
            #{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
            #{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
            #{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
            #{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
            #{li.native_plaque_pv},#{li.native_banner_pv},#{li.temp_id},#{li.temp_name}
            <if test="li.source != null and li.source != ''">
                ,#{li.source}
            </if>
            <if test="li.source == null or li.source == ''">
                ,1
            </if>
            ,#{li.active_temp_id},#{li.active_temp_name}
            ,#{li.banner_request},#{li.plaque_request},#{li.splash_request},#{li.video_request},#{li.native_banner_request}
            ,#{li.native_plaque_request},#{li.native_splash_request},#{li.plaque_video_request},#{li.native_msg_request}
            ,#{li.system_splash_request},#{li.native_new_plaque_request},#{li.native_new_banner_request},#{li.suspend_icon_request}
            ,#{li.banner_fill},#{li.plaque_fill},#{li.splash_fill},#{li.video_fill},#{li.native_banner_fill}
            ,#{li.native_plaque_fill},#{li.native_splash_fill},#{li.plaque_video_fill},#{li.native_msg_fill}
            ,#{li.system_splash_fill},#{li.native_new_plaque_fill},#{li.native_new_banner_fill},#{li.suspend_icon_fill}

            ,#{li.show_splash_ad_active_cnt},#{li.show_plaque_ad_active_cnt},#{li.show_banner_ad_active_cnt},#{li.show_video_ad_active_cnt}
            ,#{li.show_msg_ad_active_cnt},#{li.show_icon_ad_active_cnt},#{li.click_splash_ad_active_cnt},#{li.click_plaque_ad_active_cnt}
            ,#{li.click_banner_ad_active_cnt},#{li.click_video_ad_active_cnt},#{li.click_msg_ad_active_cnt},#{li.click_icon_ad_active_cnt}
            ,#{li.show_total_ad_active_cnt},#{li.click_total_ad_active_cnt},#{li.ad_violation_type},#{li.large_ver}
            )
        </foreach>
    </insert>

    <insert id="insertOppoAdIncomeList" parameterType="java.util.List">
        replace into oppo_ad_income (
        tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,
        banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
        video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
        native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
        video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
        native_plaque_show,native_splash_show,video_show,banner_income,
        plaque_income,splash_income,native_banner_income,native_plaque_income,
        native_splash_income,video_income,
        plaque_video_show,plaque_video_ecpm,plaque_video_income,
        msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
        plaque_video_pv, plaque_video_arpu,addnum,avgnum,
        banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
        native_splash_click,video_click,plaque_video_click,native_msg_click,
        system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
        system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
        system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
        system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
        system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
        system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
        native_plaque_pv,native_banner_pv
        ) values
        <foreach collection="list" item="li" separator=",">
            (#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
            #{li.total_income},#{li.dau_arpu},#{li.banner_pv},#{li.plaque_pv},
            #{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
            #{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
            #{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
            #{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
            #{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
            #{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
            #{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
            #{li.native_splash_income},#{li.video_income},
            #{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
            #{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
            #{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
            #{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
            #{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
            #{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
            #{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
            #{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
            #{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
            #{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
            #{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
            #{li.native_plaque_pv},#{li.native_banner_pv}
            )
        </foreach>
    </insert>

    <insert id="insertUmengAdIncomeOverseaList">
        replace into umeng_ad_income_oversea (
        tdate,appid,appname,appkey,channel,media,actnum,total_income,dau_arpu,gameName,
        banner_pv,plaque_pv,splash_pv,video_pv,banner_arpu,plaque_arpu,splash_arpu,
        video_arpu,banner_ecpm,plaque_ecpm,splash_ecpm,
        native_banner_ecpm,native_plaque_ecpm,native_splash_ecpm,
        video_ecpm,banner_show,plaque_show,splash_show,native_banner_show,
        native_plaque_show,native_splash_show,video_show,banner_income,
        plaque_income,splash_income,native_banner_income,native_plaque_income,
        native_splash_income,video_income,
        plaque_video_show,plaque_video_ecpm,plaque_video_income,
        msg_pv, msg_arpu, native_msg_ecpm, native_msg_show, native_msg_income,
        plaque_video_pv, plaque_video_arpu,addnum,avgnum,
        banner_click,plaque_click,splash_click,native_banner_click,native_plaque_click,
        native_splash_click,video_click,plaque_video_click,native_msg_click,
        system_splash_show,native_new_plaque_show,native_new_banner_show,suspend_icon_show,
        system_splash_pv,native_new_plaque_pv,native_new_banner_pv,suspend_icon_pv,
        system_splash_arpu,native_new_plaque_arpu,native_new_banner_arpu,suspend_icon_arpu,
        system_splash_ecpm,native_new_plaque_ecpm,native_new_banner_ecpm,suspend_icon_ecpm,
        system_splash_income,native_new_plaque_income,native_new_banner_income,suspend_icon_income,
        system_splash_click,native_new_plaque_click,native_new_banner_click,suspend_icon_click,
        native_plaque_pv,native_banner_pv,temp_id,temp_name,source,active_temp_id,active_temp_name,
        banner_request,plaque_request,splash_request,video_request,native_banner_request,native_plaque_request,
        native_splash_request,plaque_video_request,native_msg_request,system_splash_request,native_new_plaque_request,
        native_new_banner_request,suspend_icon_request,banner_fill,plaque_fill,splash_fill,video_fill,
        native_banner_fill,native_plaque_fill,native_splash_fill,plaque_video_fill,native_msg_fill,
        system_splash_fill,native_new_plaque_fill,native_new_banner_fill,suspend_icon_fill,

        country
        ) values
        <foreach collection="list" item="li" separator=",">
        (#{li.tdate},#{li.appid},#{li.appname},#{li.appkey},#{li.channel},#{li.media},#{li.actnum},
        #{li.total_income},#{li.dau_arpu},#{li.gameName},#{li.banner_pv},#{li.plaque_pv},
        #{li.splash_pv},#{li.video_pv},#{li.banner_arpu},#{li.plaque_arpu},
        #{li.splash_arpu},#{li.video_arpu},#{li.banner_ecpm},#{li.plaque_ecpm},
        #{li.splash_ecpm},#{li.native_banner_ecpm},#{li.native_plaque_ecpm},
        #{li.native_splash_ecpm},#{li.video_ecpm},#{li.banner_show},#{li.plaque_show},
        #{li.splash_show},#{li.native_banner_show},#{li.native_plaque_show},
        #{li.native_splash_show},#{li.video_show},#{li.banner_income},#{li.plaque_income},
        #{li.splash_income},#{li.native_banner_income},#{li.native_plaque_income},
        #{li.native_splash_income},#{li.video_income},
        #{li.plaque_video_show},#{li.plaque_video_ecpm},#{li.plaque_video_income},
        #{li.msg_pv},#{li.msg_arpu},#{li.native_msg_ecpm},#{li.native_msg_show},#{li.native_msg_income},
        #{li.plaque_video_pv},#{li.plaque_video_arpu},#{li.addnum},#{li.avgnum},
        #{li.banner_click},#{li.plaque_click},#{li.splash_click},#{li.native_banner_click},#{li.native_plaque_click},
        #{li.native_splash_click},#{li.video_click},#{li.plaque_video_click},#{li.native_msg_click},
        #{li.system_splash_show},#{li.native_new_plaque_show},#{li.native_new_banner_show},#{li.suspend_icon_show},
        #{li.system_splash_pv},#{li.native_new_plaque_pv},#{li.native_new_banner_pv},#{li.suspend_icon_pv},
        #{li.system_splash_arpu},#{li.native_new_plaque_arpu},#{li.native_new_banner_arpu},#{li.suspend_icon_arpu},
        #{li.system_splash_ecpm},#{li.native_new_plaque_ecpm},#{li.native_new_banner_ecpm},#{li.suspend_icon_ecpm},
        #{li.system_splash_income},#{li.native_new_plaque_income},#{li.native_new_banner_income},#{li.suspend_icon_income},
        #{li.system_splash_click},#{li.native_new_plaque_click},#{li.native_new_banner_click},#{li.suspend_icon_click},
        #{li.native_plaque_pv},#{li.native_banner_pv},#{li.temp_id},#{li.temp_name}
        <if test="li.source != null and li.source != ''">
            ,#{li.source}
        </if>
        <if test="li.source == null or li.source == ''">
            ,1
        </if>
        ,#{li.active_temp_id},#{li.active_temp_name}
        ,#{li.banner_request},#{li.plaque_request},#{li.splash_request},#{li.video_request},#{li.native_banner_request}
        ,#{li.native_plaque_request},#{li.native_splash_request},#{li.plaque_video_request},#{li.native_msg_request}
        ,#{li.system_splash_request},#{li.native_new_plaque_request},#{li.native_new_banner_request},#{li.suspend_icon_request}
        ,#{li.banner_fill},#{li.plaque_fill},#{li.splash_fill},#{li.video_fill},#{li.native_banner_fill}
        ,#{li.native_plaque_fill},#{li.native_splash_fill},#{li.plaque_video_fill},#{li.native_msg_fill}
        ,#{li.system_splash_fill},#{li.native_new_plaque_fill},#{li.native_new_banner_fill},#{li.suspend_icon_fill}

        ,#{li.country}
        )
        </foreach>
    </insert>

    <select id="getUmengAdIncomeOverseaSum" resultType="com.wbgame.pojo.advert.UmengOverseaReportVo">
        select
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,

        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        IFNULL(CONVERT(sum(b.video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) video_arpu,
        IFNULL(CONVERT((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) native_new_plaque_arpu,
        IFNULL(CONVERT((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) native_new_banner_arpu,
        IFNULL(CONVERT(sum(b.banner_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) banner_arpu,
        IFNULL(CONVERT(sum(b.total_income)/sum(b.actnum),DECIMAL(10, 3)),0.000) dau_arpu,
        IFNULL(CONVERT(sum(b.native_msg_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) msg_arpu,
        IFNULL(CONVERT(sum(b.splash_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) splash_arpu,
        IFNULL(CONVERT(sum(b.plaque_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) plaque_arpu,
        IFNULL(CONVERT(sum(b.plaque_video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) plaque_video_arpu,
        IFNULL(CONVERT(sum(b.system_splash_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) system_splash_arpu,
        IFNULL(CONVERT(sum(b.suspend_icon_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) suspend_icon_arpu,
        IFNULL(CONVERT((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_splash_arpu,
        IFNULL(CONVERT((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_plaque_arpu,
        IFNULL(CONVERT(sum(b.plaque_video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_plaque_video_arpu,
        IFNULL(CONVERT((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_banner_arpu,
        IFNULL(CONVERT(sum(b.video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_video_arpu,
        IFNULL(CONVERT(sum(b.native_msg_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_native_msg_arpu,
        IFNULL(CONVERT(sum(b.suspend_icon_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,



        round((sum(banner_income)*1000/sum(banner_show)),3) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),3) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),3) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),3) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),3) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),3) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),3) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),3) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),3) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),3) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),3) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),3) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),3) native_new_banner_ecpm,

        round(sum(total_income),3) total_income,
        round(sum(native_plaque_income),3) native_plaque_income,
        round(sum(video_income),3) video_income,
        round(sum(native_msg_income),3) native_msg_income,
        round(sum(suspend_icon_income),3) suspend_icon_income,
        round(sum(splash_income),3) splash_income,
        round(sum(banner_income),3) banner_income,
        round(sum(plaque_video_income),3) plaque_video_income,
        round(sum(system_splash_income),3) system_splash_income,
        round(sum(native_banner_income),3) native_banner_income,
        round(sum(native_new_banner_income)+sum(native_banner_income),3) native_new_banner_income,
        round(sum(native_splash_income),3) native_splash_income,
        round(sum(plaque_income),3) plaque_income,
        round(sum(native_new_plaque_income)+sum(native_plaque_income),3) native_new_plaque_income,

        concat(ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0),'%') splash_ctr,
        concat(ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0),'%') native_splash_ctr,
        concat(ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0),'%') plaque_ctr,
        concat(ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0),'%') native_plaque_ctr,
        concat(ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0),'%') native_new_plaque_ctr,
        concat(ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0),'%') plaque_video_ctr,
        concat(ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0),'%') banner_ctr,
        concat(ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0),'%') native_banner_ctr,
        concat(ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0),'%') native_new_banner_ctr,
        concat(ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0),'%') video_ctr,
        concat(ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0),'%') native_msg_ctr,
        concat(ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0),'%') suspend_icon_ctr,
        concat(IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0),'%') all_total_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        b.daily_duration daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        CONCAT(IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0),'%') all_total_fill,
        CONCAT(IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2),0),'%') total_system_splash_fill,
        CONCAT(IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2),0),'%') total_splash_fill,
        CONCAT(IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2),0),'%') total_native_splash_fill,
        CONCAT(IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2),0),'%') total_plaque_fill,
        CONCAT(IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2),0),'%') total_native_new_plaque_fill,
        CONCAT(IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2),0),'%') total_plaque_video_fill,
        CONCAT(IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2),0),'%') total_banner_fill,
        CONCAT(IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2),0),'%') total_native_new_banner_fill,
        CONCAT(IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2),0),'%') total_video_fill,
        CONCAT(IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2),0),'%') total_native_msg_fill,
        CONCAT(IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2),0),'%') total_suspend_icon_fill
        from
        (
        SELECT
        c.tdate,c.appkey,c.channel,c.media,c.gameName,c.temp_id,c.temp_name,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
        c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
        c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
        c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
        c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
        c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.avgnum,c.banner_click,
        c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
        c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
        c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
        c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
        c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
        c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,c.banner_request,c.plaque_request,
        c.splash_request,c.video_request,c.native_banner_request,c.native_plaque_request,c.native_splash_request,
        c.plaque_video_request,c.native_msg_request,c.system_splash_request,c.native_new_plaque_request,
        c.native_new_banner_request,c.suspend_icon_request,c.banner_fill,c.plaque_fill,c.splash_fill,c.video_fill,
        c.native_banner_fill,c.native_plaque_fill,c.native_splash_fill,c.plaque_video_fill,c.native_msg_fill,
        c.system_splash_fill,c.native_new_plaque_fill,c.native_new_banner_fill,c.suspend_icon_fill,

        <choose>
            <when test="dataSource != null and dataSource == 3">
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.activate_add
                ELSE f.new_users
                END AS addnum,
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.active_user
                ELSE f.act_users
                END AS actnum,
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.active_time * g.active_user
                ELSE TIME_TO_SEC(d.duration) * d.act_num
                END AS t_duration
            </when>
            <otherwise>
                sum(d.act_users) actnum,sum(d.new_users) addnum,
                cast(avg(second_avg) as int) daily_duration,
                cast(avg(single_avg) as int) t_duration
            </otherwise>
        </choose>
        FROM ${tableName} c
        LEFT JOIN ads_dim_users_info_5d_country_hourly_oversea d
        ON c.tdate=d.t_date AND c.appid=d.appid AND c.channel=d.download_channel AND c.country=d.country_code

        left join
        (select a_day, appid, install_channel,country b_country, sum(act_num) act_num, sum(add_num) add_num, sum(start_num) start_num,
        cast(avg(second_avg) as int) second_avg, cast(avg(single_avg) as int) single_avg from ads_umeng_custom_total_daily_oversea
        where a_day between #{start_date} and #{end_date} GROUP BY a_day, appid, install_channel,country)
        bb on c.tdate = bb.a_day and c.appid = bb.appid and c.channel = bb.install_channel and c.country = bb.b_country

        <if test="dataSource != null and dataSource == 3">
            LEFT JOIN dnwx_bi.ads_dim_users_info_3d_hourly f
            ON c.tdate=f.tdate
            AND c.appid = f.appid
            AND c.channel = f.download_channel
            LEFT JOIN (
            SELECT tdate,appid,provider,sum(activate_add) AS activate_add,sum(active_user) AS active_user,sum(active_time) AS active_time
            FROM dnwx_bi.ads_wechat_add_active_daily WHERE provider IN ('oppo','vivo') GROUP BY tdate,appid,provider) g
            ON c.tdate = g.tdate
            AND c.appid = g.appid
            AND c.channel = concat('h5_',g.provider)
        </if>

        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM dnwx_bi.ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="match_str != null and match_str != ''">
            and ${match_str}
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="country != null and country != '' ">
            and c.country in (${country})
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag != ''">
                    AND CONCAT(c.appid,'#',c.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(c.appid,'#',c.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        group by c.tdate,c.appid,c.channel,c.country
        ) b
    </select>
    <select id="getUmengAdIncomeOverseaList" resultType="com.wbgame.pojo.advert.UmengOverseaReportVo">
        <include refid="selectPlatformAdDataListFromUmengSql"/>
    </select>

    <sql id="selectPlatformAdDataListFromUmengSql">
        select
        b.tdate,'${dataSource}' as dataSource
        <if test="group != null and group != '' and group.contains('appkey') ">
            ,b.appid,b.appkey
        </if>
        <if test="group != null and group != '' and group.contains('channel') ">
            ,b.channel
        </if>
        <if test="group != null and group != '' and group.contains('media')">
            ,b.media
        </if>
        <if test="group != null and group != '' and group.contains('source') ">
            ,case b.source
            when '2' then '自统计'
            else '媒体'
            end source
        </if>
        ,b.appname, b.gameName, b.temp_id, b.temp_name,b.active_temp_id, b.active_temp_name,b.country,
        sum(b.addnum) addnum,sum(b.actnum) actnum,
        round(sum(b.addnum)/sum(b.actnum),4) avgnum,
        round(sum(b.native_msg_show)/sum(b.actnum),2) msg_pv,
        round(sum(b.banner_show)/sum(b.actnum),2) banner_pv,
        round(sum(b.plaque_show)/sum(b.actnum),2) plaque_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) native_msg_pv,
        round((sum(b.native_new_banner_show) + sum(b.native_banner_show))/sum(b.actnum),2) native_new_banner_pv,
        round(sum(b.native_splash_show)/sum(b.actnum),2) native_splash_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) plaque_video_pv,
        round(sum(b.native_plaque_show)/sum(b.actnum),2) native_plaque_pv,
        round(sum(b.native_banner_show)/sum(b.actnum),2) native_banner_pv,
        round(sum(b.system_splash_show)/sum(b.actnum),2) system_splash_pv,
        round(sum(b.splash_show)/sum(b.actnum),2) splash_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) suspend_icon_pv,
        round(sum(b.video_show)/sum(b.actnum),2) video_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show))/sum(b.actnum),2) native_new_plaque_pv,
        round((sum(b.system_splash_show)+sum(b.native_splash_show)+sum(splash_show))/sum(b.actnum),2) total_splash_pv,
        round((sum(b.native_new_plaque_show)+sum(b.native_plaque_show)+sum(b.plaque_show))/sum(b.actnum),2) total_plaque_pv,
        round(sum(b.plaque_video_show)/sum(b.actnum),2) total_plaque_video_pv,
        round((sum(b.native_new_banner_show)+sum(b.native_banner_show)+sum(b.banner_show))/sum(b.actnum),2) total_banner_pv,
        round(sum(b.video_show)/sum(b.actnum),2) total_video_pv,
        round(sum(b.native_msg_show)/sum(b.actnum),2) total_native_msg_pv,
        round(sum(b.suspend_icon_show)/sum(b.actnum),2) total_suspend_icon_pv,

        sum(banner_click) banner_click,
        sum(native_splash_click) native_splash_click,
        sum(native_plaque_click) native_plaque_click,
        sum(native_new_plaque_click) + sum(native_plaque_click) native_new_plaque_click,
        sum(native_msg_click) native_msg_click,
        sum(video_click) video_click,
        sum(splash_click) splash_click,
        sum(plaque_video_click) plaque_video_click,
        sum(system_splash_click) system_splash_click,
        sum(suspend_icon_click) suspend_icon_click,
        sum(plaque_click) plaque_click,
        sum(native_banner_click) native_banner_click,
        sum(native_new_banner_click) + sum(native_banner_click) native_new_banner_click,

        round((sum(b.system_splash_click)+sum(b.native_splash_click)+sum(splash_click))/sum(b.actnum),2) total_splash_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click)+sum(b.plaque_click))/sum(b.actnum),2) total_plaque_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) total_plaque_video_click,
        round((sum(b.native_new_banner_click)+sum(b.native_banner_click)+sum(b.banner_click))/sum(b.actnum),2) total_banner_click,
        round(sum(b.video_click)/sum(b.actnum),2) total_video_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) total_native_msg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) total_suspend_icon_click,

        round(sum(b.banner_click)/sum(b.actnum),2) banner_avg_click,
        round(sum(b.plaque_click)/sum(b.actnum),2) plaque_avg_click,
        round(sum(b.native_msg_click)/sum(b.actnum),2) native_msg_avg_click,
        round((sum(b.native_new_banner_click) + sum(b.native_banner_click))/sum(b.actnum),2) native_new_banner_avg_click,
        round(sum(b.native_splash_click)/sum(b.actnum),2) native_splash_avg_click,
        round(sum(b.plaque_video_click)/sum(b.actnum),2) plaque_video_avg_click,
        round(sum(b.native_plaque_click)/sum(b.actnum),2) native_plaque_avg_click,
        round(sum(b.native_banner_click)/sum(b.actnum),2) native_banner_avg_click,
        round(sum(b.system_splash_click)/sum(b.actnum),2) system_splash_avg_click,
        round(sum(b.splash_click)/sum(b.actnum),2) splash_avg_click,
        round(sum(b.suspend_icon_click)/sum(b.actnum),2) suspend_icon_avg_click,
        round(sum(b.video_click)/sum(b.actnum),2) video_avg_click,
        round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/sum(b.actnum),2) native_new_plaque_avg_click,

        IFNULL(CONVERT(sum(b.video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) video_arpu,
        IFNULL(CONVERT((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) native_new_plaque_arpu,
        IFNULL(CONVERT((sum(b.native_new_banner_income)+sum(b.native_banner_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) native_new_banner_arpu,
        IFNULL(CONVERT(sum(b.banner_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) banner_arpu,
        IFNULL(CONVERT(sum(b.total_income)/sum(b.actnum),DECIMAL(10, 3)),0.000) dau_arpu,
        IFNULL(CONVERT(sum(b.native_msg_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) msg_arpu,
        IFNULL(CONVERT(sum(b.splash_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) splash_arpu,
        IFNULL(CONVERT(sum(b.plaque_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) plaque_arpu,
        IFNULL(CONVERT(sum(b.plaque_video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) plaque_video_arpu,
        IFNULL(CONVERT(sum(b.system_splash_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) system_splash_arpu,
        IFNULL(CONVERT(sum(b.suspend_icon_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) suspend_icon_arpu,
        IFNULL(CONVERT((sum(b.system_splash_income)+sum(b.native_splash_income)+sum(b.splash_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_splash_arpu,
        IFNULL(CONVERT((sum(b.native_new_plaque_income)+sum(b.native_plaque_income)+sum(b.plaque_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_plaque_arpu,
        IFNULL(CONVERT(sum(b.plaque_video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_plaque_video_arpu,
        IFNULL(CONVERT((sum(b.native_new_banner_income)+sum(b.native_banner_income)+sum(b.banner_income))/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_banner_arpu,
        IFNULL(CONVERT(sum(b.video_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_video_arpu,
        IFNULL(CONVERT(sum(b.native_msg_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_native_msg_arpu,
        IFNULL(CONVERT(sum(b.suspend_icon_income)/sum(b.actnum),DECIMAL(10, 4)),0.0000) total_suspend_icon_arpu,

        sum(native_splash_show) native_splash_show,
        sum(splash_show) splash_show,
        sum(native_new_banner_show) + sum(native_banner_show) native_new_banner_show,
        sum(native_plaque_show) native_plaque_show,
        sum(native_new_plaque_show) + sum(native_plaque_show) native_new_plaque_show,
        sum(native_banner_show) native_banner_show,
        sum(banner_show) banner_show,
        sum(suspend_icon_show) suspend_icon_show,
        sum(system_splash_show) system_splash_show,
        sum(video_show) video_show,
        sum(plaque_video_show) plaque_video_show,
        sum(native_msg_show) native_msg_show,
        sum(plaque_show) plaque_show,

        IFNULL(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show),0) sum_total_banner,
        IFNULL(round(sum(banner_show)/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_banner,
        IFNULL(round((sum(native_new_banner_show)+sum(native_banner_show))/(sum(banner_show)+sum(native_new_banner_show)+sum(native_banner_show))*100,2),0) sum_pv_new_banner,



        IFNULL(round((sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))/sum(b.actnum), 2), 0) all_total_pv,
        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/sum(b.actnum),2), 0) all_total_click,

        IFNULL(round((sum(banner_click)+sum(plaque_click)+sum(splash_click)+sum(native_banner_click)+sum(native_plaque_click)+sum(native_splash_click)+sum(video_click)+sum(plaque_video_click)
        +sum(native_msg_click)+sum(system_splash_click)+sum(native_new_plaque_click)+sum(native_new_banner_click)+sum(suspend_icon_click))/(sum(banner_show)+sum(plaque_show)+sum(splash_show)+sum(native_banner_show)+sum(native_plaque_show)+sum(native_splash_show)+sum(video_show)+sum(plaque_video_show)
        +sum(native_msg_show)+sum(system_splash_show)+sum(native_new_plaque_show)+sum(native_new_banner_show)+sum(suspend_icon_show))*100, 2), 0) all_total_ctr,

        round((sum(banner_income)*1000/sum(banner_show)),3) banner_ecpm,
        round((sum(native_banner_income)*1000/sum(native_banner_show)),3) native_banner_ecpm,
        round((sum(plaque_video_income)*1000/sum(plaque_video_show)),3) plaque_video_ecpm,
        round(((sum(native_new_plaque_income)+sum(native_plaque_income))*1000/(sum(native_new_plaque_show)+sum(native_plaque_show))),3) native_new_plaque_ecpm,
        round((sum(native_msg_income)*1000/sum(native_msg_show)),3) native_msg_ecpm,
        round((sum(native_splash_income)*1000/sum(native_splash_show)),3) native_splash_ecpm,
        round((sum(suspend_icon_income)*1000/sum(suspend_icon_show)),3) suspend_icon_ecpm,
        round((sum(splash_income)*1000/sum(splash_show)),3) splash_ecpm,
        round((sum(system_splash_income)*1000/sum(system_splash_show)),3) system_splash_ecpm,
        round((sum(native_plaque_income)*1000/sum(native_plaque_show)),3) native_plaque_ecpm,
        round((sum(video_income)*1000/sum(video_show)),3) video_ecpm,
        round((sum(plaque_income)*1000/sum(plaque_show)),3) plaque_ecpm,
        round(((sum(native_new_banner_income)+sum(native_banner_income))*1000/(sum(native_new_banner_show)+sum(native_banner_show))),3) native_new_banner_ecpm,

        ifnull(round(sum(total_income),3),0) total_income,
        round(sum(native_plaque_income),3) native_plaque_income,
        round(sum(video_income),3) video_income,
        round(sum(native_msg_income),3) native_msg_income,
        round(sum(suspend_icon_income),3) suspend_icon_income,
        round(sum(splash_income),3) splash_income,
        round(sum(banner_income),3) banner_income,
        round(sum(plaque_video_income),3) plaque_video_income,
        round(sum(system_splash_income),3) system_splash_income,
        round(sum(native_banner_income),3) native_banner_income,
        round((sum(native_new_banner_income)+sum(native_banner_income)),3) native_new_banner_income,
        round(sum(native_splash_income),3) native_splash_income,
        round(sum(plaque_income),3) plaque_income,
        round((sum(native_new_plaque_income)+sum(native_plaque_income)),3) native_new_plaque_income,

        ifnull(round(sum(b.splash_click)/sum(b.splash_show)*100,2),0) splash_ctr,
        ifnull(round(sum(b.native_splash_click)/sum(b.native_splash_show)*100,2),0) native_splash_ctr,
        ifnull(round(sum(b.plaque_click)/sum(b.plaque_show)*100,2),0) plaque_ctr,
        ifnull(round(sum(b.native_plaque_click)/sum(b.native_plaque_show)*100,2),0) native_plaque_ctr,
        ifnull(round((sum(b.native_new_plaque_click)+sum(b.native_plaque_click))/(sum(b.native_new_plaque_show)+sum(b.native_plaque_show))*100,2),0) native_new_plaque_ctr,
        ifnull(round(sum(b.plaque_video_click)/sum(b.plaque_video_show)*100,2),0) plaque_video_ctr,
        ifnull(round(sum(b.banner_click)/sum(b.banner_show)*100,2),0) banner_ctr,
        ifnull(round(sum(b.native_banner_click)/sum(b.native_banner_show)*100,2),0) native_banner_ctr,
        ifnull(round((sum(b.native_new_banner_click)+sum(b.native_banner_click))/(sum(b.native_new_banner_show)+sum(b.native_banner_show))*100,2),0) native_new_banner_ctr,
        ifnull(round(sum(b.video_click)/sum(b.video_show)*100,2),0) video_ctr,
        ifnull(round(sum(b.native_msg_click)/sum(b.native_msg_show)*100,2),0) native_msg_ctr,
        ifnull(round(sum(b.suspend_icon_click)/sum(b.suspend_icon_show)*100,2),0) suspend_icon_ctr,

        round(sum(b.splash_income)/sum(b.splash_click),3) splash_cpc,
        round(sum(b.native_splash_income)/sum(b.native_splash_click),3) native_splash_cpc,
        round(sum(b.plaque_income)/sum(b.plaque_click),3) plaque_cpc,
        round(sum(b.native_plaque_income)/sum(b.native_plaque_click),3) native_plaque_cpc,
        round((sum(b.native_new_plaque_income)+sum(b.native_plaque_income))/(sum(b.native_new_plaque_click)+sum(b.native_plaque_click)),3) native_new_plaque_cpc,
        round(sum(b.plaque_video_income)/sum(b.plaque_video_click),3) plaque_video_cpc,
        round(sum(b.banner_income)/sum(b.banner_click),3) banner_cpc,
        round(sum(b.native_banner_income)/sum(b.native_banner_click),3) native_banner_cpc,
        round((sum(b.native_new_banner_income)+sum(b.native_banner_income))/(sum(b.native_new_banner_click)+sum(b.native_banner_click)),3) native_new_banner_cpc,
        round(sum(b.video_income)/sum(b.video_click),3) video_cpc,
        round(sum(b.native_msg_income)/sum(b.native_msg_click),3) native_msg_cpc,
        round(sum(b.suspend_icon_income)/sum(b.suspend_icon_click),3) suspend_icon_cpc,
        b.daily_duration daily_duration,

        sum(banner_request) banner_request,
        sum(native_splash_request) native_splash_request,
        sum(native_new_plaque_request) + sum(native_plaque_request) native_new_plaque_request,
        sum(native_msg_request) native_msg_request,
        sum(video_request) video_request,
        sum(splash_request) splash_request,
        sum(plaque_video_request) plaque_video_request,
        sum(system_splash_request) system_splash_request,
        sum(suspend_icon_request) suspend_icon_request,
        sum(plaque_request) plaque_request,
        sum(native_new_banner_request) + sum(native_banner_request) native_new_banner_request,

        IFNULL(round((sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))/sum(b.actnum),2), 0) all_total_request,
        round((sum(b.system_splash_request)+sum(b.native_splash_request)+sum(splash_request))/sum(b.actnum),2) total_splash_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request)+sum(b.plaque_request))/sum(b.actnum),2) total_plaque_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) total_plaque_video_request,
        round((sum(b.native_new_banner_request)+sum(b.native_banner_request)+sum(b.banner_request))/sum(b.actnum),2) total_banner_request,
        round(sum(b.video_request)/sum(b.actnum),2) total_video_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) total_native_msg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) total_suspend_icon_request,

        round(sum(b.system_splash_request)/sum(b.actnum),2) system_splash_avg_request,
        round(sum(b.splash_request)/sum(b.actnum),2) splash_avg_request,
        round(sum(b.native_splash_request)/sum(b.actnum),2) native_splash_avg_request,
        round(sum(b.plaque_request)/sum(b.actnum),2) plaque_avg_request,
        round((sum(b.native_new_plaque_request)+sum(b.native_plaque_request))/sum(b.actnum),2) native_new_plaque_avg_request,
        round(sum(b.plaque_video_request)/sum(b.actnum),2) plaque_video_avg_request,
        round(sum(b.banner_request)/sum(b.actnum),2) banner_avg_request,
        round((sum(b.native_new_banner_request) + sum(b.native_banner_request))/sum(b.actnum),2) native_new_banner_avg_request,
        round(sum(b.video_request)/sum(b.actnum),2) video_avg_request,
        round(sum(b.native_msg_request)/sum(b.actnum),2) native_msg_avg_request,
        round(sum(b.suspend_icon_request)/sum(b.actnum),2) suspend_icon_avg_request,

        sum(banner_fill) banner_fill,
        sum(native_splash_fill) native_splash_fill,
        sum(native_new_plaque_fill) + sum(native_plaque_fill) native_new_plaque_fill,
        sum(native_msg_fill) native_msg_fill,
        sum(video_fill) video_fill,
        sum(splash_fill) splash_fill,
        sum(plaque_video_fill) plaque_video_fill,
        sum(system_splash_fill) system_splash_fill,
        sum(suspend_icon_fill) suspend_icon_fill,
        sum(plaque_fill) plaque_fill,
        sum(native_new_banner_fill) + sum(native_banner_fill) native_new_banner_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/sum(b.actnum),2), 0) all_total_avg_fill,
        round((sum(b.system_splash_fill)+sum(b.native_splash_fill)+sum(splash_fill))/sum(b.actnum),2) total_splash_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill)+sum(b.plaque_fill))/sum(b.actnum),2) total_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) total_plaque_video_avg_fill,
        round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill)+sum(b.banner_fill))/sum(b.actnum),2) total_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) total_video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) total_native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) total_suspend_icon_avg_fill,

        round(sum(b.system_splash_fill)/sum(b.actnum),2) system_splash_avg_fill,
        round(sum(b.splash_fill)/sum(b.actnum),2) splash_avg_fill,
        round(sum(b.native_splash_fill)/sum(b.actnum),2) native_splash_avg_fill,
        round(sum(b.plaque_fill)/sum(b.actnum),2) plaque_avg_fill,
        round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/sum(b.actnum),2) native_new_plaque_avg_fill,
        round(sum(b.plaque_video_fill)/sum(b.actnum),2) plaque_video_avg_fill,
        round(sum(b.banner_fill)/sum(b.actnum),2) banner_avg_fill,
        round((sum(b.native_new_banner_fill) + sum(b.native_banner_fill))/sum(b.actnum),2) native_new_banner_avg_fill,
        round(sum(b.video_fill)/sum(b.actnum),2) video_avg_fill,
        round(sum(b.native_msg_fill)/sum(b.actnum),2) native_msg_avg_fill,
        round(sum(b.suspend_icon_fill)/sum(b.actnum),2) suspend_icon_avg_fill,

        IFNULL(round((sum(banner_fill)+sum(plaque_fill)+sum(splash_fill)+sum(native_banner_fill)+sum(native_plaque_fill)+sum(native_splash_fill)+sum(video_fill)+sum(plaque_video_fill)
        +sum(native_msg_fill)+sum(system_splash_fill)+sum(native_new_plaque_fill)+sum(native_new_banner_fill)+sum(suspend_icon_fill))/
        (sum(banner_request)+sum(native_splash_request)+sum(native_new_plaque_request)+sum(native_plaque_request)+sum(native_msg_request)+sum(video_request)+sum(splash_request)
        +sum(plaque_video_request)+sum(system_splash_request)+sum(suspend_icon_request)+sum(plaque_request)+sum(native_new_banner_request)+sum(native_banner_request))*100,2), 0) all_total_fill,
        IFNULL(round(sum(b.system_splash_fill)/sum(b.system_splash_request)*100,2), 0) total_system_splash_fill,
        IFNULL(round(sum(b.splash_fill)/sum(b.splash_request)*100,2), 0) total_splash_fill,
        IFNULL(round(sum(b.native_splash_fill)/sum(b.native_splash_request)*100,2), 0) total_native_splash_fill,
        IFNULL(round(sum(b.plaque_fill)/sum(b.plaque_request)*100,2), 0) total_plaque_fill,
        IFNULL(round((sum(b.native_new_plaque_fill)+sum(b.native_plaque_fill))/(sum(b.native_new_plaque_request)+sum(b.native_plaque_request))*100,2), 0) total_native_new_plaque_fill,
        IFNULL(round(sum(b.plaque_video_fill)/sum(b.plaque_video_request)*100,2), 0) total_plaque_video_fill,
        IFNULL(round(sum(b.banner_fill)/sum(b.banner_request)*100,2), 0) total_banner_fill,
        IFNULL(round((sum(b.native_new_banner_fill)+sum(b.native_banner_fill))/(sum(b.native_new_banner_request)+sum(b.native_banner_request))*100,2), 0) total_native_new_banner_fill,
        IFNULL(round(sum(b.video_fill)/sum(b.video_request)*100,2), 0) total_video_fill,
        IFNULL(round(sum(b.native_msg_fill)/sum(b.native_msg_request)*100,2), 0) total_native_msg_fill,
        IFNULL(round(sum(b.suspend_icon_fill)/sum(b.suspend_icon_request)*100,2), 0) total_suspend_icon_fill
        from
        (
        SELECT
        REPLACE(c.appkey,'_2','') appkey,c.channel,c.media,c.gameName,c.temp_id,c.temp_name,c.total_income,c.dau_arpu,c.banner_pv,c.plaque_pv,c.splash_pv,c.video_pv,
        c.banner_arpu,c.plaque_arpu,c.splash_arpu,c.video_arpu,c.banner_ecpm,c.plaque_ecpm,c.splash_ecpm,c.native_banner_ecpm,
        c.native_plaque_ecpm,c.native_splash_ecpm,c.video_ecpm,c.plaque_video_ecpm,c.banner_show,c.plaque_show,c.splash_show,
        c.native_banner_show,c.native_plaque_show,c.native_splash_show,c.video_show,c.plaque_video_show,c.banner_income,c.plaque_income,
        c.splash_income,c.native_banner_income,c.native_plaque_income,c.native_splash_income,c.video_income,c.plaque_video_income,c.msg_pv,
        c.msg_arpu,c.native_msg_ecpm,c.native_msg_show,c.native_msg_income,c.plaque_video_pv,c.plaque_video_arpu,c.avgnum,c.banner_click,
        c.plaque_click,c.splash_click,c.native_banner_click,c.native_plaque_click,c.native_splash_click,c.video_click,c.plaque_video_click,
        c.system_splash_show,c.native_new_plaque_show,c.native_new_banner_show,c.suspend_icon_show,c.system_splash_pv,c.native_new_plaque_pv,
        c.native_new_banner_pv,c.suspend_icon_pv,c.system_splash_arpu,c.native_new_plaque_arpu,c.native_new_banner_arpu,c.suspend_icon_arpu,
        c.system_splash_ecpm,c.native_new_plaque_ecpm,c.native_new_banner_ecpm,c.suspend_icon_ecpm,c.system_splash_income,c.native_new_plaque_income,
        c.native_new_banner_income,c.suspend_icon_income,c.system_splash_click,c.native_new_plaque_click,c.native_new_banner_click,c.suspend_icon_click,
        c.native_plaque_pv,c.native_banner_pv,c.native_msg_click,c.source,c.active_temp_id, c.active_temp_name,c.banner_request,c.plaque_request,
        c.splash_request,c.video_request,c.native_banner_request,c.native_plaque_request,c.native_splash_request,
        c.plaque_video_request,c.native_msg_request,c.system_splash_request,c.native_new_plaque_request,
        c.native_new_banner_request,c.suspend_icon_request,c.banner_fill,c.plaque_fill,c.splash_fill,c.video_fill,
        c.native_banner_fill,c.native_plaque_fill,c.native_splash_fill,c.plaque_video_fill,c.native_msg_fill,
        c.system_splash_fill,c.native_new_plaque_fill,c.native_new_banner_fill,c.suspend_icon_fill,
        c.country,

        <choose>
            <when test="dataSource != null and dataSource == 3">
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.activate_add
                ELSE f.new_users
                END addnum,
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.active_user
                ELSE f.act_users
                END actnum,
                CASE
                WHEN c.channel IN ('h5_oppo', 'h5_vivo') THEN g.active_time * g.active_user
                ELSE TIME_TO_SEC(d.duration) * d.act_num
                END AS t_duration,
            </when>
            <otherwise>
                sum(d.act_users) actnum,sum(d.new_users) addnum,
                cast(avg(second_avg) as int) daily_duration,
                cast(avg(single_avg) as int) t_duration,
            </otherwise>
        </choose>
        aa.id appid,aa.app_name appname,
        <choose>
            <when test="custom_date != null and custom_date != ''">
                concat(#{start_date},'至',#{end_date}) as tdate
            </when>
            <when test="group != null and group != '' and group.contains('tdate')">
                c.tdate
            </when>
            <when test="group != null and group != '' and group.contains('week')">
                DATE_FORMAT(c.tdate, '%x-%v') as tdate,
                DATE_FORMAT(c.tdate, '%x-%v') as week
            </when>
            <when test="group != null and group != '' and group.contains('month')">
                DATE_FORMAT(c.tdate,'%Y-%m') as tdate,
                DATE_FORMAT(c.tdate,'%Y-%m') as `month`
            </when>
            <when test="group != null and group != '' and group.contains('beek')">
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS tdate,
                CONCAT(DATE_FORMAT(c.tdate, '%x'),"-", LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 1,INTEGER),2,"0"),"-",LPAD(CONVERT(FLOOR((DATE_FORMAT(c.tdate, '%v') - 1) / 2) * 2 + 2,INTEGER),2,"0"))  AS beek
            </when>
            <otherwise>
                concat(#{start_date},'至',#{end_date}) as tdate
            </otherwise>
        </choose>
        FROM ${tableName} c
        left join dnwx_bi.app_info aa on c.appid = aa.id
        LEFT JOIN ads_dim_users_info_5d_country_hourly_oversea d
        ON c.tdate=d.t_date AND c.appid=d.appid AND c.channel=d.download_channel AND c.country=d.country_code

        left join
        (select a_day, appid, install_channel,country b_country, sum(act_num) act_num, sum(add_num) add_num, sum(start_num) start_num,
        cast(avg(second_avg) as int) second_avg, cast(avg(single_avg) as int) single_avg from ads_umeng_custom_total_daily_oversea
        where a_day between #{start_date} and #{end_date} GROUP BY a_day, appid, install_channel,country)
        bb on c.tdate = bb.a_day and c.appid = bb.appid and c.channel = bb.install_channel and c.country = bb.b_country
        <if test="dataSource != null and dataSource == 3">
            LEFT JOIN dnwx_bi.ads_dim_users_info_3d_hourly f
            ON c.tdate=f.tdate
            AND c.appid = f.appid
            AND c.channel = f.download_channel
            LEFT JOIN (
            SELECT tdate,appid,provider,sum(activate_add) AS activate_add,sum(active_user) AS active_user,round(sum(active_time)/sum(active_user),0) AS active_time
            FROM dnwx_bi.ads_wechat_add_active_daily WHERE provider IN ('oppo','vivo') GROUP BY tdate,appid,provider) g
            ON c.tdate = g.tdate
            AND c.appid = g.appid
            AND c.channel = concat('h5_',g.provider)
        </if>
        WHERE c.tdate <![CDATA[>=]]> #{start_date} and c.tdate <![CDATA[<=]]> #{end_date}
        and concat(c.appid, c.channel) in
        (SELECT con_ac FROM dnwx_bi.ads_appid_channel_info
        <where>
            <if test="state != null and state != ''">
                state in (${state})
            </if>
        </where>)
        <if test="appid != null and appid != '' ">
            and c.appid in (${appid})
        </if>
        <if test="channel != null and channel != '' ">
            and c.channel in (${channel})
        </if>
        <if test="media != null and media != '' ">
            and c.media in (${media})
        </if>
        <if test="gameName != null and gameName != '' ">
            and c.gameName in (${gameName})
        </if>
        <if test="temp_id != null and temp_id != '' ">
            and c.temp_id like concat('%',#{temp_id},'%')
        </if>
        <if test="temp_name != null and temp_name != '' ">
            and c.temp_name like concat('%',#{temp_name},'%')
        </if>
        <if test="active_temp_id != null and active_temp_id != '' ">
            and c.active_temp_id like concat('%',#{active_temp_id},'%')
        </if>
        <if test="active_temp_name != null and active_temp_name != '' ">
            and c.active_temp_name like concat('%',#{active_temp_name},'%')
        </if>
        <if test="match_str != null and match_str != ''">
            and ${match_str}
        </if>
        <if test="source != null and source != ''">
            and c.source = #{source}
        </if>
        <if test="country != null and country != '' ">
            and c.country in (${country})
        </if>
        <if test="appid_tag != null and appid_tag != ''">
            <choose>
                <when test="appid_tag_rev != null and appid_tag != ''">
                    AND CONCAT(c.appid,'#',c.channel) not in (${appid_tag})
                </when>
                <otherwise>
                    AND CONCAT(c.appid,'#',c.channel) in (${appid_tag})
                </otherwise>
            </choose>
        </if>
        group by c.tdate,c.appid,c.channel,c.country
        ) b
        <if test="group != null and group != '' ">
            group by ${group}
        </if>
        <choose>
            <when test="order_str != null and order_str != ''">
                order by ${order_str}
            </when>
            <otherwise>
                order by b.tdate asc,b.addnum+0 desc
            </otherwise>
        </choose>
    </sql>

</mapper>