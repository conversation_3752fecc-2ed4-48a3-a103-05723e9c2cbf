package com.wbgame.pojo.nnjy;

public class NnjyToolRemainStepVo {
	
	private String tdate; // 日期
	private int appid; // 产品ID
	private int pid; // 项目ID
	private int lv; // 关卡id
	private int tool; // 道具ID
	private int step0; // 剩余步数0的人数
	private int step1;
	private int step2;
	private int step3;
	private int step4;
	private int step5;
	private int step6; // 剩余步数大于5的人数
	
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public int getAppid() {
		return appid;
	}
	public void setAppid(int appid) {
		this.appid = appid;
	}
	public int getPid() {
		return pid;
	}
	public void setPid(int pid) {
		this.pid = pid;
	}
	public int getLv() {
		return lv;
	}
	public void setLv(int lv) {
		this.lv = lv;
	}
	public int getTool() {
		return tool;
	}
	public void setTool(int tool) {
		this.tool = tool;
	}
	public int getStep0() {
		return step0;
	}
	public void setStep0(int step0) {
		this.step0 = step0;
	}
	public int getStep1() {
		return step1;
	}
	public void setStep1(int step1) {
		this.step1 = step1;
	}
	public int getStep2() {
		return step2;
	}
	public void setStep2(int step2) {
		this.step2 = step2;
	}
	public int getStep3() {
		return step3;
	}
	public void setStep3(int step3) {
		this.step3 = step3;
	}
	public int getStep4() {
		return step4;
	}
	public void setStep4(int step4) {
		this.step4 = step4;
	}
	public int getStep5() {
		return step5;
	}
	public void setStep5(int step5) {
		this.step5 = step5;
	}
	public int getStep6() {
		return step6;
	}
	public void setStep6(int step6) {
		this.step6 = step6;
	}
	
}
