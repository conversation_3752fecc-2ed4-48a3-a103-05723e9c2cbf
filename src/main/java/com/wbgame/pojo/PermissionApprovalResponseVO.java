package com.wbgame.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 权限申请响应VO
  <AUTHOR>
 */
@Data
@ApiModel("权限申请响应")
public class PermissionApprovalResponseVO {

    @ApiModelProperty("申请人用户名")
    private String applicantUsername;

    @ApiModelProperty("申请类型：1-界面权限，2-应用权限")
    private Integer applicationType;

    @ApiModelProperty("申请内容标识")
    private String applicationMark;

    @ApiModelProperty("申请内容详情")
    private String applicationDetail;

    @ApiModelProperty("申请人昵称")
    private String applicantNickName;

    @ApiModelProperty("申请人部门名称")
    private String applicantDepartment;

    @ApiModelProperty("申请人部门名称")
    private String applicantDepartmentName;

    @ApiModelProperty("审批人列表")
    private List<ApproverInfoVO> approvers;

    @ApiModelProperty("审批流程说明")
    private String approvalProcess;

    @ApiModelProperty("申请记录ID")
    private Integer applicationId;

    @ApiModelProperty(value = "凭证")
    private String token;


}
