package com.wbgame.pojo.jettison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description oppo查询模板
 * <AUTHOR>
 * @Date 2024/10/29 18:04
 */
@Data
@ApiModel("oppo查询模板")
public class OppoBatchTemplateVo {

    @ApiModelProperty(value = "模板id")
    private Long temp_id;

    @ApiModelProperty(value = "模板名称")
    private String temp_name;

    @ApiModelProperty(value = "推广流量")
    private String extension_flow;

    @ApiModelProperty(value = "流量场景")
    private String flow_scene;

    @ApiModelProperty(value = "创意规格")
    private String global_spec_names;

    @ApiModelProperty(value = "创建人")
    private String owner_user;

    @ApiModelProperty(value = "更新时间")
    private String update_time;

    @ApiModelProperty(value = "账户")
    private String accounts;

    @ApiModelProperty(value = "OPPO应用id")
    private Integer oppo_appid;

}
