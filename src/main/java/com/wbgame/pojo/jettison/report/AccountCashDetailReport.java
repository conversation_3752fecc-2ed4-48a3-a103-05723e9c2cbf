package com.wbgame.pojo.jettison.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/17
 * @description 账号转账记录实体类
 **/
@Data
@ApiModel(value = "账号转账记录")
public class AccountCashDetailReport {

    @ApiModelProperty(value = "日期")
    private String tdate;

    @ApiModelProperty(value = "账号ID")
    private String account_id;
    @ApiModelProperty(value = "账号名称")
    private String accountName;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "优化师")
    private String putUser;
    @ApiModelProperty(value = "投放主体")
    private String accountSubject;

    @ApiModelProperty(value = "转账订单id")
    private String transfer_id;

    @ApiModelProperty(value = "转账金额")
    private Double transfer_amount;
    @ApiModelProperty(value = "转账次数")
    private Double transfer_count;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "转账时间")
    private String transfer_time;

    @ApiModelProperty(value = "账户类型 CASH 现金 GIFT 赠送 REBATE 返利")
    private String account_type;

    @ApiModelProperty(value = "转账类型 TRANSFER_IN 转入 TRANSFER_OUT 转出")
    private String transfer_type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "对方企业ID")
    private String peer_account_id;

    @ApiModelProperty(value = "对方企业名称")
    private String peer_account_name;

    @ApiModelProperty(value = "业务类型")
    private String busType;

    @ApiModelProperty(value = "返利金有效期")
    private String expireTime;
}
