package com.wbgame.pojo.jettison.report.dto;

/**
 * <AUTHOR>
 * @Description 新增对比报表对象
 * @Create 2023-03-07
 */
public class AddusersReportDTO extends BaseReportDTO{

    private String day;

    private String ad_platform;

    private String channelId;

    private String channel;

    private Long um_add_num;

    private Long ry_add_num;

    private Long add_num;

    private String app;//导出用
    private String appCategory;//导出用
    private String appId;//导出用
    private String account; //账户
    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getAd_platform() {
        return ad_platform;
    }

    public void setAd_platform(String ad_platform) {
        this.ad_platform = ad_platform;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getUm_add_num() {
        return um_add_num;
    }

    public void setUm_add_num(Long um_add_num) {
        this.um_add_num = um_add_num;
    }

    public Long getRy_add_num() {
        return ry_add_num;
    }

    public void setRy_add_num(Long ry_add_num) {
        this.ry_add_num = ry_add_num;
    }

    public Long getAdd_num() {
        return add_num;
    }

    public void setAdd_num(Long add_num) {
        this.add_num = add_num;
    }

    @Override
    public String getApp() {
        return app;
    }

    @Override
    public void setApp(String app) {
        this.app = app;
    }

    @Override
    public String getAppCategory() {
        return appCategory;
    }

    @Override
    public void setAppCategory(String appCategory) {
        this.appCategory = appCategory;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getAccount() {
        return account;
    }

    @Override
    public void setAccount(String account) {
        this.account = account;
    }
}
