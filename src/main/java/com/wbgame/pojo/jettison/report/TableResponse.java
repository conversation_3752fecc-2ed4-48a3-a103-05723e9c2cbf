package com.wbgame.pojo.jettison.report;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/5
 * @description
 **/
@Data
public class TableResponse<T> {

    private int ret;
    private String message;
    private long totalCount;
    private T total;
    private List<T> data;

    public static <T> TableResponse<T> success(List<T> data, T total, long totalCount) {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(1);
        response.setMessage("success");
        response.setData(data);
        response.setTotal(total);
        response.setTotalCount(totalCount);
        return response;
    }
    public static <T> TableResponse<T> success(List<T> data, T total) {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(1);
        response.setMessage("success");
        response.setData(data);
        response.setTotal(total);
        return response;
    }

    public static <T> TableResponse<T> success(List<T> data, int totalCount) {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(1);
        response.setMessage("success");
        response.setTotalCount(totalCount);
        response.setData(data);
        return response;
    }

    public static <T> TableResponse<T> success(List<T> data) {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(1);
        response.setMessage("success");
        response.setData(data);
        return response;
    }

    public static <T> TableResponse<T> success() {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(1);
        response.setMessage("success");
        return response;
    }

    public static <T> TableResponse<T> fail(String message) {
        TableResponse<T> response = new TableResponse<>();
        response.setRet(0);
        response.setMessage(message);
        return response;
    }
}
