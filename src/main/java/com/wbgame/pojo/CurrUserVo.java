package com.wbgame.pojo;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * 登录用户基本信息
 * <AUTHOR>
 *
 */
public class CurrUserVo {
	
	/****
	 * 由于合作方后台登录会调用后台登录接口，
	 * 所以该用户VO改动，需要同步调整合作方的VO
	 * 
	 */
	
	private String login_name; // 登录名
	private String password; // 密码
	private String nick_name; // 昵称
	private String org_id; // 所属组ID
	private String org_name; // 组名称
	private String role_id; // 角色ID
	private String role_name; // 角色名称
	private String company; // 公司
	private String menu_group;
	private String token;
	private String app_group_b; // 移动运营后台
	private String app_group_c; // 广告统计后台
	
	private String platform_id; // 平台ID
	private String client_ip; // 用户访问ip
	private String sys; //系统标识
	private String user_name;
	private String level;
	private String off;
	private String mach_code; // 机器码
	private String email; // 邮件地址
	private String department; // 部门中文
	private String euser; // 最后修改人
	private String endtime; // 最后修改时间

	
	/** 隐藏对应菜单分类 */
	private String hidden_menu_list;
	/** 无视分类及菜单目录的页面index */
	private String page_list;
	/** 用于权限列表排序 */
	private int orderTime;
	
	private String little_group; // v3使用，用户小游戏应用列表
	private String app_group; // v3使用，用户App应用列表
	private String cha_group; // v3使用，用户子渠道列表
	private String groupapp; // v3使用，用户的应用组
	private String cha_media; // v3使用，用户的渠道媒体列表
	private String cha_type; // v3使用，用户的渠道类型列表
	private String account_group; // v3使用，账号分组列表
	private String app_category; // 应用分类列表
	private List<JSONObject> customArray; // v3使用，用户的角色自定义参数列表
	
	
	public String getLogin_name() {
		return login_name;
	}
	public void setLogin_name(String login_name) {
		this.login_name = login_name;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getNick_name() {
		return nick_name;
	}
	public void setNick_name(String nick_name) {
		this.nick_name = nick_name;
	}
	public String getOrg_id() {
		return org_id;
	}
	public void setOrg_id(String org_id) {
		this.org_id = org_id;
	}
	public String getOrg_name() {
		return org_name;
	}
	public void setOrg_name(String org_name) {
		this.org_name = org_name;
	}
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getRole_name() {
		return role_name;
	}
	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getMenu_group() {
		return menu_group;
	}
	public void setMenu_group(String menu_group) {
		this.menu_group = menu_group;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getApp_group_b() {
		return app_group_b;
	}
	public void setApp_group_b(String app_group_b) {
		this.app_group_b = app_group_b;
	}
	public String getApp_group_c() {
		return app_group_c;
	}
	public void setApp_group_c(String app_group_c) {
		this.app_group_c = app_group_c;
	}
	public String getPlatform_id() {
		return platform_id;
	}
	public void setPlatform_id(String platform_id) {
		this.platform_id = platform_id;
	}
	public String getClient_ip() {
		return client_ip;
	}
	public void setClient_ip(String client_ip) {
		this.client_ip = client_ip;
	}
	public String getSys() {
		return sys;
	}
	public void setSys(String sys) {
		this.sys = sys;
	}
	public String getUser_name() {
		return user_name;
	}
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}
	public String getLevel() {
		return level;
	}
	public void setLevel(String level) {
		this.level = level;
	}
	public String getOff() {
		return off;
	}
	public void setOff(String off) {
		this.off = off;
	}
	public String getHidden_menu_list() {
		return hidden_menu_list;
	}
	public void setHidden_menu_list(String hidden_menu_list) {
		this.hidden_menu_list = hidden_menu_list;
	}
	public String getPage_list() {
		return page_list;
	}
	public void setPage_list(String page_list) {
		this.page_list = page_list;
	}
	public int getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(int orderTime) {
		this.orderTime = orderTime;
	}
	public String getMach_code() {
		return mach_code;
	}
	public void setMach_code(String mach_code) {
		this.mach_code = mach_code;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public String getApp_group() {
		return app_group;
	}
	public void setApp_group(String app_group) {
		this.app_group = app_group;
	}
	public String getCha_group() {
		return cha_group;
	}
	public void setCha_group(String cha_group) {
		this.cha_group = cha_group;
	}
	public String getGroupapp() {
		return groupapp;
	}
	public void setGroupapp(String groupapp) {
		this.groupapp = groupapp;
	}
	public String getLittle_group() {
		return little_group;
	}
	public void setLittle_group(String little_group) {
		this.little_group = little_group;
	}
	public String getCha_media() {
		return cha_media;
	}
	public void setCha_media(String cha_media) {
		this.cha_media = cha_media;
	}
	public List<JSONObject> getCustomArray() {
		return customArray;
	}
	public void setCustomArray(List<JSONObject> customArray) {
		this.customArray = customArray;
	}
	public String getCha_type() {
		return cha_type;
	}
	public void setCha_type(String cha_type) {
		this.cha_type = cha_type;
	}
	public String getAccount_group() {
		return account_group;
	}
	public void setAccount_group(String account_group) {
		this.account_group = account_group;
	}
	public String getApp_category() {
		return app_category;
	}
	public void setApp_category(String app_category) {
		this.app_category = app_category;
	}
	public String getEuser() {
		return euser;
	}
	public void setEuser(String euser) {
		this.euser = euser;
	}
	public String getEndtime() {
		return endtime;
	}
	public void setEndtime(String endtime) {
		this.endtime = endtime;
	}
	
}
