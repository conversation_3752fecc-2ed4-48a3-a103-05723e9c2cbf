package com.wbgame.pojo.custom;

/**
 * @description: 广告互推实时统计
 * @author: huangmb
 * @date: 2020/11/17
 **/
public class AdvertInferHourVo {
    private int hour00;
    private int hour01;
    private int hour02;
    private int hour03;
    private int hour04;
    private int hour05;
    private int hour06;
    private int hour07;
    private int hour08;
    private int hour09;
    private int hour10;
    private int hour11;
    private int hour12;
    private int hour13;
    private int hour14;
    private int hour15;
    private int hour16;
    private int hour17;
    private int hour18;
    private int hour19;
    private int hour20;
    private int hour21;
    private int hour22;
    private int hour23;
    /**
     * 当前日期
     */
    private String tdate;
    /**
     * 广告id
     */
    private String sid;
    /**
     * 汇总
     */
    private int total;

    public int getHour00() {
        return hour00;
    }

    public void setHour00(int hour00) {
        this.hour00 = hour00;
    }

    public int getHour01() {
        return hour01;
    }

    public void setHour01(int hour01) {
        this.hour01 = hour01;
    }

    public int getHour02() {
        return hour02;
    }

    public void setHour02(int hour02) {
        this.hour02 = hour02;
    }

    public int getHour03() {
        return hour03;
    }

    public void setHour03(int hour03) {
        this.hour03 = hour03;
    }

    public int getHour04() {
        return hour04;
    }

    public void setHour04(int hour04) {
        this.hour04 = hour04;
    }

    public int getHour05() {
        return hour05;
    }

    public void setHour05(int hour05) {
        this.hour05 = hour05;
    }

    public int getHour06() {
        return hour06;
    }

    public void setHour06(int hour06) {
        this.hour06 = hour06;
    }

    public int getHour07() {
        return hour07;
    }

    public void setHour07(int hour07) {
        this.hour07 = hour07;
    }

    public int getHour08() {
        return hour08;
    }

    public void setHour08(int hour08) {
        this.hour08 = hour08;
    }

    public int getHour09() {
        return hour09;
    }

    public void setHour09(int hour09) {
        this.hour09 = hour09;
    }

    public int getHour10() {
        return hour10;
    }

    public void setHour10(int hour10) {
        this.hour10 = hour10;
    }

    public int getHour11() {
        return hour11;
    }

    public void setHour11(int hour11) {
        this.hour11 = hour11;
    }

    public int getHour12() {
        return hour12;
    }

    public void setHour12(int hour12) {
        this.hour12 = hour12;
    }

    public int getHour13() {
        return hour13;
    }

    public void setHour13(int hour13) {
        this.hour13 = hour13;
    }

    public int getHour14() {
        return hour14;
    }

    public void setHour14(int hour14) {
        this.hour14 = hour14;
    }

    public int getHour15() {
        return hour15;
    }

    public void setHour15(int hour15) {
        this.hour15 = hour15;
    }

    public int getHour16() {
        return hour16;
    }

    public void setHour16(int hour16) {
        this.hour16 = hour16;
    }

    public int getHour17() {
        return hour17;
    }

    public void setHour17(int hour17) {
        this.hour17 = hour17;
    }

    public int getHour18() {
        return hour18;
    }

    public void setHour18(int hour18) {
        this.hour18 = hour18;
    }

    public int getHour19() {
        return hour19;
    }

    public void setHour19(int hour19) {
        this.hour19 = hour19;
    }

    public int getHour20() {
        return hour20;
    }

    public void setHour20(int hour20) {
        this.hour20 = hour20;
    }

    public int getHour21() {
        return hour21;
    }

    public void setHour21(int hour21) {
        this.hour21 = hour21;
    }

    public int getHour22() {
        return hour22;
    }

    public void setHour22(int hour22) {
        this.hour22 = hour22;
    }

    public int getHour23() {
        return hour23;
    }

    public void setHour23(int hour23) {
        this.hour23 = hour23;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
