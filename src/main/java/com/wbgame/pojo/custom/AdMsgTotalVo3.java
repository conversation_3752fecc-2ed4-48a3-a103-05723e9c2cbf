package com.wbgame.pojo.custom;

public class AdMsgTotalVo3 {
	
	private String tdate;
	private String appid; // 应用ID
	private String app_version;
	private String install_channel;
	private String type; // 广告类型
	
	private Long load_num; // 触发加载
	private Integer loaded_num;//加载成功
	private Integer loadfail_num; // 加载失败
	private Integer show_num;//触发展示
	private Integer showed_num;//展示成功
	private Integer click_num; // 点击数
	private Integer unique_num; // 独立数
	private Integer act_num; // 活跃数
	
	private String click_rate; // 点击率
	private String show_rate; // 	展示数
	private String unique_rate; // 覆盖率
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getApp_version() {
		return app_version;
	}
	public void setApp_version(String app_version) {
		this.app_version = app_version;
	}
	public String getInstall_channel() {
		return install_channel;
	}
	public void setInstall_channel(String install_channel) {
		this.install_channel = install_channel;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Long getLoad_num() {
		return load_num;
	}
	public void setLoad_num(Long load_num) {
		this.load_num = load_num;
	}
	public Integer getLoaded_num() {
		return loaded_num;
	}
	public void setLoaded_num(Integer loaded_num) {
		this.loaded_num = loaded_num;
	}
	public Integer getLoadfail_num() {
		return loadfail_num;
	}
	public void setLoadfail_num(Integer loadfail_num) {
		this.loadfail_num = loadfail_num;
	}
	public Integer getShow_num() {
		return show_num;
	}
	public void setShow_num(Integer show_num) {
		this.show_num = show_num;
	}
	public Integer getShowed_num() {
		return showed_num;
	}
	public void setShowed_num(Integer showed_num) {
		this.showed_num = showed_num;
	}
	public Integer getClick_num() {
		return click_num;
	}
	public void setClick_num(Integer click_num) {
		this.click_num = click_num;
	}
	public Integer getUnique_num() {
		return unique_num;
	}
	public void setUnique_num(Integer unique_num) {
		this.unique_num = unique_num;
	}
	public String getClick_rate() {
		return click_rate;
	}
	public void setClick_rate(String click_rate) {
		this.click_rate = click_rate;
	}
	public String getShow_rate() {
		return show_rate;
	}
	public void setShow_rate(String show_rate) {
		this.show_rate = show_rate;
	}
	public String getUnique_rate() {
		return unique_rate;
	}
	public void setUnique_rate(String unique_rate) {
		this.unique_rate = unique_rate;
	}
	public Integer getAct_num() {
		return act_num;
	}
	public void setAct_num(Integer act_num) {
		this.act_num = act_num;
	}
}
