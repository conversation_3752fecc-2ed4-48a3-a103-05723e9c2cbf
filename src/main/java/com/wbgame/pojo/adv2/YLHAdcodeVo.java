package com.wbgame.pojo.adv2;

import org.omg.CORBA.PRIVATE_MEMBER;

/**
 * <AUTHOR>
 * @Classname YLHAdcodeVo
 * @Description TODO
 * @Date 2021/11/23 17:52
 */
public class YLHAdcodeVo {


    private String appid;                   //动能产品id
    private String channel;                 //子渠道
    private String adExtensionName;             //广告扩展名
    private String remark;                  //备注
    private String sdk_ad_type;             //广告源sdk广告类型
    private String open_type;               //广告使用类型
    private String createUser;              //创建人
    private String createTime;              //创建时间
    private String modifyUser;              //最后操作人
    private String modifyTime;              //最后操作事件
    private String placement_id;            //广告位id
    private String strategy;                //策略

    private String member_id;               //开发者账号
    private String app_id;                  //媒体id 根据创建接口返回的app_id指定需要在哪个媒体下创建广告位
    private String placement_name;          //广告位名字 最大60个字符（30个汉字或者60个英文字符）；同一个媒体下广告位名字不能重复
    private String scene;                   //广告场景
                                            // 1 FLOW 信息流广告 2 FOCUS_POINT 详情页插入广告
                                            //3 FLASH 开屏广告 4 VIDEO_PASTE 视频贴片广告 5 BANNER 横幅广告
                                            //6 REWARDED_VIDEO 激励广告 7 INSERTION  插屏广告

    private String rewarded_video_scene;    //激励场景 仅场景scene为激励广告时使用
                                            // 1OTHER 其他 2 LOGIN_REWARD	登陆奖励 3 TASK_REWARD 任务奖励
                                            //4 PASS_REWARD	过关奖励 5 CONTINUE_PLAY_REWARD 续玩奖励 6 DOUBLE_REWARD 翻倍奖励
                                            //7 WAITING	等待时间 8CLUES	线索提示 9 APP_SHOPPING	应用商店购物 10 LUCKY_TABLE 幸运转盘
                                            //10 APP_OPEN_AGAIN	再次打开应用

    private String rewarded_video_description;//激励广告场景点为其他时的描述 仅场景scene为激励广告时使用 最大长度200

    private String ad_pull_mode;             //接入方式 SDK JS API RTB
    private String render_type;              //渲染方式 NORMAL 自渲染 TEMPLATE 模板渲染 非必填，场景为激励广告和开屏广告时可以不填
    private String ad_crt_type_list;         //素材类型 非必填，模板渲染时，必填，模板渲染时，当素材仅支持一种素材类型时，默认为该类型，选择的素材类型必须要和素材支持的类型匹配
    private String ad_crt_template_type;     //模板广告样式 非必填，模板渲染时，当场景非激励广告和开屏广告时，模板渲染必填；模板2.0的部分场景支持多个模板类型,模板2.0生成规则模板版本生成规则;不支持自定义模板
    private String ad_crt_normal_type;       //自渲染广告样式 非必填，场景非激励广告和开屏时，自渲染必填
    private String ad_crt_normal_types;       //自渲染广告样式 非必填，场景非激励广告和开屏时，自渲染必填 替换原有的 ad_crt_normal_type 字段
    private String flash_crt_type;           //开屏广告样式  FLASH_ERECT 竖图 FLASH_JOINT 接受横版拼接的竖图(下线) FLASH 开屏 FLASH_V_PLUS 开屏V+
    private String rewarded_video_crt_type;  //激励广告广告样式  ALL_DIRECTION 激励浏览+激励视频 IMAGE 激励浏览 VIDEO 激励视频
    private String ad_feedback_element;      //广告返回元素 非必填，原生自渲染可填，不填默认全选

    private String need_server_verify;       //是否开启服务端校验，仅适用于接入方式为sdk的激励广告场景
                                             //NeedServerVerify 开启服务器端校验 NotNeedServerVerify 不开启服务器端校验
    private String transfer_url;             //激励广告服务端校验回调地址 非必填，当设置need_server_verify为开启验证时该字段必填

    private String secret;                  //激励广告服务端校验密钥  32位随机大小写字母和1-9数字组成
                                            // 非必填，当设置need_server_verify为开启验证时该字段必填

    private String price_strategy_type;     //广告位价格策略 非必填，需联系运营经理开通白名单权限
                                            //Default 默认底价 TargetPrice 目标价 CustomBasePrice 自定义底价 BiddingPrice 实时竞价
    private String real_time_bidding_type;  //实时竞价类型  Server_Bidding 服务端竞价 Client_Bidding 客户端竞价

    private String ecpm_price;              //广告位价格 单位：分 非必填，当设置价格策略时该字段必填
    private String placement_test_status;   //广告位测试状态 非必填，默认为Formal
                                            //Test 测试广告位 Formal 	正式广告位

    private String is_open_rewarded;        //是否开启激励 Close Open 默认为Close

    private String template_type;        //1游戏 2工具
    
    private String params;        //自定义参数

    private String create_record;   //创建记录

    public String getParams() {
		return params;
	}

	public void setParams(String params) {
		this.params = params;
	}

	public String getAd_crt_normal_types() {
        return ad_crt_normal_types;
    }

    public void setAd_crt_normal_types(String ad_crt_normal_types) {
        this.ad_crt_normal_types = ad_crt_normal_types;
    }

    public String getTemplate_type() {
        return template_type;
    }

    public void setTemplate_type(String template_type) {
        this.template_type = template_type;
    }

    public String getReal_time_bidding_type() {
        return real_time_bidding_type;
    }

    public void setReal_time_bidding_type(String real_time_bidding_type) {
        this.real_time_bidding_type = real_time_bidding_type;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getIs_open_rewarded() {
        return is_open_rewarded;
    }

    public void setIs_open_rewarded(String is_open_rewarded) {
        this.is_open_rewarded = is_open_rewarded;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getOpen_type() {
        return open_type;
    }

    public void setOpen_type(String open_type) {
        this.open_type = open_type;
    }

    public String getSdk_ad_type() {
        return sdk_ad_type;
    }

    public void setSdk_ad_type(String sdk_ad_type) {
        this.sdk_ad_type = sdk_ad_type;
    }

    public String getAdExtensionName() {
        return adExtensionName;
    }

    public void setAdExtensionName(String adExtensionName) {
        this.adExtensionName = adExtensionName;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlacement_id() {
        return placement_id;
    }

    public void setPlacement_id(String placement_id) {
        this.placement_id = placement_id;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getMember_id() {
        return member_id;
    }

    public void setMember_id(String member_id) {
        this.member_id = member_id;
    }

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

    public String getPlacement_name() {
        return placement_name;
    }

    public void setPlacement_name(String placement_name) {
        this.placement_name = placement_name;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getRewarded_video_scene() {
        return rewarded_video_scene;
    }

    public void setRewarded_video_scene(String rewarded_video_scene) {
        this.rewarded_video_scene = rewarded_video_scene;
    }

    public String getRewarded_video_description() {
        return rewarded_video_description;
    }

    public void setRewarded_video_description(String rewarded_video_description) {
        this.rewarded_video_description = rewarded_video_description;
    }

    public String getAd_pull_mode() {
        return ad_pull_mode;
    }

    public void setAd_pull_mode(String ad_pull_mode) {
        this.ad_pull_mode = ad_pull_mode;
    }

    public String getRender_type() {
        return render_type;
    }

    public void setRender_type(String render_type) {
        this.render_type = render_type;
    }

    public String getAd_crt_type_list() {
        return ad_crt_type_list;
    }

    public void setAd_crt_type_list(String ad_crt_type_list) {
        this.ad_crt_type_list = ad_crt_type_list;
    }

    public String getAd_crt_template_type() {
        return ad_crt_template_type;
    }

    public void setAd_crt_template_type(String ad_crt_template_type) {
        this.ad_crt_template_type = ad_crt_template_type;
    }

    public String getAd_crt_normal_type() {
        return ad_crt_normal_type;
    }

    public void setAd_crt_normal_type(String ad_crt_normal_type) {
        this.ad_crt_normal_type = ad_crt_normal_type;
    }

    public String getFlash_crt_type() {
        return flash_crt_type;
    }

    public void setFlash_crt_type(String flash_crt_type) {
        this.flash_crt_type = flash_crt_type;
    }

    public String getRewarded_video_crt_type() {
        return rewarded_video_crt_type;
    }

    public void setRewarded_video_crt_type(String rewarded_video_crt_type) {
        this.rewarded_video_crt_type = rewarded_video_crt_type;
    }

    public String getAd_feedback_element() {
        return ad_feedback_element;
    }

    public void setAd_feedback_element(String ad_feedback_element) {
        this.ad_feedback_element = ad_feedback_element;
    }

    public String getNeed_server_verify() {
        return need_server_verify;
    }

    public void setNeed_server_verify(String need_server_verify) {
        this.need_server_verify = need_server_verify;
    }

    public String getTransfer_url() {
        return transfer_url;
    }

    public void setTransfer_url(String transfer_url) {
        this.transfer_url = transfer_url;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getPrice_strategy_type() {
        return price_strategy_type;
    }

    public void setPrice_strategy_type(String price_strategy_type) {
        this.price_strategy_type = price_strategy_type;
    }

    public String getEcpm_price() {
        return ecpm_price;
    }

    public void setEcpm_price(String ecpm_price) {
        this.ecpm_price = ecpm_price;
    }

    public String getPlacement_test_status() {
        return placement_test_status;
    }

    public void setPlacement_test_status(String placement_test_status) {
        this.placement_test_status = placement_test_status;
    }

    public String getCreate_record() {
        return create_record;
    }

    public void setCreate_record(String create_record) {
        this.create_record = create_record;
    }
}
