package com.wbgame.pojo.adv2.reportEntity;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 描述
 * @Create 2020-08-11 14:33
 */
@Data
public class ChinaMonetizationReport {

    private String app;

    private String appId;

    private String appGroup;//应用组

    private String appCategory;//应用分类

    private String country;

    private String account; //账户

    private String accountRemark;//账号备注

    private String day;

    private String cha_id;

    private String cha_type_name;

    private String cha_media;

    private String cha_sub_launch;

    private String duration;

    private Double revenue;

    private Long installs;

    private Double dau;

    private String installs_rate;

    private Double dau_arpu;

    private Double installs_arpu;

    private Double pv_per_video;
    private Double pv_per_plaque;
    private Double pv_per_banner;
    private Double pv_per_splash;
    private Double pv_per_msg;

    private Double ecpm_video;
    private Double ecpm_plaque;
    private Double ecpm_banner;
    private Double ecpm_splash;
    private Double ecpm_msg;

    private Long pv_video;
    private Long pv_plaque;
    private Long pv_banner;
    private Long pv_splash;
    private Long pv_msg;

    private Double revenue_video;
    private Double revenue_plaque;
    private Double revenue_banner;
    private Double revenue_splash;
    private Double revenue_msg;

    private Double dau_arpu_video;
    private Double dau_arpu_plaque;
    private Double dau_arpu_banner;
    private Double dau_arpu_splash;
    private Double dau_arpu_msg;

    private int out;

    private Long out_installs;
    private Double out_dau;
    private Double out_revenue;
    private Long out_pv_video;
    private Long out_pv_plaque;
    private Long out_pv_banner;
    private Long out_pv_splash;
    private Long out_pv_msg;
    private Double out_revenue_video;
    private Double out_revenue_plaque;
    private Double out_revenue_banner;
    private Double out_revenue_splash;
    private Double out_revenue_msg;
    private String out_duration;
}
