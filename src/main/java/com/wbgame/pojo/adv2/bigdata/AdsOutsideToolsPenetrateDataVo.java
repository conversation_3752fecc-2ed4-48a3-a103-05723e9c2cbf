package com.wbgame.pojo.adv2.bigdata;

import lombok.Data;

/**
 * 工具海外产品功能渗透报表 vo
 */
@Data
public class AdsOutsideToolsPenetrateDataVo {

    /**
     * 日期
     */
    private String tdate;

    /**
     * 产品ID
     */
    private String appid;

    /**
     * 产品名称
     */
    private String app_name;

    /**
     * 系统版本
     */
    private String os_version;

    /**
     * 品牌
     */
    private String brand;

    /**
     * dau
     */
    private Integer dau;

    /**
     * 自然量uv
     */
    private String natural_uv;


    /**
     * topc初始化渗透
     */
    private String topc_penetration;

    /**
     * 新C模初始化渗透
     */
    private String cmode_penetration;

    /**
     * 新bh初始化渗透
     */
    private String bhmode_penetration;


    /**
     * 新激活uv
     */
    private String news;

    /**
     * 新bh首启uv
     */
    private String auto_bh_uv;


    /**
     * creating渗透
     */
    private String create_uv_penetration;

    /**
     * 人均creating
     */
    private String create_pv_penetration;


    /**
     * 创建成功率
     */
    private String create_success_rate;

    /**
     * 人均弹窗展示
     */
    private String avg_popup;


    /**
     * 人均整体广告展示
     */
    private String avg_game_ad_pv;

    /**
     * 整体广告展示成功率
     */
    private String ad_pv_success_rate;

    /**
     * 整体广告展示渗透
     */
    private String ad_uv_penetration;

    /**
     * 是否新用户
     */
    private String isnew;

}
