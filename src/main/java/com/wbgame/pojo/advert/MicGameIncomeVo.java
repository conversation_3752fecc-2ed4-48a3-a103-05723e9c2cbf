package com.wbgame.pojo.advert;


/**
 * <AUTHOR>
 * @Classname MicGameIncomeVo
 * @Description TODO
 * @Date 2022/4/11 17:53
 */
public class MicGameIncomeVo {


    private String tdate;               //日期
    private String appid;               //产品id
    private String appname;             //产品名称
    private String rebate_cost;         //返点消耗
    private String add_num;             //注册
    private String cpa;                 //cpa
    private String dau;                 //活跃
    private String reg_ratio;           //新增占比 新增/活跃人数
    private String pay_num;             //付费人数
    private String mds_pay_num;         //米大师付费人数 安卓付费人数
    private String wx_pay_num;          //微信支付付费人数 IOS付费人数
    private String tt_pay_num;          //头条支付人数
    private String pay_income_total;    //付费收入
    private String pay_ratio;           //付费率 付费人数/活跃
    private String pay_user_arpu;       //付费用户arpu 付费收入/付费人数
    private String pay_arpu;            //付费arpu 付费收入（分成后/活跃）
    private String ad_arpu;             //广告arpu
    private String all_arpu;            //总arpu
    private String ad_income;           //广告总收入
    private String pay_income;          //付费收入(分成后)
    private String all_income;          //总收入 分成收入+广告收入
    private String profit;              //当天利润 总收入-返点消耗
    private String ad_revenue;          //流量广告金
    private String pay_ad_revenue;      //内购广告金
    private String refund_revenue;      //退款
    private String net_profit;          //实际利润
    private String pre_profit;          //总收入(分成前)

    private String android_order_cnt;   //安卓付费笔数
    private String ios_order_cnt;       //ios付费笔数

    private String total_rebate_cost;   //累计投放 返点消耗累计值 从表有数据开始
    private String total_income;        //累计收入 总收入累计值 从
    private String total_ad_revenue;    //累计广告金
    private String total_profit;        //累计利润 累计收入+累计广告金-累计投放
    private String total_gross_profit_ratio;  //累计毛利率 累计收入+累计广告金 / 累计投放



    private String mds_pay_income;      //米大师支付收入
    private String wx_pay_income;       //微信支付收入
    private String tt_pay_income;       //字节支付收入

    private String modify_user;         //最后操作人

    private String pay_reg_user_num;    //新增用户付费用户数

    private String pay_reg_user_num_ratio; //新增用户付费率

    public String getPay_ad_revenue() {
        return pay_ad_revenue;
    }

    public void setPay_ad_revenue(String pay_ad_revenue) {
        this.pay_ad_revenue = pay_ad_revenue;
    }

    public String getRefund_revenue() {
        return refund_revenue;
    }

    public void setRefund_revenue(String refund_revenue) {
        this.refund_revenue = refund_revenue;
    }

    public String getPre_profit() {
        return pre_profit;
    }

    public void setPre_profit(String pre_profit) {
        this.pre_profit = pre_profit;
    }

    public String getTt_pay_num() {
        return tt_pay_num;
    }

    public void setTt_pay_num(String tt_pay_num) {
        this.tt_pay_num = tt_pay_num;
    }

    public String getTt_pay_income() {
        return tt_pay_income;
    }

    public void setTt_pay_income(String tt_pay_income) {
        this.tt_pay_income = tt_pay_income;
    }

    public String getAndroid_order_cnt() {
        return android_order_cnt;
    }

    public void setAndroid_order_cnt(String android_order_cnt) {
        this.android_order_cnt = android_order_cnt;
    }

    public String getIos_order_cnt() {
        return ios_order_cnt;
    }

    public void setIos_order_cnt(String ios_order_cnt) {
        this.ios_order_cnt = ios_order_cnt;
    }

    public String getPay_reg_user_num() {
        return pay_reg_user_num;
    }

    public void setPay_reg_user_num(String pay_reg_user_num) {
        this.pay_reg_user_num = pay_reg_user_num;
    }

    public String getPay_reg_user_num_ratio() {
        return pay_reg_user_num_ratio;
    }

    public void setPay_reg_user_num_ratio(String pay_reg_user_num_ratio) {
        this.pay_reg_user_num_ratio = pay_reg_user_num_ratio;
    }

    public String getMds_pay_num() {
        return mds_pay_num;
    }

    public void setMds_pay_num(String mds_pay_num) {
        this.mds_pay_num = mds_pay_num;
    }

    public String getWx_pay_num() {
        return wx_pay_num;
    }

    public void setWx_pay_num(String wx_pay_num) {
        this.wx_pay_num = wx_pay_num;
    }

    public String getModify_user() {
        return modify_user;
    }

    public void setModify_user(String modify_user) {
        this.modify_user = modify_user;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getRebate_cost() {
        return rebate_cost;
    }

    public void setRebate_cost(String rebate_cost) {
        this.rebate_cost = rebate_cost;
    }

    public String getAdd_num() {
        return add_num;
    }

    public void setAdd_num(String add_num) {
        this.add_num = add_num;
    }

    public String getCpa() {
        return cpa;
    }

    public void setCpa(String cpa) {
        this.cpa = cpa;
    }

    public String getDau() {
        return dau;
    }

    public void setDau(String dau) {
        this.dau = dau;
    }

    public String getReg_ratio() {
        return reg_ratio;
    }

    public void setReg_ratio(String reg_ratio) {
        this.reg_ratio = reg_ratio;
    }

    public String getPay_num() {
        return pay_num;
    }

    public void setPay_num(String pay_num) {
        this.pay_num = pay_num;
    }

    public String getPay_income_total() {
        return pay_income_total;
    }

    public void setPay_income_total(String pay_income_total) {
        this.pay_income_total = pay_income_total;
    }

    public String getPay_ratio() {
        return pay_ratio;
    }

    public void setPay_ratio(String pay_ratio) {
        this.pay_ratio = pay_ratio;
    }

    public String getPay_user_arpu() {
        return pay_user_arpu;
    }

    public void setPay_user_arpu(String pay_user_arpu) {
        this.pay_user_arpu = pay_user_arpu;
    }

    public String getPay_arpu() {
        return pay_arpu;
    }

    public void setPay_arpu(String pay_arpu) {
        this.pay_arpu = pay_arpu;
    }

    public String getAd_arpu() {
        return ad_arpu;
    }

    public void setAd_arpu(String ad_arpu) {
        this.ad_arpu = ad_arpu;
    }

    public String getAll_arpu() {
        return all_arpu;
    }

    public void setAll_arpu(String all_arpu) {
        this.all_arpu = all_arpu;
    }

    public String getAd_income() {
        return ad_income;
    }

    public void setAd_income(String ad_income) {
        this.ad_income = ad_income;
    }

    public String getPay_income() {
        return pay_income;
    }

    public void setPay_income(String pay_income) {
        this.pay_income = pay_income;
    }

    public String getAll_income() {
        return all_income;
    }

    public void setAll_income(String all_income) {
        this.all_income = all_income;
    }

    public String getProfit() {
        return profit;
    }

    public void setProfit(String profit) {
        this.profit = profit;
    }

    public String getAd_revenue() {
        return ad_revenue;
    }

    public void setAd_revenue(String ad_revenue) {
        this.ad_revenue = ad_revenue;
    }

    public String getNet_profit() {
        return net_profit;
    }

    public void setNet_profit(String net_profit) {
        this.net_profit = net_profit;
    }

    public String getTotal_rebate_cost() {
        return total_rebate_cost;
    }

    public void setTotal_rebate_cost(String total_rebate_cost) {
        this.total_rebate_cost = total_rebate_cost;
    }

    public String getTotal_income() {
        return total_income;
    }

    public void setTotal_income(String total_income) {
        this.total_income = total_income;
    }

    public String getTotal_ad_revenue() {
        return total_ad_revenue;
    }

    public void setTotal_ad_revenue(String total_ad_revenue) {
        this.total_ad_revenue = total_ad_revenue;
    }

    public String getTotal_profit() {
        return total_profit;
    }

    public void setTotal_profit(String total_profit) {
        this.total_profit = total_profit;
    }

    public String getTotal_gross_profit_ratio() {
        return total_gross_profit_ratio;
    }

    public void setTotal_gross_profit_ratio(String total_gross_profit_ratio) {
        this.total_gross_profit_ratio = total_gross_profit_ratio;
    }

    public String getMds_pay_income() {
        return mds_pay_income;
    }

    public void setMds_pay_income(String mds_pay_income) {
        this.mds_pay_income = mds_pay_income;
    }

    public String getWx_pay_income() {
        return wx_pay_income;
    }

    public void setWx_pay_income(String wx_pay_income) {
        this.wx_pay_income = wx_pay_income;
    }
}
