package com.wbgame.pojo;

import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

@ApiModel("业务模式配置")
public class DnBusModelConfigDTO {

    @ApiModelProperty("主键")
    private Integer id;

//    @ApiModelProperty("id集合")
//    @NotEmpty(message = "id集合不能为空", groups = UpdateGroup.class)
//    private List<Integer> idList;

    @ApiModelProperty(value = "业务模式")
    @NotBlank(message = "业务模式不能为空", groups = {UpdateGroup.class, Default.class})
    private String busModel;

    @ApiModelProperty("状态")
    @NotNull(message = "状态码必填", groups = {Default.class, UpdateGroup.class})
    @Range(max = 1, message = "状态码错误", groups = {Default.class, UpdateGroup.class, QueryGroup.class})
    private Short status;

    @ApiModelProperty("业务端口")
    private String busPort;
    @ApiModelProperty("备注")
    private String description;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("修改时间")
    private String updateTime;
    @ApiModelProperty("创建人")
    private String createUser;
    @ApiModelProperty("修改人")
    private String updateUser;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

//    public List<Integer> getIdList() {
//        return idList;
//    }
//
//    public void setIdList(List<Integer> idList) {
//        this.idList = idList;
//    }

    public String getBusModel() {
        return busModel;
    }

    public void setBusModel(String busModel) {
        this.busModel = busModel;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }
    public String getBusPort() {
        return busPort;
    }

    public void setBusPort(String busPort) {
        this.busPort = busPort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}