package com.wbgame.pojo.product;

public class DnwxX4dataVo {
	
	private String id; // 
	private String appid; // 应用
	private String cha_id; // 子渠道
	private String prjid; // 项目id
	private String adpos_type; // 广告位类型
	private String agent; // 变现平台
	private String ecpm; // 价格，预估ecpm
	private String adpos; // 广告位
	private String bidding; // 竞价类型，0-非bidding模式，1-bidding模式，all-不区分
	private String convert_type; // 转化类型
	private String adving; // 特殊广告主，0-不是特殊广告主，1-是特殊广告主，all-不区分
	private String adving_show; // 特殊广告主是否模拟展示
	private String preset_ctr; // 预设ctr，加百分号
	private String zc_back; // 正常点击后拉回app时间，单位秒
	private String mn_back; // 模拟点击后拉回app时间，单位秒
	private String day_click; // 单日点击上限
	private String sum_click; // 总点击上限
	private String delay_click; // 点击延迟时间
	private String statu; // 启用状态，0-失效 1-生效
	private String createtime; // 创建时间
	private String cuser; // 创建人
	private String endtime; // 最后操作时间
	private String euser; // 操作人
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getAdpos_type() {
		return adpos_type;
	}
	public void setAdpos_type(String adpos_type) {
		this.adpos_type = adpos_type;
	}
	public String getAgent() {
		return agent;
	}
	public void setAgent(String agent) {
		this.agent = agent;
	}
	public String getEcpm() {
		return ecpm;
	}
	public void setEcpm(String ecpm) {
		this.ecpm = ecpm;
	}
	public String getAdpos() {
		return adpos;
	}
	public void setAdpos(String adpos) {
		this.adpos = adpos;
	}
	public String getBidding() {
		return bidding;
	}
	public void setBidding(String bidding) {
		this.bidding = bidding;
	}
	public String getConvert_type() {
		return convert_type;
	}
	public void setConvert_type(String convert_type) {
		this.convert_type = convert_type;
	}
	public String getAdving() {
		return adving;
	}
	public void setAdving(String adving) {
		this.adving = adving;
	}
	public String getAdving_show() {
		return adving_show;
	}
	public void setAdving_show(String adving_show) {
		this.adving_show = adving_show;
	}
	public String getPreset_ctr() {
		return preset_ctr;
	}
	public void setPreset_ctr(String preset_ctr) {
		this.preset_ctr = preset_ctr;
	}
	public String getZc_back() {
		return zc_back;
	}
	public void setZc_back(String zc_back) {
		this.zc_back = zc_back;
	}
	public String getMn_back() {
		return mn_back;
	}
	public void setMn_back(String mn_back) {
		this.mn_back = mn_back;
	}
	public String getDay_click() {
		return day_click;
	}
	public void setDay_click(String day_click) {
		this.day_click = day_click;
	}
	public String getSum_click() {
		return sum_click;
	}
	public void setSum_click(String sum_click) {
		this.sum_click = sum_click;
	}
	public String getStatu() {
		return statu;
	}
	public void setStatu(String statu) {
		this.statu = statu;
	}
	public String getCreatetime() {
		return createtime;
	}
	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}
	public String getCuser() {
		return cuser;
	}
	public void setCuser(String cuser) {
		this.cuser = cuser;
	}
	public String getEndtime() {
		return endtime;
	}
	public void setEndtime(String endtime) {
		this.endtime = endtime;
	}
	public String getEuser() {
		return euser;
	}
	public void setEuser(String euser) {
		this.euser = euser;
	}
	public String getDelay_click() {
		return delay_click;
	}
	public void setDelay_click(String delay_click) {
		this.delay_click = delay_click;
	}
	
}
