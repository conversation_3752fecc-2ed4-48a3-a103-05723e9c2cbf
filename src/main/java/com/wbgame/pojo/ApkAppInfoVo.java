package com.wbgame.pojo;

/**
 * apk游戏对象
 * <AUTHOR>
 */
public class ApkAppInfoVo {

    private String gameId;
    private String appid;
    private String appkey;
    private String gameName;
    private String originator;
    private String maxpjid;
    private int todayUser;
    private int currDau;
    private int currNew;
    private int currStart;
    private int utimes;

    public int getCurrDau() {
        return currDau;
    }

    public void setCurrDau(int currDau) {
        this.currDau = currDau;
    }

    public int getCurrNew() {
        return currNew;
    }

    public void setCurrNew(int currNew) {
        this.currNew = currNew;
    }

    public int getCurrStart() {
        return currStart;
    }

    public void setCurrStart(int currStart) {
        this.currStart = currStart;
    }

    public int getUtimes() {
        return utimes;
    }

    public void setUtimes(int utimes) {
        this.utimes = utimes;
    }

    public int getTodayUser() {
        return todayUser;
    }

    public void setTodayUser(int todayUser) {
        this.todayUser = todayUser;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public String getOriginator() {
        return originator;
    }

    public void setOriginator(String originator) {
        this.originator = originator;
    }

    public String getMaxpjid() {
        return maxpjid;
    }

    public void setMaxpjid(String maxpjid) {
        this.maxpjid = maxpjid;
    }
}