package com.wbgame.pojo.yxcGame;

public class GameContentDetailVo {
	
	private String gameId;
	private String info;
	private String statu;
	private String keyword;
	private String priority;
	private String appName;
	private String abbreviatedName;
	private String appDesc;
	private String downloadUrl;
	private String pkgName;
	private String apkSize;
	private String apkLength;
	private String installDesktop;
	private String appVerName;
	private String appVerCode;
	private String playNumInfo;
	private String iconUrl;
	private String appDetailUrl;
	private String appDetailDesc;
	private String taskInfo;
	public String getGameId() {
		return gameId;
	}
	public void setGameId(String gameId) {
		this.gameId = gameId;
	}
	public String getInfo() {
		return info;
	}
	public void setInfo(String info) {
		this.info = info;
	}
	public String getStatu() {
		return statu;
	}
	public void setStatu(String statu) {
		this.statu = statu;
	}
	public String getPriority() {
		return priority;
	}
	public void setPriority(String priority) {
		this.priority = priority;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	public String getAbbreviatedName() {
		return abbreviatedName;
	}
	public void setAbbreviatedName(String abbreviatedName) {
		this.abbreviatedName = abbreviatedName;
	}
	public String getAppDesc() {
		return appDesc;
	}
	public void setAppDesc(String appDesc) {
		this.appDesc = appDesc;
	}
	public String getDownloadUrl() {
		return downloadUrl;
	}
	public void setDownloadUrl(String downloadUrl) {
		this.downloadUrl = downloadUrl;
	}
	public String getPkgName() {
		return pkgName;
	}
	public void setPkgName(String pkgName) {
		this.pkgName = pkgName;
	}
	public String getApkSize() {
		return apkSize;
	}
	public void setApkSize(String apkSize) {
		this.apkSize = apkSize;
	}
	public String getApkLength() {
		return apkLength;
	}
	public void setApkLength(String apkLength) {
		this.apkLength = apkLength;
	}
	public String getInstallDesktop() {
		return installDesktop;
	}
	public void setInstallDesktop(String installDesktop) {
		this.installDesktop = installDesktop;
	}
	public String getAppVerName() {
		return appVerName;
	}
	public void setAppVerName(String appVerName) {
		this.appVerName = appVerName;
	}
	public String getAppVerCode() {
		return appVerCode;
	}
	public void setAppVerCode(String appVerCode) {
		this.appVerCode = appVerCode;
	}
	public String getPlayNumInfo() {
		return playNumInfo;
	}
	public void setPlayNumInfo(String playNumInfo) {
		this.playNumInfo = playNumInfo;
	}
	public String getIconUrl() {
		return iconUrl;
	}
	public void setIconUrl(String iconUrl) {
		this.iconUrl = iconUrl;
	}
	public String getAppDetailUrl() {
		return appDetailUrl;
	}
	public void setAppDetailUrl(String appDetailUrl) {
		this.appDetailUrl = appDetailUrl;
	}
	public String getAppDetailDesc() {
		return appDetailDesc;
	}
	public void setAppDetailDesc(String appDetailDesc) {
		this.appDetailDesc = appDetailDesc;
	}
	public String getTaskInfo() {
		return taskInfo;
	}
	public void setTaskInfo(String taskInfo) {
		this.taskInfo = taskInfo;
	}
	public String getKeyword() {return keyword;}
	public void setKeyword(String keyword) {this.keyword = keyword;}
}
