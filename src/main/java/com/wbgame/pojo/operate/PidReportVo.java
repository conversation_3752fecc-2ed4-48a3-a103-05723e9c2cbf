package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目id报表参数")
public class PidReportVo extends CommonReportVo{

    @ApiModelProperty(value = "项目id",dataType = "String")
    private String pid;

    @ApiModelProperty(value = "是否新用户",dataType = "String")
    private String is_new;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getIs_new() {
        return is_new;
    }

    public void setIs_new(String is_new) {
        this.is_new = is_new;
    }
}
