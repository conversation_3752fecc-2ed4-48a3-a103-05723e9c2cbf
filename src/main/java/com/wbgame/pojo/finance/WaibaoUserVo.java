package com.wbgame.pojo.finance;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public class WaibaoUserVo {

    /**
     * 测试添加
     */
    private String keysx;

    /**
     * 测试添加
     */
    private String text;

    /**
     * 角色授权的应用列表
     */
    private String app_key;

    /**
     * 角色授权的应用列表
     */
    private String app_name;

    /**
     * 角色授权的应用列表
     */
    private String role_app_slot;

    /**
     * 用户账号
     */
    private String user_id;

    /**
     * 用户
     */
    private String user_name;

    /**
     * 权限标识
     */
    private String org_id;

    /**
     * 系统标识
     */
    private String sys;

    /**
     * 权限名称
     */
    private String org_name;

    /**
     * 隐藏的菜单分类
     */
    private String hidden_menu_list;

    /**
     * 已授权的菜单目录
     */
    private String page_list;

    /**
     * 目录页面路径键
     */
    private String index;

    /**
     * 目录类型
     */
    private String style;

    /**
     * 目录图标
     */
    private String icon;

    /**
     * 目录中文标题
     */
    private String title;

    /**
     * 目录归属
     */
    private String menu;

    /**
     * 目录开关
     */
    private int off;

    /**
     * 目录权重_失效
     */
    private int leve;

    /**
     * 目录插槽
     */
    private String slot;

    private String login_name;

    private String password;

    /**
     * 昵称
     */
    private String nick_name;

    /**
     * 角色ID
     */
    private String role_id;

    /**
     * 角色名称
     */
    private String role_name;

    private String app_group;

    private String token;

    /**
     * 公司
     */
    private String email;

    /**
     * 平台ID
     */
    private String phone;

    /**
     * 状态 1-启用，2-停用，3-离职
     */
    private String status;

    private String date;

    private String level;

    private String company;

    private String sys_type;

    @Override
    public String toString() {
        return "WaibaoUserVo [keysx=" + keysx + ", text=" + text + ", user_id=" + user_id + ", user_name=" + user_name
                + ", org_id=" + org_id + ", sys=" + sys + ", hidden_menu_list=" + hidden_menu_list + ", page_list="
                + page_list + ", login_name=" + login_name + ", password=" + password + ", nick_name=" + nick_name
                + ", role_id=" + role_id + ", role_name=" + role_name + ", app_group=" + app_group + ", token=" + token
                + ", email=" + email + ", phone=" + phone + ", status=" + status + ", date=" + date + "]";
    }

    public String getApp_key() {
        return app_key;
    }

    public void setApp_key(String app_key) {
        this.app_key = app_key;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getRole_app_slot() {
        return role_app_slot;
    }

    public void setRole_app_slot(String role_app_slot) {
        this.role_app_slot = role_app_slot;
    }

    public String getSlot() {
        return slot;
    }

    public void setSlot(final String slot) {
        this.slot = slot;
    }

    public String getOrg_name() {
        return org_name;
    }

    public void setOrg_name(final String org_name) {
        this.org_name = org_name;
    }

    public int getLeve() {
        return leve;
    }

    public void setLeve(final int leve) {
        this.leve = leve;
    }

    public int getOff() {
        return off;
    }

    public void setOff(final int off) {
        this.off = off;
    }

    public String getMenu() {
        return menu;
    }

    public void setMenu(final String menu) {
        this.menu = menu;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(final String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(final String icon) {
        this.icon = icon;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(final String style) {
        this.style = style;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(final String index) {
        this.index = index;
    }

    public String getLogin_name() {
        return login_name;
    }

    public String getHidden_menu_list() {
        return hidden_menu_list;
    }

    public void setHidden_menu_list(final String hidden_menu_list) {
        this.hidden_menu_list = hidden_menu_list;
    }

    public String getPage_list() {
        return page_list;
    }

    public void setPage_list(final String page_list) {
        this.page_list = page_list;
    }

    public String getSys() {
        return sys;
    }

    public void setSys(final String sys) {
        this.sys = sys;
    }

    public String getOrg_id() {
        return org_id;
    }

    public void setOrg_id(final String org_id) {
        this.org_id = org_id;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(final String user_name) {
        this.user_name = user_name;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(final String user_id) {
        this.user_id = user_id;
    }

    public String getText() {
        return text;
    }

    public void setText(final String text) {
        this.text = text;
    }

    public String getKeysx() {
        return keysx;
    }

    public void setKeysx(final String keysx) {
        this.keysx = keysx;
    }

    public void setLogin_name(final String login_name) {
        this.login_name = login_name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(final String password) {
        this.password = password;
    }

    public String getNick_name() {
        return nick_name;
    }

    public void setNick_name(final String nick_name) {
        this.nick_name = nick_name;
    }

    public String getRole_id() {
        return role_id;
    }

    public void setRole_id(final String role_id) {
        this.role_id = role_id;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(final String role_name) {
        this.role_name = role_name;
    }

    public String getApp_group() {
        return app_group;
    }

    public void setApp_group(final String app_group) {
        this.app_group = app_group;
    }

    public String getToken() {
        return token;
    }

    public void setToken(final String token) {
        this.token = token;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(final String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(final String phone) {
        this.phone = phone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public String getDate() {
        return date;
    }

    public void setDate(final String date) {
        this.date = date;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getSys_type() {
        return sys_type;
    }

    public void setSys_type(String sys_type) {
        this.sys_type = sys_type;
    }
}
