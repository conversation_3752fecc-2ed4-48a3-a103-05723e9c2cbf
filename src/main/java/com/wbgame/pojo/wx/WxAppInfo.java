package com.wbgame.pojo.wx;

import java.util.Date;

/**
 * @description: 微信小程序信息
 * @author: huangmb
 * @date: 2020-10-29
 **/
public class WxAppInfo {
    private Long id ;

    //游戏名称
    private String name;

    //appid
    private String appid;

    //秘钥
    private String secret;

    //创建时间
    private Date createTime;

    //是否使用
    private Integer isUse;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    @Override
    public String toString() {
        return "WxAppInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", appid='" + appid + '\'' +
                ", secret='" + secret + '\'' +
                ", createTime=" + createTime +
                ", isUse=" + isUse +
                '}';
    }
}
