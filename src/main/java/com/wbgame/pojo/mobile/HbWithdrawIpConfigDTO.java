package com.wbgame.pojo.mobile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel("通用提现ip白名单配置")
public class HbWithdrawIpConfigDTO {
    private Integer id;

    /**
     * ip
     */
    @ApiModelProperty("ip")
    @NotBlank(message = "ip不能为空")
    private String ip;

    /**
     * 状态
     */
    @NotNull
    @Range(max = 10, message = "状态错误")
    @ApiModelProperty("状态")
    private Integer status;

    private Date createTime;

    private Date updateTime;

    private String createOwner;

    private String updateOwner;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateOwner() {
        return createOwner;
    }

    public void setCreateOwner(String createOwner) {
        this.createOwner = createOwner == null ? null : createOwner.trim();
    }

    public String getUpdateOwner() {
        return updateOwner;
    }

    public void setUpdateOwner(String updateOwner) {
        this.updateOwner = updateOwner == null ? null : updateOwner.trim();
    }
}