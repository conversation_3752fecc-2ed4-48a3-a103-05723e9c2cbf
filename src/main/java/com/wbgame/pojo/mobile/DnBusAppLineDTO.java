package com.wbgame.pojo.mobile;

import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

@ApiModel("业务线配置DTO")
public class DnBusAppLineDTO {

    @ApiModelProperty("ID")
    private Integer id;

    @ApiModelProperty("业务线")
    @NotBlank(message = "业务线不能为空", groups = {Default.class, UpdateGroup.class})
    private String busLine;

    @ApiModelProperty("备注")
    private String description;

    @ApiModelProperty("状态 1:开启，0:关闭")
    @NotNull(message = "状态不能为空", groups = {Default.class, UpdateGroup.class})
    @Range(max = 1, message = "状态码错误", groups = {Default.class, UpdateGroup.class, QueryGroup.class})
    private Byte status;

//    @ApiModelProperty("创建时间")
    private String createTime;

//    @ApiModelProperty("修改时间")
    private String updateTime;

//    @ApiModelProperty("创建人")
    private String createUser;

//    @ApiModelProperty("修改人")
    private String updateUser;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusLine() {
        return busLine;
    }

    public void setBusLine(String busLine) {
        this.busLine = busLine == null ? null : busLine.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}