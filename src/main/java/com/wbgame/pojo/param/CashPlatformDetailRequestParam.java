package com.wbgame.pojo.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 变现平台明细表 dto
 * @Date 2025/3/18 14:35
 */
@Data
//@Schema(description = "CashPlatformDetailRequestParam")
public class CashPlatformDetailRequestParam {

//    @Schema(description = "开始页面")
    private Integer start;

//    @Schema(description = "每页数量")
    private Integer limit;

//    @Schema(description = "查询开始时间")
    private String start_date;

//    @Schema(description = "查询结束时间")
    private String end_date;

//    @Schema(description = "应用id列表")
    private String app_category;

//    @Schema(description = "应用id列表")
    private String dnappid;


//    @Schema(description = "渠道类型")
    private String cha_type;

//    @Schema(description = "投放媒体")
    private String cha_media;

//    @Schema(description = "cha_sub_launch")
    private String cha_sub_launch;

//    @Schema(description = "子渠道标识")
    private String cha_id;

//    @Schema(description = "变现平台")
    private String agent;

//    @Schema(description = "sdk广告源类型")
    private String placement_type;

//    @Schema(description = "广告源(模糊查询)")
    private String ad_sid;

//    @Schema(description = "广告源(精确查询)")
    private String detail_ad_sid;

//    @Schema(description = "地区")
    private String country;

//    @Schema(description = "广告使用类型")
    private String open_type;

//    @Schema(description = "区分应用内外")
    private String out;

//    @Schema(description = "sdk_code")
    private String sdk_code;

//    @Schema(description = "sdk_appid")
    private String sdk_appid;

//    @Schema(description = "应用自定义分组")
    private String appid_tag;

//    @Schema(description = "反选")
    private String appid_tag_rev;


//    @Schema(description = "维度")
    private String groups;

    private List<String> groupList;


//    @Schema(description = "排序条件")
    private String order_str;

    private String ad_sid_group;


//    @Schema(description = "导出文件表头value")
    private String value;

//    @Schema(description = "导出文件名")
    private String export_file_name;

    private String member_id;

}
