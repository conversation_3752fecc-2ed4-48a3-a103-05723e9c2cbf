package com.wbgame.pojo.test;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Classname ForbiddenResultVo
 * @Description TODO
 * @Date 2022/8/3 17:05
 */
@ApiModel(value = "黑名单服务")
public class ForbiddenResultVo {

    @ApiModelProperty(value = "封禁理由")
    private String reason;
    @ApiModelProperty(value = "解封剩余时间")
    private String leftTime;
    @ApiModelProperty(value = "解封截止时间")
    private String forbiddenTime;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getLeftTime() {
        return leftTime;
    }

    public void setLeftTime(String leftTime) {
        this.leftTime = leftTime;
    }

    public String getForbiddenTime() {
        return forbiddenTime;
    }

    public void setForbiddenTime(String forbiddenTime) {
        this.forbiddenTime = forbiddenTime;
    }
}
