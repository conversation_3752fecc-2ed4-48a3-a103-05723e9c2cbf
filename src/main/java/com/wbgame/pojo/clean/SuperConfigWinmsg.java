package com.wbgame.pojo.clean;

/**
 * 文案弹窗
 * <AUTHOR>
 * @date: 2021年2月7日
 */
public class SuperConfigWinmsg {
	private String msgId;//配置id -服务端生成,需保证全局唯一
    private String appid;
    private String cha;
    private String prjid;
    private String icon1;//icon1链接 -文件上传控件,文件上传目录unlock类型
    private String text1;//文案1内容
    private String btnText;//按钮文案
    private String icon2;//icon2链接
    private String creatTime;
    private String modifyTime;
    private String modifyUser;
    private String status;
    private String text2;//文案2内容
    private String per;//比例 -输入框,默认100.
    private String msgType;//配置类型 -下拉框,{解锁(unlock)、home键(home)}
    private String loadingMsg;//清理动画中文字提示
    private String finshMsg;//清理完成后中文字提示
	public String getMsgId() {
		return msgId;
	}
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha() {
		return cha;
	}
	public void setCha(String cha) {
		this.cha = cha;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getIcon1() {
		return icon1;
	}
	public void setIcon1(String icon1) {
		this.icon1 = icon1;
	}
	public String getText1() {
		return text1;
	}
	public void setText1(String text1) {
		this.text1 = text1;
	}
	public String getBtnText() {
		return btnText;
	}
	public void setBtnText(String btnText) {
		this.btnText = btnText;
	}
	public String getIcon2() {
		return icon2;
	}
	public void setIcon2(String icon2) {
		this.icon2 = icon2;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getText2() {
		return text2;
	}
	public void setText2(String text2) {
		this.text2 = text2;
	}
	public String getPer() {
		return per;
	}
	public void setPer(String per) {
		this.per = per;
	}
	public String getMsgType() {
		return msgType;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getLoadingMsg() {
		return loadingMsg;
	}
	public void setLoadingMsg(String loadingMsg) {
		this.loadingMsg = loadingMsg;
	}
	public String getFinshMsg() {
		return finshMsg;
	}
	public void setFinshMsg(String finshMsg) {
		this.finshMsg = finshMsg;
	}

}