package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("弹窗创建报表")
public class AdsToolPopCreateDailyVO {

    private Long id;

    @ApiModelProperty("日期")
    private String tdate;

    @ApiModelProperty("应用")
    private String appid;

    @ApiModelProperty("应用名称")
    private String app;

    @ApiModelProperty("媒体")
    private String chaMedia;

    @ApiModelProperty("子渠道")
    private String channel;

    @ApiModelProperty("项目id")
    private String pid;

    @ApiModelProperty("系统")
    private String os;

    @ApiModelProperty("系统版本")
    private String osVer;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("机型")
    private String model;

    @ApiModelProperty("场景")
    private String scenes;

    @ApiModelProperty("用户分类")
    private String userType;

    @ApiModelProperty("总用户数")
    private Long totalUsers;

    @ApiModelProperty("creating人数")
    private Long pageCreatingUv;

    @ApiModelProperty("created人数")
    private Long pageCreatedUv;

    @ApiModelProperty("creating次数")
    private Long pageCreatingPv;

    @ApiModelProperty("created次数")
    private Long pageCreatedPv;

    @ApiModelProperty("creating占比")
    private String creatingProportion;

    @ApiModelProperty("create占比")
    private String createProportion;

    @ApiModelProperty("creating人均pv")
    private String creatingPerCapitaPv;

    @ApiModelProperty("created人均pv")
    private String createPerCapitaPv;

    @ApiModelProperty("创建成功率")
    private String creationSuccessRate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getChaMedia() {
        return chaMedia;
    }

    public void setChaMedia(String chaMedia) {
        this.chaMedia = chaMedia;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOsVer() {
        return osVer;
    }

    public void setOsVer(String osVer) {
        this.osVer = osVer;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getScenes() {
        return scenes;
    }

    public void setScenes(String scenes) {
        this.scenes = scenes;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Long getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }

    public Long getPageCreatingUv() {
        return pageCreatingUv;
    }

    public void setPageCreatingUv(Long pageCreatingUv) {
        this.pageCreatingUv = pageCreatingUv;
    }

    public Long getPageCreatedUv() {
        return pageCreatedUv;
    }

    public void setPageCreatedUv(Long pageCreatedUv) {
        this.pageCreatedUv = pageCreatedUv;
    }

    public Long getPageCreatingPv() {
        return pageCreatingPv;
    }

    public void setPageCreatingPv(Long pageCreatingPv) {
        this.pageCreatingPv = pageCreatingPv;
    }

    public Long getPageCreatedPv() {
        return pageCreatedPv;
    }

    public void setPageCreatedPv(Long pageCreatedPv) {
        this.pageCreatedPv = pageCreatedPv;
    }

    public String getCreatingProportion() {
        return creatingProportion;
    }

    public void setCreatingProportion(String creatingProportion) {
        this.creatingProportion = creatingProportion;
    }

    public String getCreateProportion() {
        return createProportion;
    }

    public void setCreateProportion(String createProportion) {
        this.createProportion = createProportion;
    }

    public String getCreatingPerCapitaPv() {
        return creatingPerCapitaPv;
    }

    public void setCreatingPerCapitaPv(String creatingPerCapitaPv) {
        this.creatingPerCapitaPv = creatingPerCapitaPv;
    }

    public String getCreatePerCapitaPv() {
        return createPerCapitaPv;
    }

    public void setCreatePerCapitaPv(String createPerCapitaPv) {
        this.createPerCapitaPv = createPerCapitaPv;
    }

    public String getCreationSuccessRate() {
        return creationSuccessRate;
    }

    public void setCreationSuccessRate(String creationSuccessRate) {
        this.creationSuccessRate = creationSuccessRate;
    }
}