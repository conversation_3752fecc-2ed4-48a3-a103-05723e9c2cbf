package com.wbgame.pojo.clean.face;

import com.wbgame.common.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.groups.Default;

@ApiModel("脸映DTO")
public class SuperFaceUserDTO {

    public static final String DATE_YYYY_MM_DD = "^((((19|20)\\d{2})-(0?[13-9]|1[012])-(0?[1-9]|[12]\\d|30))|(((19|20)\\d{2})-(0?[13578]|1[02])-31)|(((19|20)\\d{2})-0?2-(0?[1-9]|1\\d|2[0-8]))|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))-0?2-29))$";

    @ApiModelProperty("开始时间")
    @Pattern(regexp = DATE_YYYY_MM_DD, message = "日期格式错误", groups = {Default.class, QueryGroup.class})
    private String start_date;

    @ApiModelProperty("结束时间")
    @Pattern(regexp = DATE_YYYY_MM_DD, message = "日期格式错误", groups = {Default.class, QueryGroup.class})
    private String end_date;

    @ApiModelProperty("页码")
    @NotNull(message = "页码错误", groups = QueryGroup.class)
    private Integer start;

    @ApiModelProperty("条数")
    @NotNull(message = "条数错误", groups = QueryGroup.class)
    private Integer limit;

    @ApiModelProperty("用户id")
    private String userId;


    @ApiModelProperty("手机号码")
    private String phoneNumber;


    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
}