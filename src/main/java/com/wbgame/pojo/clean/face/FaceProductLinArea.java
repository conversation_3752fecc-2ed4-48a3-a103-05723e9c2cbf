package com.wbgame.pojo.clean.face;

import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/12/07 007
 * @class: FaceProductLinArea
 * @description:
 */
@ApiModel("商品地区关联pojo")
public class FaceProductLinArea {

    @ApiModelProperty("商品id")
    @NotNull(message = "商品不能为空", groups = {Default.class, UpdateGroup.class})
    private Integer productId;

    @ApiModelProperty("地区详情")

    private List<FaceOpreateSort> sortList;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<FaceOpreateSort> getSortList() {
        return sortList;
    }

    public void setSortList(List<FaceOpreateSort> sortList) {
        this.sortList = sortList;
    }
}
