package com.wbgame.pojo.clean.face;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("商品排序")
public class FaceOpreateSort {


    @ApiModelProperty("排序序号")
    private Integer sortId;

    @ApiModelProperty("商品id")
    private Integer productId;

    @ApiModelProperty("地区")
    private String area;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }
}