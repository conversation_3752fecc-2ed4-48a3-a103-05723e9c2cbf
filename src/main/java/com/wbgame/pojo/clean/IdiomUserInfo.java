package com.wbgame.pojo.clean;

public class IdiomUserInfo {
    private Long id;

    private String prjId;

    private String appId;

    private String pkg;

    private String wxProfile;

    private String wxName;

    private String randomName;

    private String userId;

    private String wxOpenId;

    private Byte isNew;

    private Integer signTotal;

    private Long signTime;

    private Integer currentLevel;

    private String androidId;

    private Integer curMontreeNum;

    private Integer curClickNum;

    private Integer curShakeNum;

    private Long homepageUpdateTime;

    private Integer curWithdrawLimit;

    private Long withdrawTime;

    private Byte userType;

    private String digitalId;

    private String createUser;

    private Long createTime;

    private String modifyUser;

    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId == null ? null : prjId.trim();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getPkg() {
        return pkg;
    }

    public void setPkg(String pkg) {
        this.pkg = pkg == null ? null : pkg.trim();
    }

    public String getWxProfile() {
        return wxProfile;
    }

    public void setWxProfile(String wxProfile) {
        this.wxProfile = wxProfile == null ? null : wxProfile.trim();
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName == null ? null : wxName.trim();
    }

    public String getRandomName() {
        return randomName;
    }

    public void setRandomName(String randomName) {
        this.randomName = randomName == null ? null : randomName.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getWxOpenId() {
        return wxOpenId;
    }

    public void setWxOpenId(String wxOpenId) {
        this.wxOpenId = wxOpenId == null ? null : wxOpenId.trim();
    }

    public Byte getIsNew() {
        return isNew;
    }

    public void setIsNew(Byte isNew) {
        this.isNew = isNew;
    }

    public Integer getSignTotal() {
        return signTotal;
    }

    public void setSignTotal(Integer signTotal) {
        this.signTotal = signTotal;
    }

    public Long getSignTime() {
        return signTime;
    }

    public void setSignTime(Long signTime) {
        this.signTime = signTime;
    }

    public Integer getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(Integer currentLevel) {
        this.currentLevel = currentLevel;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId == null ? null : androidId.trim();
    }

    public Integer getCurMontreeNum() {
        return curMontreeNum;
    }

    public void setCurMontreeNum(Integer curMontreeNum) {
        this.curMontreeNum = curMontreeNum;
    }

    public Integer getCurClickNum() {
        return curClickNum;
    }

    public void setCurClickNum(Integer curClickNum) {
        this.curClickNum = curClickNum;
    }

    public Integer getCurShakeNum() {
        return curShakeNum;
    }

    public void setCurShakeNum(Integer curShakeNum) {
        this.curShakeNum = curShakeNum;
    }

    public Long getHomepageUpdateTime() {
        return homepageUpdateTime;
    }

    public void setHomepageUpdateTime(Long homepageUpdateTime) {
        this.homepageUpdateTime = homepageUpdateTime;
    }

    public Integer getCurWithdrawLimit() {
        return curWithdrawLimit;
    }

    public void setCurWithdrawLimit(Integer curWithdrawLimit) {
        this.curWithdrawLimit = curWithdrawLimit;
    }

    public Long getWithdrawTime() {
        return withdrawTime;
    }

    public void setWithdrawTime(Long withdrawTime) {
        this.withdrawTime = withdrawTime;
    }

    public Byte getUserType() {
        return userType;
    }

    public void setUserType(Byte userType) {
        this.userType = userType;
    }

    public String getDigitalId() {
        return digitalId;
    }

    public void setDigitalId(String digitalId) {
        this.digitalId = digitalId == null ? null : digitalId.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}