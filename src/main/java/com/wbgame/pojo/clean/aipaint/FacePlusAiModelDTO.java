package com.wbgame.pojo.clean.aipaint;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("ai作画模板列表dto")
public class FacePlusAiModelDTO extends GenericQueryParameters {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("'模板名称'")
    private String modelName;

    @ApiModelProperty("'模板关键词'")
    private String modelKeyword;

    @ApiModelProperty("'strength值'")
    private BigDecimal modelStrength;

    @ApiModelProperty("'seed值'")
    private Integer modelSeed;

    @ApiModelProperty("'制作数'")
    private Integer makeTemplateCount;

    @ApiModelProperty("'转化会员数'")
    private Integer tplConvertVipOunt;

    @ApiModelProperty("'资源地址 1 国内 2 海外'")
    private Byte region;

    @ApiModelProperty("'封面图片'")
    private String coverUrl;

    @ApiModelProperty("'详情图地址'")
    private String sourceUrl;

    @ApiModelProperty("'模板属性 1 付费 2 激励 3 免费'")
    private Byte tempType;

    @ApiModelProperty("'发布时间'")
    private Long taskTime;

    @ApiModelProperty("'状态 1 上架 2 下架 3 定时上架 4 测试'")
    private Byte state;

    @ApiModelProperty("'定时上架时间'")
    private String shelveTime;

    @ApiModelProperty("'模板序号'")
    private Integer sort;

    @ApiModelProperty("'创建人'")
    private String createUser;

    @ApiModelProperty("'创建时间(毫秒时间戳)'")
    private Long createTime;

    @ApiModelProperty("'修改人'")
    private String modifyUser;

    @ApiModelProperty("'修改时间(毫秒时间戳)'")
    private Long modifyTime;

    @ApiModelProperty("地区id")
    private Long areaId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName == null ? null : modelName.trim();
    }

    public String getModelKeyword() {
        return modelKeyword;
    }

    public void setModelKeyword(String modelKeyword) {
        this.modelKeyword = modelKeyword == null ? null : modelKeyword.trim();
    }

    public BigDecimal getModelStrength() {
        return modelStrength;
    }

    public void setModelStrength(BigDecimal modelStrength) {
        this.modelStrength = modelStrength;
    }

    public Integer getModelSeed() {
        return modelSeed;
    }

    public void setModelSeed(Integer modelSeed) {
        this.modelSeed = modelSeed;
    }

    public Integer getMakeTemplateCount() {
        return makeTemplateCount;
    }

    public void setMakeTemplateCount(Integer makeTemplateCount) {
        this.makeTemplateCount = makeTemplateCount;
    }

    public Integer getTplConvertVipOunt() {
        return tplConvertVipOunt;
    }

    public void setTplConvertVipOunt(Integer tplConvertVipOunt) {
        this.tplConvertVipOunt = tplConvertVipOunt;
    }

    public Byte getRegion() {
        return region;
    }

    public void setRegion(Byte region) {
        this.region = region;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl == null ? null : coverUrl.trim();
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl == null ? null : sourceUrl.trim();
    }

    public Byte getTempType() {
        return tempType;
    }

    public void setTempType(Byte tempType) {
        this.tempType = tempType;
    }

    public Long getTaskTime() {
        return taskTime;
    }

    public void setTaskTime(Long taskTime) {
        this.taskTime = taskTime;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getShelveTime() {
        return shelveTime;
    }

    public void setShelveTime(String shelveTime) {
        this.shelveTime = shelveTime;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
}