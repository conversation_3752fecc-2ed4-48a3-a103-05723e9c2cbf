package com.wbgame.pojo.clean;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("素材VO")
public class ArticleManageDTO extends GenericQueryParameters {


    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("项目ID")
    private String prjId;

    @ApiModelProperty("产品ID")
    private String appId;

    @ApiModelProperty("封面图")
    private String backUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("地区 0 国内 1 国外")
    private Byte areaType;

    @ApiModelProperty("是否需要会员解锁 0 否 1 是")
    private Byte vipFlag;

    @ApiModelProperty("模块中的Tab类型(知识Tab、膳食食谱)")
    private Byte tabType;

    @ApiModelProperty("模块类型 1 知识Tab 2 计划列表页 3 我的-体重页")
    private Byte articleType;

    @ApiModelProperty("模块类型为知识tab时，知识Tab序号")
    private Integer articleSeq;

    @ApiModelProperty("删除标识 0 否 1 是")
    private Byte idDelete;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间(毫秒时间戳)")
    private Long createTime;

    @ApiModelProperty("修改人")
    private String modifier;

    @ApiModelProperty("修改时间")
    private Long modifyTime;


    @ApiModelProperty("素材内容上传地址")
    private String picUrlList;

    @ApiModelProperty("id集合")
    private List<Long> idList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId == null ? null : prjId.trim();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl == null ? null : backUrl.trim();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public Byte getAreaType() {
        return areaType;
    }

    public void setAreaType(Byte areaType) {
        this.areaType = areaType;
    }

    public Byte getVipFlag() {
        return vipFlag;
    }

    public void setVipFlag(Byte vipFlag) {
        this.vipFlag = vipFlag;
    }

    public Byte getTabType() {
        return tabType;
    }

    public void setTabType(Byte tabType) {
        this.tabType = tabType;
    }

    public Byte getArticleType() {
        return articleType;
    }

    public void setArticleType(Byte articleType) {
        this.articleType = articleType;
    }

    public Integer getArticleSeq() {
        return articleSeq;
    }

    public void setArticleSeq(Integer articleSeq) {
        this.articleSeq = articleSeq;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Byte getIdDelete() {
        return idDelete;
    }

    public void setIdDelete(Byte idDelete) {
        this.idDelete = idDelete;
    }

    public String getPicUrlList() {
        return picUrlList;
    }

    public void setPicUrlList(String picUrlList) {
        this.picUrlList = picUrlList;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }
}