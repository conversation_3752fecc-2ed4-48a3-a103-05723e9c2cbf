package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("商品管理")
public class WifiProductConfigVO {
    private Integer id;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道")
    private String cha;

    /**
     * 商品名
     */
    @ApiModelProperty("商品名")
    private String productName;

    /**
     * 商品价格
     */
    @ApiModelProperty("商品价格")
    private String productPrice;

    /**
     *订阅周期：1周 2月  3季  4半年  5年
     */
    @ApiModelProperty("订阅周期：1周 2月  3季  4半年  5年")
    private Byte period;

    /**
     *免费试用天数
     */
    @ApiModelProperty("免费试用天数")
    private String feeDay;

    /**
     *平台产品id
     */
    @ApiModelProperty("平台产品id")
    private String platformProductId;


    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改时间")
    private String modifyTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("修改人")
    private String modifyUser;

    /**
     *状态； 1开启 0关闭
     */
    @ApiModelProperty("状态； 1开启 0关闭")
    private String status;

    /**
     *商品描述
     */
    @ApiModelProperty("商品描述")
    private String productDescribe;

    @ApiModelProperty("货币类型")
    private String moneyType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCha() {
        return cha;
    }

    public void setCha(String cha) {
        this.cha = cha == null ? null : cha.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(String productPrice) {
        this.productPrice = productPrice == null ? null : productPrice.trim();
    }

    public Byte getPeriod() {
        return period;
    }

    public void setPeriod(Byte period) {
        this.period = period;
    }

    public String getFeeDay() {
        return feeDay;
    }

    public void setFeeDay(String feeDay) {
        this.feeDay = feeDay == null ? null : feeDay.trim();
    }

    public String getPlatformProductId() {
        return platformProductId;
    }

    public void setPlatformProductId(String platformProductId) {
        this.platformProductId = platformProductId == null ? null : platformProductId.trim();
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime == null ? null : modifyTime.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getProductDescribe() {
        return productDescribe;
    }

    public void setProductDescribe(String productDescribe) {
        this.productDescribe = productDescribe == null ? null : productDescribe.trim();
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType == null ? null : moneyType.trim();
    }
}