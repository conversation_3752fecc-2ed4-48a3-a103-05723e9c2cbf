package com.wbgame.pojo.clean;

/**
 * 趣拍 早安相册
 * <AUTHOR>
 * @date: 2021年6月04日
 */
public class QpSignTemp  {
	private String id;//'主键',
	private String appid ;
    private String temp_title;//模板标题
    
    private String video_url;//视频地址
    private String video_width;//视频宽度
    private String video_height;//
    
    private String img_url;//图片地址
    private String img_width;//图片宽度
    private String img_height;//图片高度
    
    private String res_url;//压缩地址
    private String res_size;//压缩宽度
    private String res_md5;//压缩高度
 
    private String like_cnt;//点赞数
    private String type;//1大头  2人像 3相册 
    private String plcolor;//占位颜色
    
    private String micro_video_url;//微预览视频url
    private String micro_video_width;//微预览视频宽度
    private String micro_video_height;//微预览视频高度
    
    private String temp_type;//'模板类型  1：早安模板  2：相册模板',
    
    private String material_url;//素材地址
    private String sort;//排序值
    private String describe; //引导描述
    
    private String desc_text;//标签描述
    private String tag_text; //标签定制化名
    private String vipTemp;
    
    private String creatTime;
    private String modifyTime;
    private String modifyUser;
    private String creatUser;
    private String status;
    
    
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTemp_title() {
		return temp_title;
	}
	public void setTemp_title(String temp_title) {
		this.temp_title = temp_title;
	}
	public String getVideo_url() {
		return video_url;
	}
	public void setVideo_url(String video_url) {
		this.video_url = video_url;
	}
	public String getVideo_width() {
		return video_width;
	}
	public void setVideo_width(String video_width) {
		this.video_width = video_width;
	}
	public String getVideo_height() {
		return video_height;
	}
	public void setVideo_height(String video_height) {
		this.video_height = video_height;
	}
	public String getImg_url() {
		return img_url;
	}
	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}
	public String getImg_width() {
		return img_width;
	}
	public void setImg_width(String img_width) {
		this.img_width = img_width;
	}
	public String getImg_height() {
		return img_height;
	}
	public void setImg_height(String img_height) {
		this.img_height = img_height;
	}
	public String getRes_url() {
		return res_url;
	}
	public void setRes_url(String res_url) {
		this.res_url = res_url;
	}
	public String getRes_size() {
		return res_size;
	}
	public void setRes_size(String res_size) {
		this.res_size = res_size;
	}
	public String getRes_md5() {
		return res_md5;
	}
	public void setRes_md5(String res_md5) {
		this.res_md5 = res_md5;
	}
	public String getLike_cnt() {
		return like_cnt;
	}
	public void setLike_cnt(String like_cnt) {
		this.like_cnt = like_cnt;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getPlcolor() {
		return plcolor;
	}
	public void setPlcolor(String plcolor) {
		this.plcolor = plcolor;
	}
	public String getMicro_video_url() {
		return micro_video_url;
	}
	public void setMicro_video_url(String micro_video_url) {
		this.micro_video_url = micro_video_url;
	}
	public String getMicro_video_width() {
		return micro_video_width;
	}
	public void setMicro_video_width(String micro_video_width) {
		this.micro_video_width = micro_video_width;
	}
	public String getMicro_video_height() {
		return micro_video_height;
	}
	public void setMicro_video_height(String micro_video_height) {
		this.micro_video_height = micro_video_height;
	}
	public String getTemp_type() {
		return temp_type;
	}
	public void setTemp_type(String temp_type) {
		this.temp_type = temp_type;
	}
	public String getMaterial_url() {
		return material_url;
	}
	public void setMaterial_url(String material_url) {
		this.material_url = material_url;
	}
	public String getSort() {
		return sort;
	}
	public void setSort(String sort) {
		this.sort = sort;
	}
	public String getDescribe() {
		return describe;
	}
	public void setDescribe(String describe) {
		this.describe = describe;
	}
	public String getDesc_text() {
		return desc_text;
	}
	public void setDesc_text(String desc_text) {
		this.desc_text = desc_text;
	}
	public String getTag_text() {
		return tag_text;
	}
	public void setTag_text(String tag_text) {
		this.tag_text = tag_text;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getCreatUser() {
		return creatUser;
	}
	public void setCreatUser(String creatUser) {
		this.creatUser = creatUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getVipTemp() {
		return vipTemp;
	}
	public void setVipTemp(String vipTemp) {
		this.vipTemp = vipTemp;
	}
    
}