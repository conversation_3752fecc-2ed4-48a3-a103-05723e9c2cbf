package com.wbgame.pojo.clean.stepcount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("跳转等待vo")
public class StepCountConfigVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("产品ID")
    private String appid;

    @ApiModelProperty("渠道")
    private String cha;

    @ApiModelProperty("项目ID")
    private String prjid;

    @ApiModelProperty("splash")
    private Integer splash;

    @ApiModelProperty("plaque")
    private Integer plaque;

    @ApiModelProperty("video")
    private Integer video;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改人")
    private String modifyUser;

    @ApiModelProperty("修改时间")
    private Long modifyTime;

    @ApiModelProperty("1开启0关闭")
    private String status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getCha() {
        return cha;
    }

    public void setCha(String cha) {
        this.cha = cha == null ? null : cha.trim();
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid == null ? null : prjid.trim();
    }

    public Integer getSplash() {
        return splash;
    }

    public void setSplash(Integer splash) {
        this.splash = splash;
    }

    public Integer getPlaque() {
        return plaque;
    }

    public void setPlaque(Integer plaque) {
        this.plaque = plaque;
    }

    public Integer getVideo() {
        return video;
    }

    public void setVideo(Integer video) {
        this.video = video;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }
}