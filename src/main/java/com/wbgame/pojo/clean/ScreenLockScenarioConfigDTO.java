package com.wbgame.pojo.clean;


import com.wbgame.common.GeneralReportParam;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

@ApiModel("V2锁屏配置")
public class ScreenLockScenarioConfigDTO extends GeneralReportParam {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("txt")
    @NotBlank(message = "txt不能为空", groups = {Default.class})
    @Length(message = "超过长度范围", max = 50)
    private String txt;

    @ApiModelProperty("val")
    @Length(message = "超过长度范围", max = 50, groups = Default.class)
    @NotBlank(message = "val不能为空", groups = {Default.class})
    private String val;

    @ApiModelProperty("类型")
    @Length(message = "超过长度范围", max = 20, groups = {Default.class, UpdateGroup.class})
    @NotBlank(message = "type不能为空", groups = {UpdateGroup.class, Default.class})
    private String type;

    @ApiModelProperty("状态 1:开启 0:关闭")
    @NotNull(message = "状态不能为空", groups = {Default.class, UpdateGroup.class})
    @Range(max = 1, groups = {Default.class, UpdateGroup.class})
    private Byte status;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("操作时间")
    private String updateTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("操作人")
    private String updateUser;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTxt() {
        return txt;
    }

    public void setTxt(String txt) {
        this.txt = txt == null ? null : txt.trim();
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val == null ? null : val.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}