package com.wbgame.pojo.clean;

/**
 * 清理王-deeplink新版配置
 * <AUTHOR>
 * @date: 2021年3月05日
 */
public class SuperDeeplinkNew {
	private String dpId;//'deeplink唯一标识主键自增',
    private String pkg;//包名
    private String dpUrl;//拉起地址
    private String dpModel;//拉起模式
    private String normalPer;//正常模式比例  
    private String noFellPer; //无感知模式比例
    private String status;//开启状态，0：关闭；1：开启
    private String creatTime;
    private String modifyTime;
    private String modifyUser;
	public String getDpId() {
		return dpId;
	}
	public void setDpId(String dpId) {
		this.dpId = dpId;
	}
	public String getPkg() {
		return pkg;
	}
	public void setPkg(String pkg) {
		this.pkg = pkg;
	}
	public String getDpUrl() {
		return dpUrl;
	}
	public void setDpUrl(String dpUrl) {
		this.dpUrl = dpUrl;
	}
	public String getDpModel() {
		return dpModel;
	}
	public void setDpModel(String dpModel) {
		this.dpModel = dpModel;
	}
	public String getNormalPer() {
		return normalPer;
	}
	public void setNormalPer(String normalPer) {
		this.normalPer = normalPer;
	}
	public String getNoFellPer() {
		return noFellPer;
	}
	public void setNoFellPer(String noFellPer) {
		this.noFellPer = noFellPer;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public SuperDeeplinkNew(String pkg) {
		super();
		this.pkg = pkg;
	}
	public SuperDeeplinkNew() {
		super();
	}
	
}