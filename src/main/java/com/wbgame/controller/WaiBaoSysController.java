package com.wbgame.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.slave.WaiBaoSysMapper;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;

// -待删除
//@Controller
public class WaiBaoSysController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private WaiBaoSysMapper wbMapper;
	@Autowired
	private AdMapper adMapper;
	
	@CrossOrigin
	@RequestMapping(value="/waibao/login", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getinfo(HttpServletRequest req){
		try {
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONObject obj = JSONObject.parseObject(value);
			if(BlankUtils.checkBlank(obj.getString("uname"))
				|| BlankUtils.checkBlank(obj.getString("pwd"))){
				return "{\"ret\":0,\"msg\":\"request value is error!\"}";
			}
			
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("login_name", obj.getString("uname"));
			param.put("password", obj.getString("pwd"));
			
			WaibaoUserVo user = wbMapper.selectWbSysUser(param);
			JSONObject result = new JSONObject();
			if(user != null){
				result.put("ret", 1);
				result.put("msg", "success");
				result.put("uname", user.getLogin_name());
				result.put("app_group", CommonUtil.waibaoUserMap.get(user.getLogin_name()).getApp_group());
				
				/*List<WaiBaoSysMenuVo> treeMenu = wbMapper.selectTreeMenu("0");
				if(CommonUtil.userMap.get(user.getLogin_name()).getOrg_id().equals("root")){
					String[] split = user.getMenu_group().split(","); // 菜单组
					List<String> group = Arrays.asList(split); // 换成通过select查询tb_sys_menu_permit表获取
					Iterator<WaiBaoSysMenuVo> iterator = treeMenu.iterator();
					
					while(iterator.hasNext()){
						WaiBaoSysMenuVo next = iterator.next();
						String mid = next.getMid()+"";
						if(!group.contains(mid)){
							iterator.remove(); // 不在菜单组里则移除
						}else{
							// 进一步验证子菜单
							if(next.getSubMenu() != null && next.getSubMenu().size() > 0){
								Iterator<WaiBaoSysMenuVo> it = next.getSubMenu().iterator();
								while(it.hasNext()){
									WaiBaoSysMenuVo sub = it.next();
									String md = sub.getMid()+"";
									if(!group.contains(md)){ // 该子菜单不在菜单组里则移除
										it.remove();
									}
								}
							}
						}
					}
				}
				result.put("data", treeMenu);*/
				
				String randomStr = BlankUtils.getRandomStr(24);
				result.put("token", "waibaotoken"+randomStr);
				
				// 随机字符串作为token令牌校验
				redisTemplate.opsForValue()
					.set("waibaotoken"+randomStr, user, 20 * 60, TimeUnit.SECONDS);
			}else{
				result.put("ret", 0);
				result.put("msg", "fail");
			}
			
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("ret", 0);
			result.put("msg", "错误信息 : "+e.getMessage());
			return result.toJSONString();
		}
		
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/userHandle", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String userHandle(WaibaoUserVo cu, HttpServletRequest req){
		try {
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONObject obj = JSONObject.parseObject(value);
			if(BlankUtils.checkBlank(obj.getString("token"))
				|| BlankUtils.checkBlank(obj.getString("handle"))
				|| BlankUtils.checkBlank(obj.getString("user"))){
				return "{\"ret\":0,\"msg\":\"request value is error!\"}";
			}
			
			WaibaoUserVo cuser = (WaibaoUserVo)redisTemplate.opsForValue().get(obj.getString("token"));
			if(cuser == null){
				return "{\"ret\":2,\"msg\":\"token is error!\"}";
			}else{
				redisTemplate.opsForValue()
					.set(obj.getString("token"), cuser, 20 * 60, TimeUnit.SECONDS);
			}
			
			int res = 0;
			WaibaoUserVo userVo = JSONObject.parseObject(obj.getString("user"), WaibaoUserVo.class);
			if("add".equals(obj.getString("handle"))){
				res = wbMapper.insertWbSysUser(userVo);
			}else if("edit".equals(obj.getString("handle"))){
				res = wbMapper.updateWbSysUser(userVo);
			}else if("del".equals(obj.getString("handle"))){
				res = wbMapper.deleteWbSysUser(userVo);
			}
			JSONObject result = new JSONObject();
			if(res > 0){
				result.put("ret", 1);
				result.put("msg", "success");
			}else{
				result.put("ret", 0);
				result.put("msg", "fail");
			}
			
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("ret", 0);
			result.put("msg", "错误信息 : "+e.getMessage());
			return result.toJSONString();
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/getUserInfo", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getUserInfo(WaibaoUserVo cu, HttpServletRequest request,HttpServletResponse response){
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		
		if(BlankUtils.checkBlank(cu.getToken()))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		WaibaoUserVo cuser = (WaibaoUserVo)redisTemplate.opsForValue().get(cu.getToken());
		if(cuser == null ){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.opsForValue()
				.set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
		}
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<WaibaoUserVo> list = wbMapper.selectWbSysUserInfoList(cu);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/roleHandle", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String roleHandle(WaibaoUserVo cu, HttpServletRequest req){
		try {
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONObject obj = JSONObject.parseObject(value);
			if(BlankUtils.checkBlank(obj.getString("token"))
				|| BlankUtils.checkBlank(obj.getString("handle"))
				|| BlankUtils.checkBlank(obj.getString("user"))){
				return "{\"ret\":0,\"msg\":\"request value is error!\"}";
			}
			
			WaibaoUserVo cuser = (WaibaoUserVo)redisTemplate.opsForValue().get(obj.getString("token"));
			if(cuser == null ){
				return "{\"ret\":2,\"msg\":\"token is error!\"}";
			}else{
				redisTemplate.opsForValue()
					.set(obj.getString("token"), cuser, 20 * 60, TimeUnit.SECONDS);
			}
			
			int res = 0;
			WaibaoUserVo userVo = JSONObject.parseObject(obj.getString("user"), WaibaoUserVo.class);
			if("add".equals(obj.getString("handle"))){
				res = wbMapper.insertWbSysRole(userVo);
			}else if("edit".equals(obj.getString("handle"))){
				res = wbMapper.updateWbSysRole(userVo);
			}else if("del".equals(obj.getString("handle"))){
				res = wbMapper.deleteWbSysRole(userVo);
			}
			JSONObject result = new JSONObject();
			if(res > 0){
				result.put("ret", 1);
				result.put("msg", "success");
			}else{
				result.put("ret", 0);
				result.put("msg", "fail");
			}
			
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("ret", 0);
			result.put("msg", "错误信息 : "+e.getMessage());
			return result.toJSONString();
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/getRoleInfo", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getRoleInfo(WaibaoUserVo cu, HttpServletRequest request,HttpServletResponse response){
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		
		if(BlankUtils.checkBlank(cu.getToken()))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		WaibaoUserVo cuser = (WaibaoUserVo)redisTemplate.opsForValue().get(cu.getToken());
		if(cuser == null ){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.opsForValue()
				.set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
		}
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<WaibaoUserVo> list = wbMapper.selectWbSysRoleInfoList(cu);
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/changePassword", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String changePassword(WaibaoUserVo cu, HttpServletRequest req){
		try {
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONObject obj = JSONObject.parseObject(value);
			if(BlankUtils.checkBlank(obj.getString("token"))
				|| BlankUtils.checkBlank(obj.getString("old_pwd"))
				|| BlankUtils.checkBlank(obj.getString("new_pwd"))){
				return "{\"ret\":0,\"msg\":\"request value is error!\"}";
			}
			
			WaibaoUserVo cuser = (WaibaoUserVo)redisTemplate.opsForValue().get(obj.getString("token"));
			if(cuser == null || !obj.getString("old_pwd").equals(cuser.getPassword())){
				return "{\"ret\":2,\"msg\":\"token or password is error!\"}";
			}else{
				cuser.setPassword(obj.getString("new_pwd"));
				redisTemplate.opsForValue()
					.set(obj.getString("token"), cuser, 20 * 60, TimeUnit.SECONDS);
			}
			
			int res = wbMapper.updateWbSysUserForPassword(cuser);
			JSONObject result = new JSONObject();
			if(res > 0){
				result.put("ret", 1);
				result.put("msg", "success");
			}else{
				result.put("ret", 0);
				result.put("msg", "fail");
			}
			
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("ret", 0);
			result.put("msg", "错误信息 : "+e.getMessage());
			return result.toJSONString();
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/waibao/getUserLoginLog", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String getUserLoginLog(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String tdate = request.getParameter("tdate");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(tdate)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			if(BlankUtils.checkBlank(tdate)){
				tdate = DateTime.now().toString("yyyy-MM-dd");
			}
						
			String sql = "select * from main_sys_login_log where DATE_FORMAT(create_time,'%Y-%m-%d') = '"+tdate+"'";
			sql = sql + " order by logid desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adMapper.queryListMap(sql);
			list.forEach(map -> {
				
				map.put("create_time", map.get("create_time")+"");
				if(!BlankUtils.checkBlank(map.get("client_ip").toString())){
					try {
						String[] find = CommonUtil.cityUtil.find(map.get("client_ip").toString());
						if(find.length >= 3){
							map.put("client_place", find[1]+find[2]);
						}else if(find.length >= 2){
							map.put("client_place", find[1]);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
}
