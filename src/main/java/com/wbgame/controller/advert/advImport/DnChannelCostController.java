package com.wbgame.controller.advert.advImport;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnAvgPriceVo;
import com.wbgame.pojo.DnChannelCost;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import com.wbgame.utils.excel.ExportExcelWrapper;

import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 成本数据导入
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/dnChannelCost")
public class DnChannelCostController {

    @Autowired
    private SomeService someService;

    @Autowired
    private SomeMapper someMapper;

    @Autowired
    private YdMapper ydMapper;

    /**
     * 导入
     */
    @RequestMapping(value="/batchImport", method={RequestMethod.GET,RequestMethod.POST})
    public String batchImport(@RequestParam(value="fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject obj = new JSONObject();
        int error = 0 ;
        try{
            if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")){
                List<DnChannelCost> list = new ArrayList<DnChannelCost>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    error = r + 1;
                    if(BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[7];
                    for (int c = 0; c < 7; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    DnChannelCost gad = new DnChannelCost();
                    gad.setTdate(vals[0]);
                    if(vals[0] != null && !vals[0].isEmpty()){
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                        boolean flag = false;
                        try {
                            if(vals[0].split("-").length != 3
                                    || vals[0].split("-")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setTdate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if(flag){
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy-MM-dd 的格式！");
                            return obj.toString();
                        }
                    }
                    if(vals[1] == null || "".equals(vals[1]) || vals[1].length() == 0){
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行appid有误，请查看");
                        return obj.toString();
                    }else {
                        gad.setAppid(vals[1]);
                    }

                    if(vals[1] == null || "".equals(vals[1]) || vals[1].length() == 0){
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行渠道标识有误，请查看");
                        return obj.toString();
                    }else {
                        gad.setChaId(vals[2]);
                    }

                    if(vals[3] == null || "".equals(vals[3]) || vals[3].length() == 0){
                        gad.setInvestAmount(BigDecimal.ZERO);
                    }else {
                        gad.setInvestAmount(new BigDecimal(vals[3].trim()));
                    }

                    if(vals[4] == null || "".equals(vals[4]) || vals[4].length() == 0){
                        gad.setAdvIncome(BigDecimal.ZERO);
                    }else {
                        gad.setAdvIncome(new BigDecimal(vals[4].trim()));
                    }
                    if(vals[5] == null || "".equals(vals[5]) || vals[5].length() == 0){
                        gad.setBillingIncome(BigDecimal.ZERO);
                    }else {
                        gad.setBillingIncome(new BigDecimal(vals[5].trim()));
                    }

                    List<DnChannelInfo> chaList = someMapper.selectDnChannelRat();
                    for (int i = 0; i < chaList.size(); i++) {
                        if(vals[2].equals(chaList.get(i).getChaId())){
                            BigDecimal rot = new BigDecimal(chaList.get(i).getChaRatio());
                            gad.setInvestAmount(gad.getAdvIncome().multiply(rot));
                            break;
                        }
                    }

                    list.add(gad);
                }
                int code = someService.insertDnChannelCost(list);
                if(-100 == code){
                    obj.put("ret", 0);
                    obj.put("msg", "有收支数据已审核通过，请联系管理员确认是否要覆盖！");
                    return obj.toJSONString();
                }

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            }else{
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        }catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败 第"+error+"行有误，请查看");
        }
        return obj.toJSONString();
    }

    @RequestMapping(value="/batchImportCost", method={RequestMethod.GET,RequestMethod.POST})
    public String importDnChannelCostNew(@RequestParam(value="fileName")MultipartFile file,HttpServletRequest request,HttpServletResponse response) throws IOException{

        JSONObject obj = new JSONObject();
        int error = 0 ;
        try{
            if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")){
                List<DnChannelCost> list = new ArrayList<DnChannelCost>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    error = r + 1;
                    if(BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[7];
                    for (int c = 0; c < 7; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    DnChannelCost gad = new DnChannelCost();
                    gad.setTdate(vals[0]);
                    if(vals[0] != null && !vals[0].isEmpty()){
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                        boolean flag = false;
                        try {
                            if(vals[0].split("-").length != 3
                                    || vals[0].split("-")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            gad.setTdate(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if(flag){
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy-MM-dd 的格式！");
                            return obj.toString();
                        }
                    }
                    if(vals[1] == null || "".equals(vals[1]) || vals[1].length() == 0){
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行appid有误，请查看");
                        return obj.toString();
                    }else {
                        gad.setAppid(vals[1]);
                    }

                    if(vals[2] == null || "".equals(vals[2]) || vals[2].length() == 0){
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行渠道标识有误，请查看");
                        return obj.toString();
                    }else {
                        gad.setChaId(vals[2]);
                    }

                    if(vals[3] == null || "".equals(vals[3]) || vals[3].length() == 0){
                        gad.setInvestAmount(BigDecimal.ZERO);
                    }else {
                        gad.setInvestAmount(new BigDecimal(vals[3].trim()));
                    }

                    if(vals[4] == null || "".equals(vals[4]) || vals[4].length() == 0){
                        gad.setAdvIncome(BigDecimal.ZERO);
                    }else {
                        gad.setAdvIncome(new BigDecimal(vals[4].trim()));
                    }
                    if(vals[5] == null || "".equals(vals[5]) || vals[5].length() == 0){
                        gad.setBillingIncome(BigDecimal.ZERO);
                    }else {
                        gad.setBillingIncome(new BigDecimal(vals[5].trim()));
                    }

                    List<DnChannelInfo> chaList = someMapper.selectDnChannelRat();
                    for (int i = 0; i < chaList.size(); i++) {
                        if(vals[2].equals(chaList.get(i).getChaId())){
                            BigDecimal rot = new BigDecimal(chaList.get(i).getChaRatio());
                            gad.setInvestAmount(gad.getAdvIncome().multiply(rot));
                            break;
                        }
                    }

                    list.add(gad);
                }
                int code = someMapper.insertDnChannelCost(list);

                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
            }else{
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls文件!");
            }

        }catch (Exception e) {
            //上传异常
            e.printStackTrace();
            obj.put("ret", 0);
            obj.put("msg", "导入失败 第"+error+"行有误，请查看");
        }
        return obj.toJSONString();
    }

    /**
     * 友盟渠道成本
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/list", method={RequestMethod.GET,RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("appid", request.getParameter("appid"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("chaId", request.getParameter("chaId"));

        JSONObject result = new JSONObject();

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<DnChannelCost> list = someService.selectDnChannelCost(map);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 友盟渠道成本导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET,RequestMethod.POST})
    public String export(HttpServletRequest request,HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("appid", request.getParameter("appid"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("chaId", request.getParameter("chaId"));
        // 数据内容
        List<DnChannelCost> list = someService.selectDnChannelCost(map);

        
        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;

        /*for (DnChannelCost temp : list) {
            headerMap.put("tdate", "日期");
            headerMap.put("appid", "appid");
            headerMap.put("appidName", "产品名");
            headerMap.put("chaId", "渠道标识");
            headerMap.put("investAmount", "投放金额");
            headerMap.put("advIncome", "广告收入");
            headerMap.put("billingIncome", "计费收入");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("appid", temp.getAppid());
            contentMap.put("appidName", temp.getAppidName());
            contentMap.put("chaId", temp.getChaId());
            contentMap.put("investAmount", temp.getInvestAmount());
            contentMap.put("advIncome", temp.getAdvIncome());
            contentMap.put("billingIncome", temp.getBillingIncome());

            contentList.add(contentMap);
        }

        String fileName = "友盟渠道成本_" + DateTime.now().toString("yyyyMMdd")+".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);*/
        
        /** 新版本导出，poi形式默认按照字段名顺序 */
        ExportExcelWrapper<DnChannelCost> doc = new ExportExcelWrapper<DnChannelCost>();
        String[] columnNames = {"日期","游戏名称","appid","子渠道标识","投放金额","广告收入","计费收入" };
        try {
        	doc.exportExcel("成本数据导入_","成本数据导入", columnNames, list, response, response.getOutputStream(), ExportExcelWrapper.EXCEl_FILE_2007, "yyyy-MM-dd");
		} catch (Exception e) {
			e.printStackTrace();
		}

        return null;
    }


    /**
     * 同步成本数据至产品收支汇总
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/syncDnChaCostToTotal", method={RequestMethod.GET,RequestMethod.POST})
    public String syncDnChaCostToTotal(String tdate, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 同步投放金额和广告收入，从wb_pay_info表中获取计费收入
            someMapper.insertDnAppIncomeTotal(tdate);
            someMapper.insertDnAppIncomeTotalBilling(tdate);
            
            /* 同时同步到合作方收入表 */
            someMapper.insertDnChannelCostTwoBilling(tdate);

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    
    @RequestMapping(value = "/sycDnAvgPrice", method = {RequestMethod.GET, RequestMethod.POST})
    public String sycDnAvgPrice(String startTime, HttpServletRequest request, HttpServletResponse response) {
        int r = 0;
        try {
            //按产品和渠道分组查询出分组后的总活跃出每个活跃用户的单价
            List<DnAvgPriceVo> list = ydMapper.selectDnAvgPrice(startTime);
            if (list == null || list.size() == 0) {
                return "{\"ret\":0,\"msg\":\"新增活跃汇总查询或成本导入表找不到" + startTime + "的数据!请检查！\"}";
            }
            //跟新单价
            ydMapper.updateDnChaDauTotalPrice(list);

            r = ydMapper.insertDnCpsReport(ydMapper.selectDnChaDauTotalPrice(startTime));

            if (r > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

}
