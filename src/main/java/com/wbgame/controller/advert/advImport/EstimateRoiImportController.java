package com.wbgame.controller.advert.advImport;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.NewPageLimit;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.pojo.jettison.AppleRoiReportVo;
import com.wbgame.pojo.operate.TokenManageVo;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.*;
import jxl.Sheet;
import jxl.Workbook;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/21
 * @description
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/estimateRoi")
@Api(tags = "ROI预估需求页面导入")
@ApiSupport(author = "shenl")
public class EstimateRoiImportController {

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;


    @RequestMapping(value = "/import", method = {RequestMethod.POST}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "ROI预估需求页面导入", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = String.class),
            @ApiResponse(code = 1, message = "操作成功", response = String.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form"),
            @ApiImplicitParam(name = "fileName", value = "文件名", required = true, dataType = "string", paramType = "form"),
            @ApiImplicitParam(name = "tag", value = "文件统一第三列填充值", required = true, dataType = "string", paramType = "form")
    })
    public Result<String> importData(@ApiNeed("file") @RequestPart("file") MultipartFile file,
                                     @ApiNeed("fileName") @RequestParam("fileName") String fileName,
                                     @ApiNeed("tag") @RequestParam("tag") String tag) {

        if (null != file && !file.isEmpty() && (file.getOriginalFilename().endsWith(".xls"))) {
            try {
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                ArrayList<HashMap<String, String>> list = new ArrayList<>();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    try {
                        if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                            continue;
                        HashMap<String, String> map = new HashMap<>();

                        String[] vals = new String[3];
                        for (int c = 0; c < 2; c++) {
                            vals[c] = sheet.getCell(c, r).getContents();
                        }
                        if (BlankUtils.checkBlank(vals[0])
                                || BlankUtils.checkBlank(vals[1])
                        ) {
                            break;
                        }
                        map.put("key", vals[0]);
                        map.put("value", vals[1]);
                        list.add(map);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if (!CollectionUtils.isEmpty(list)) {
                    dnwxBiAdtMapper.insertEstimateRoiList(list, fileName, tag);
                }
                return ResultUtils.success("成功");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            return ResultUtils.failure("上传文件有误，需要.xls文件!");
        }
        return null;
    }
}
