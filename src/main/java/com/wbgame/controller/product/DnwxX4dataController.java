package com.wbgame.controller.product;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.adv2.ExtendAdconfigVo;
import com.wbgame.pojo.adv2.ExtendAdposVo;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.product.DnwxX4dataVo;
import com.wbgame.service.AdService;
import com.wbgame.service.product.ProductReportService;
import com.wbgame.utils.BlankUtils;

/**
 * @description: x4配置
 * @author: caow
 * @date: 2023/04/12
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/product")
public class DnwxX4dataController {

    @Autowired
    private ProductReportService productReportService;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * x4配置.查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/x4dataConfig/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(DnwxX4dataVo data,HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"appid","cha_id","prjid","adpos_type","adpos","agent","statu","cuser","euser","order"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = productReportService.selectDnwxX4dataConfig(paramMap);
            long size = ((Page) list).getTotal();
            
            list.forEach(act -> act.put("createtime", act.get("createtime")+""));
            list.forEach(act -> act.put("endtime", act.get("endtime")+""));

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }


    /**
     * x4配置.操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/x4dataConfig/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String handle(DnwxX4dataVo data,HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
        
        int result = 0;
        try {
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
            
            if ("add".equals(request.getParameter("handle"))) {
                result = yyhzMapper.insertDnwxX4dataConfig(data);
                return "{\"ret\":1,\"msg\":\"操作成功!\",\"add_id\":"+data.getId()+"}";

            } else if ("edit".equals(request.getParameter("handle"))) {
                String sql = "update dnwx_cfg.dn_extend_x4data_config set appid=#{obj.appid},cha_id=#{obj.cha_id},prjid=#{obj.prjid},adpos_type=#{obj.adpos_type},agent=#{obj.agent},ecpm=#{obj.ecpm},adpos=#{obj.adpos},bidding=#{obj.bidding},convert_type=#{obj.convert_type},adving=#{obj.adving},adving_show=#{obj.adving_show},preset_ctr=#{obj.preset_ctr},zc_back=#{obj.zc_back},mn_back=#{obj.mn_back},day_click=#{obj.day_click},sum_click=#{obj.sum_click},delay_click=#{obj.delay_click},statu=#{obj.statu},endtime=now(),euser=#{obj.euser} where id in ("+data.getId()+") ";
                result = yyhzMapper.execSqlHandle(sql, data);

            } else if ("del".equals(request.getParameter("handle"))) {
                String sql = "delete from dnwx_cfg.dn_extend_x4data_config where id in ("+data.getId()+") ";
                result = yyhzMapper.execSqlHandle(sql, data);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * x4配置.批量复制操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/x4dataConfig/batchCopy", method={RequestMethod.POST})
    public String batchCopyX4dataConfig(DnwxX4dataVo data,HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));

        // 操作人登录名获取
        data.setCuser(json.getString("login_name"));
        data.setEuser(json.getString("login_name"));

        int result = 0;
        try {
            String ids = request.getParameter("ids");
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(ids)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            JSONObject info = JSONObject.parseObject(JSON.toJSONString(data));

            if(!BlankUtils.checkBlank(ids)){
                String sql = "select * from dnwx_cfg.dn_extend_x4data_config where id in ("+ids+")";
                List<Map<String, Object>> list = yyhzMapper.queryListMap(sql);

                list.forEach(act -> {
                    // 替换内容，取消参数的替换排它性
                    String[] fields = {"appid","cha_id","prjid","adpos_type","agent","ecpm","adpos","bidding","convert_type","adving","adving_show","preset_ctr","zc_back","mn_back","day_click","sum_click","delay_click","statu"};
                    for (String field : fields) {
                        if(info.get(field) != null){
                            act.put(field, info.getString(field));
                        }
                    }
                });

                try {
                    Map<String, Object> paramMap = new HashMap<String, Object>();
                    paramMap.put("sql1", "insert into dnwx_cfg.dn_extend_x4data_config(appid,cha_id,prjid,adpos_type,agent,ecpm,adpos,bidding,convert_type,adving,adving_show,preset_ctr,zc_back,mn_back,day_click,sum_click,delay_click,statu,createtime,cuser,endtime,euser) values ");
                    paramMap.put("sql2", " (#{li.appid},#{li.cha_id},#{li.prjid},#{li.adpos_type},#{li.agent},#{li.ecpm},#{li.adpos},#{li.bidding},#{li.convert_type},#{li.adving},#{li.adving_show},#{li.preset_ctr},#{li.zc_back},#{li.mn_back},#{li.day_click},#{li.sum_click},#{li.delay_click},#{li.statu},now(),'"+data.getCuser()+"',now(),'"+data.getEuser()+"') ");
                    paramMap.put("sql3", " ");
                    paramMap.put("list", list);
                    yyhzMapper.batchExecSql(paramMap);

                    result = 1;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if(result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * x4配置.批量编辑操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/x4dataConfig/batchEdit", method={RequestMethod.POST})
    public String batchEditX4dataConfig(DnwxX4dataVo data,HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));

        // 操作人登录名获取
        data.setCuser(json.getString("login_name"));
        data.setEuser(json.getString("login_name"));

        int result = 0;
        try {
            String ids = request.getParameter("ids");
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(ids)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            JSONObject info = JSONObject.parseObject(JSON.toJSONString(data));
            String set = "endtime=now(),euser='"+data.getCuser()+"'";

            // 以DnwxX4dataVo中字段生成数组对象
            String[] fields = {"appid","cha_id","prjid","adpos_type","agent","ecpm","adpos","bidding","convert_type","adving","adving_show","preset_ctr","zc_back","mn_back","day_click","sum_click","delay_click","statu"};
            for (String field : fields) {
                if(info.get(field) != null){
                    set += ","+field+"='"+info.getString(field)+"'";
                }
            }


            if(!BlankUtils.checkBlank(ids)){

                String sql = "update dnwx_cfg.dn_extend_x4data_config set "+set+" where id in ("+ids+")";
                System.out.println("sql:"+sql);

                result = yyhzMapper.execSql(sql);
            }

            if(result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
    
}
