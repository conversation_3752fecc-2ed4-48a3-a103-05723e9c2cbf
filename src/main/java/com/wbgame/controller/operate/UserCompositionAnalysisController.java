package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.UserCompositionAnalysisDTO;
import com.wbgame.pojo.operate.UserCompositionAnalysisVO;
import com.wbgame.service.adb.IUserCompositionAnalysisService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2023/02/20 020
 * @class: UserCompositionAnalysisController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/userCompositionAnalysisController")
@Api(tags = "用户构成分析报表")
public class UserCompositionAnalysisController {


    private IUserCompositionAnalysisService service;

    public UserCompositionAnalysisController(IUserCompositionAnalysisService service) {
        this.service = service;
    }

    @ApiOperation(value = "用户活跃分31组-查询", notes = "查询")
    @PostMapping("/selectDataList")
    Result<PageResult<UserCompositionAnalysisVO>> selectDataList( @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "start",
            "limit",
            "grpList",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        return service.selectDataList(dto);
    }


    @ApiOperation(value = "用户活跃分31组-导出", notes = "导出")
    @PostMapping("/export1")
    void export1(HttpServletResponse response, @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "export_file_name",
            "value",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        List<UserCompositionAnalysisVO> list = service.export(dto);


        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = dto.getExport_file_name()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);

    }

    @ApiOperation(value = "用户活跃分31组-图", notes = "图:返回数据按照时间进行了分组")
    @PostMapping("/selectDateScheme")
    Result<Map<String, List<UserCompositionAnalysisVO>>> selectDateScheme(HttpServletResponse response, @ApiNeed({
            "start_date",
            "end_date",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        return service.selectDateScheme(dto);
    }

    @ApiOperation(value = "用户活跃分6组-查询", notes = "查询")
    @PostMapping("/selectDataList6")
    Result<PageResult<UserCompositionAnalysisVO>> selectDataList6( @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "start",
            "limit",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        return service.selectDataList6(dto);
    }


    @ApiOperation(value = "用户活跃分6组-导出", notes = "导出")
    @PostMapping("/export6")
    void export6(HttpServletResponse response, @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "export_file_name",
            "value",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        List<UserCompositionAnalysisVO> list = service.export6(dto);


        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = dto.getExport_file_name()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);

    }

    @ApiOperation(value = "用户活跃分6组-图", notes = "图")
    @PostMapping("/selectDateScheme6")
    Result<Map<String, List<UserCompositionAnalysisVO>>> selectDateScheme6(HttpServletResponse response, @ApiNeed({
            "start_date",
            "end_date",
            "appidList"
    }) UserCompositionAnalysisDTO dto) {

        return service.selectDateScheme6(dto);
    }
}
