package com.wbgame.controller.operate;

import com.wbgame.aop.LoginCheck;
import com.wbgame.common.ReturnJson;
import com.wbgame.service.LeyuanService;
import com.wbgame.service.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@RequestMapping(value = "/operate/leyuan")
@Api(tags = "233乐园收入同步")
public class LeyuanController {

    @Autowired
    private RedisService redisService;

    @Autowired
    private LeyuanService leyuanService;

    @RequestMapping(value = "/syn")
    @ApiOperation(value = "同步", notes = "同步233乐园收入数据", httpMethod = "POST")
    @LoginCheck
    public String syn(HttpServletRequest request,
                      @ApiParam(value = "开始时间(yyyy-MM-dd)", required = true) String start,
                      @ApiParam(value = "结束时间(yyyy-MM-dd)", required = true) String end) {
        //验证是否需要锁住方法
        if (redisService.hasKey("leyuan_233_syn_lock")) {
            return ReturnJson.toErrorJson("收入明细表同步执行中,请稍后再试");
        }
        try {
            redisService.set("leyuan_233_syn_lock", "lock", 3600);
            leyuanService.synLeyuanData(start,end);
            return ReturnJson.success("同步执行完成");
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.success("同步执行异常");
        } finally {
            redisService.del("leyuan_233_syn_lock");
        }
    }

}
