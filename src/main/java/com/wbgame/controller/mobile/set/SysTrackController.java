package com.wbgame.controller.mobile.set;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.mobile.UserEventRecord;
import com.wbgame.service.mobile.SysTrackService;
import com.wbgame.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * 系统埋点
 */
@RestController
@RequestMapping("/api/v1")
@CrossOrigin
public class SysTrackController {

    @Autowired
    private SysTrackService sysTrackService;

    /**
     * 系统埋点事件上报存储
     *
     * @param value 埋点数据
     * @return 处理结果
     */
    @ControllerLoggingEnhancer
    @RequestMapping("/track")
    public Object trackEvent(String value) {
        if (StringUtils.isEmpty(value)) {
            return ResultUtils.failure(Constants.isNotNull);
        }
        JSONObject object;
        try {
            //上报数据解密
            String json = new String(Base64.decodeBase64(value.replace(" ", "+")), "UTF-8");
            object = JSONObject.parseObject(json);
        } catch (UnsupportedEncodingException e) {
            return ResultUtils.failure(Constants.ParamError);
        }
        //token校验
        String userName = LOGIN_USER_NAME.get();
        if (StringUtils.isEmpty(userName)) {
            return ResultUtils.failure(Constants.ErrorToken);
        }
        //封装当前操作人
        object.put("userId",userName);
        //数据解析追踪
        return sysTrackService.trackEvent(object);
    }


    /**
     * 拉取系统埋点缓存数据至数据库表中
     *
     * @return 处理结果
     */
    @RequestMapping("/pullRedisEvents")
    public Result<String> pullRedisEvents() {
        return sysTrackService.pullRedisEvents();
    }


    /**
     * 拉取系统埋点缓存数据至数据库表中
     *
     * @return 处理结果
     */
    @RequestMapping("/list")
    public Result<List<UserEventRecord>> queryList(UserEventRecord dto, @RequestParam(value = "start") int start, @RequestParam(value = "limit") int limit) {
        return sysTrackService.queryList(dto, start, limit);
    }


}
