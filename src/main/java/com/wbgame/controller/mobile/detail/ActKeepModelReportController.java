package com.wbgame.controller.mobile.detail;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.DataReportMapper;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 渠道活跃留存分析报表（设备）
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/actKeepModelReport")
public class ActKeepModelReportController {

    @Resource
    private DataReportMapper dataReportMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(HttpServletRequest request, HttpServletResponse response){
        //处理请求参数
        Map<String, Object> param = handleParams(request);
        //查询
        List<Map<String,Object>> list = dataReportMapper.selectActKeepModelReport(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("total",((Page) list).getTotal());
        return ReturnJson.success(result);
    }


    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        //处理请求参数
        Map<String, Object> param = handleParams(request);
        //查询
        List<Map<String,Object>> list = dataReportMapper.selectActKeepModelReport(param);
        //自定义列
        String value = request.getParameter("value");
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "设备活跃留存";
        }
        ExportExcelUtil.exportXLSX(response,list,head,export_file_name+ "_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }

    /**
     * 处理参数
     * @param request
     * @return
     */
    public Map<String,Object> handleParams(HttpServletRequest request){
        String appid = request.getParameter("appid");
        String model = request.getParameter("model");
        String channel = request.getParameter("channel");
        String version = request.getParameter("version");
        String group = request.getParameter("group");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String order = request.getParameter("order");
        if (BlankUtils.checkBlank(group)) {
            group = "tdate,appid,download_channel,app_ver,model";
        }
        if (group.contains("model")) {
            group = group+",brand";
        }
        Map<String,Object> param = new HashMap<>();
        param.put("appid",appid);
        param.put("model", model);
        param.put("channel",channel);
        param.put("version",version);
        param.put("group",group);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("order",order);
        //品牌分组去掉机型
        String bgroup = group.replace("model", "").replace(",,", ",");
        if (bgroup.endsWith(",")) {
            bgroup = bgroup.substring(0,bgroup.length() - 1);
        }
        param.put("bgroup",bgroup);
        //根据分组拼接连表参数
        String[] bgroups = bgroup.split(",");
        String join = "";
        for (String s : bgroups) {
            join = join + "a."+s + "=" + "b" + s + " and ";
        }
        param.put("join",join.substring(0,join.length()-5));
        return param;
    }

}
