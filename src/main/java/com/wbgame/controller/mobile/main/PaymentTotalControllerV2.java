package com.wbgame.controller.mobile.main;

import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.game.report.query.PayTotalInfoQueryVo;
import com.wbgame.pojo.game.report.query.PayTotalQueryVo;
import com.wbgame.service.game.GamePayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 支付汇总查询
 * @author: huangmb
 * @date: 2021/02/09
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/paymentTotalNew")
@Api(tags ="支付汇总查询")
public class PaymentTotalControllerV2 {

    @Resource
    private GamePayService gamePayService;


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("list")
    @LoginCheck
    public String list(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "payidList",
            "pid",
            "order_str",
            "group_idList",
            "start",
            "limit",
    }) @Validated(QueryGroup.class) PayTotalQueryVo query) {
        return gamePayService.getPayTotalReportList(query);
    }



    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value="export")
    @LoginCheck
    public void list(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "payidList",
            "pid",
            "order_str",
            "group_idList",
            "value",
            "export_file_name",
    }) @Validated(QueryGroup.class) PayTotalQueryVo query,HttpServletResponse response) {
        gamePayService.exportPayTotalReportList(query,response);
    }

    @ApiOperation(value = "详情", notes = "详情")
    @PostMapping(value="info")
    @LoginCheck
    public String info(@ApiNeed({
            "start_date",
            "end_date",
            "appid",
            "paytid",
            "pid",
            "order_str",
    }) @Validated(QueryGroup.class) PayTotalInfoQueryVo query)  {
        return gamePayService.getPayTotalDetailList(query);
    }

    @PostMapping("sync")
    @LoginCheck
    @ApiOperation(value = "拉取", notes = "支付汇总数据拉取", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start_date", value = "开始时间", example = "yyyy-MM-dd", dataType = "String"),
            @ApiImplicitParam(name = "end_date", value = "结束时间", example = "yyyy-MM-dd", dataType = "String"),
    })
    public String sync(@RequestParam(name = "start_date", defaultValue = "开始时间") String start_date,
                           @RequestParam(name = "end_date", defaultValue = "结束时间") String end_date
                         ){
        try {
            gamePayService.syncPayTotalPidData(start_date,end_date);
            gamePayService.syncPayTotalDauData(start_date,end_date);
            return ReturnJson.success();
        }catch (Exception e){
            return ReturnJson.toErrorJson("拉取数据异常");
        }
    }

}
