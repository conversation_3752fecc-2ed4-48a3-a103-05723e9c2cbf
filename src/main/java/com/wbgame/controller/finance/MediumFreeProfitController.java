package com.wbgame.controller.finance;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.aop.LoginCheck;
import com.wbgame.aop.SyncFeiShuSend;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.adv2.DnExtendGroupMonitorVo;
import com.wbgame.pojo.finance.MediumFreeIProfitVo;
import com.wbgame.pojo.finance.MediumFreeImportVo;
import com.wbgame.service.AdService;
import com.wbgame.service.finance.MediumFreeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @author: caow
 * @date: 2023/05/24
 * @description: 中休产品毛利收入
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/finance")
public class MediumFreeProfitController {

    @Autowired
    private MediumFreeService mediumFreeService;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private HttpServletRequest request;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/mediumFreeProfit/list", method={RequestMethod.GET, RequestMethod.POST})
    public String mediumFreeProfit(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"sdate","edate","bus_port","app_category","appid","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(paramMap);
            List<MediumFreeIProfitVo> list = mediumFreeService.selectMediumFreeProfitList(paramMap);
            long size = ((Page) list).getTotal();


            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("total", mediumFreeService.selectMediumFreeProfitListSum(paramMap));
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }


    /**
     * 导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mediumFreeProfit/export", method={RequestMethod.GET, RequestMethod.POST})
    public void mediumFreeProfit_export(HttpServletRequest request,HttpServletResponse response) {

        String[] args = {"sdate","edate","bus_port","app_category","appid","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);


        List<MediumFreeIProfitVo> list = mediumFreeService.selectMediumFreeProfitList(paramMap);
        Map<String, Map<String, Object>> appCategoryMap = yyhzMapper.getAppCategoryMap();

        List<Map<String, Object>> contentList = new ArrayList<Map<String,Object>>();
        for (MediumFreeIProfitVo act : list) {
            Map<String, Object> obj = (Map<String, Object>)JSONObject.parseObject(JSONObject.toJSONString(act), Map.class);

            Map<String, Object> category = appCategoryMap.get(act.getAppid());
            if(category != null){
                obj.put("app_name", category.get("app_name"));
                obj.put("app_category_name", category.get("app_category_name"));
            }

            contentList.add(obj);
        }

        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "中休产品毛利收入_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
    }

    /**
     * 同步
     * @return
     */
    @RequestMapping(value="/mediumFreeProfit/sync", method={RequestMethod.POST})
    @LoginCheck
    @SyncFeiShuSend(defaultPage = "中休产品毛利收入",isSendError = true)
    public String mediumFreeProfit_sync(String sdate, String edate) {
        if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
            return ReturnJson.toErrorJson("同步失败，请检查日期格式是否正常！");
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("sdate", sdate);
        paramMap.put("edate", edate);
        int res = mediumFreeService.syncMediumFreeProfitInfo(paramMap);
        if(res >= 0) {
            return ReturnJson.success("同步成功！");
        }else {
            return ReturnJson.toErrorJson("同步失败，请稍后重试！");
        }
    }

}
