package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.GeneralReportParam;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.adv2.YyhzService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时汇总，数据报表相关
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/adv2")
public class GroupReportController {
	
	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private Adv2Service adv2Service;
	@Autowired
	private AdService adService;
	@Autowired
	private YyhzService yyhzService;
	
	
	/**
	 * SDK版本监控.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/selectSdkRelationReport", method={RequestMethod.GET, RequestMethod.POST})
	public String selectSdkRelationReport(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		String[] args = {"sdate","edate","sdk_name","ver","local_ver","appGroup","appid","cha_id","prjid","order_str", "temp_id"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		JSONObject result = new JSONObject();
		try {
			
			PageHelper.startPage(paramMap);
			List<Map<String, Object>> list = adv2Service.selectSdkRelationReport(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	/**
	 * SDK版本监控.导出
	 * @param request
	 * @param response
	 * @return
	 */ 
	@RequestMapping(value = "/app/exportSdkRelationReport", method={RequestMethod.GET, RequestMethod.POST})
	public void exportSdkRelationReport(HttpServletRequest request, HttpServletResponse response, GeneralReportParam param) {
		
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		
		String[] args = {"sdate","edate","sdk_name","ver","local_ver","appGroup","appid","cha_id","prjid","order_str", "temp_id"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		List<Map<String, Object>> contentList = adv2Service.selectSdkRelationReport(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});
		
		headerMap.put("tdate","日期");
		headerMap.put("appname","应用");
		headerMap.put("cha_id","子渠道");
		headerMap.put("prjid","项目ID");
		headerMap.put("sdk_name","平台sdk");
		headerMap.put("ver","sdk版本");
		headerMap.put("local_ver","本地版本");
		headerMap.put("num","覆盖用户量");
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
//		String fileName = "SDK版本监控_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);


		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getExport_file_name()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
	}
	
	/**
	 * 异常数据监控.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/app/selectExtendClickWarn", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendClickWarn(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return "{\"ret\":2,\"msg\":\"token is error!\"}";
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		String[] args = {"sdate","edate","appid","appGroup","cha_id","event","order_str"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		JSONObject result = new JSONObject();
		try {
			
			PageHelper.startPage(paramMap);
			List<Map<String, Object>> list = yyhzService.selectExtendClickWarn(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	/**
	 * 异常数据监控.导出
	 * @param request
	 * @param response
	 * @return
	 */ 
	@RequestMapping(value = "/app/exportExtendClickWarn", method={RequestMethod.GET, RequestMethod.POST})
	public void exportExtendClickWarn(HttpServletRequest request,HttpServletResponse response) {
		
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		
		String[] args = {"sdate","edate","appid","appGroup","cha_id","event","order_str"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		List<Map<String, Object>> contentList = yyhzService.selectExtendClickWarn(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});
		
		headerMap.put("tdate","日期");
		headerMap.put("appname","应用");
		headerMap.put("cha_id","子渠道");
		headerMap.put("prjid","项目ID");
		headerMap.put("event","异常事件");
		headerMap.put("num","异常人数");
		headerMap.put("total_num","总人数");
		headerMap.put("warn_rate","异常占比");
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "异常数据监控_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
	}
	
}
