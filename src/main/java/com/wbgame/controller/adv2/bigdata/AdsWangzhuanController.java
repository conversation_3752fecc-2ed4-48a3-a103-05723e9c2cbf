package com.wbgame.controller.adv2.bigdata;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.AdsWangzhuanDataDTO;
import com.wbgame.pojo.adv2.bigdata.AdsWangzhuanDataVo;
import com.wbgame.service.adv2.AdsWangzhuanService;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 网赚-体外数据
 * @Date 2024/12/24 14:19
 */
@Api(tags = "网赚-体外数据汇总")
@CrossOrigin
@ControllerLoggingEnhancer
@RestController
@RequestMapping(value = "/adb/wangzhuan")
public class AdsWangzhuanController {

    @Autowired
    private AdsWangzhuanService adsWangzhuanService;

    /**
     * 查询版本，actionTypes数据
     *
     * @return 版本数据，actionTypes
     */
    @GetMapping("/queryVersions")
    public Result<Map<String, List<String>>> queryVersions() {
        return adsWangzhuanService.queryVersions();
    }

    /**
     * 网赚-体外数据-分页查询接口
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @PostMapping("/list")
    public Result<List<AdsWangzhuanDataVo>> queryList(@RequestBody AdsWangzhuanDataDTO dto) {
        return adsWangzhuanService.queryList(dto);
    }

    /**
     * 网赚-体外数据-导出接口
     *
     * @param dto 查询参数
     */
    @PostMapping("/export")
    public void export(AdsWangzhuanDataDTO dto, HttpServletResponse response) {
        dto.setStart(1);
        dto.setLimit(99999);
        Result<List<AdsWangzhuanDataVo>> queryResult = adsWangzhuanService.queryList(dto);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }


}
