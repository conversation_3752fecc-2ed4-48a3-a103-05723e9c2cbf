package com.wbgame.controller.wyz;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.wyz.ClientConfigVo;
import com.wbgame.service.wyz.ClientConfigService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 客户端配置文件查询修改接口
 * <AUTHOR>
 * @createDate 2023/2/6
 * @class ClientConfigController
 */

@CrossOrigin
@RestController
@RequestMapping("/game/set/clientConfig")
public class ClientConfigController {

    @Autowired
    ClientConfigService clientConfigService;

    /**
     * 查询客户端配置文件信息返回客户端
     * @param request
     * @return
     */
    @PostMapping("/get")
    public String getClientConfig(HttpServletRequest request) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageSize = "".equals(limit) ? 100 : Integer.parseInt(limit);
        int pageStart = (("".equals(start) ? 0 : Integer.parseInt(start)) - 1) * pageSize + 1;
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            Integer id = BlankUtils.checkBlank(request.getParameter("id")) ? null : Integer.parseInt(request.getParameter("id"));
            String ctype = BlankUtils.checkNull(request, "ctype");
            String appid = request.getParameter("appid");
            String chaId = request.getParameter("chaId");
            String pid = request.getParameter("pid");
            String cdata = BlankUtils.checkNull(request, "cdata");

            PageHelper.startPage(pageNo, pageSize);
            List<ClientConfigVo> clientConfigVos = clientConfigService.selectClientConfig(id, ctype, appid, chaId, pid, cdata);
            long size = ((Page) clientConfigVos).getTotal();

            result.put("ret", 1);
            result.put("data", clientConfigVos);
            result.put("totalCount", size);
        } catch (Exception e) {
            result.put("ret", 0);
            result.put("msg", "错误信息： " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 接收客户端上报新增客户端配置文件信息
     * @param request
     * @return
     */
    @PostMapping("/insert")
    public String insertClientConfig(HttpServletRequest request) {
        int result = 0;
        try {
            String ctype = BlankUtils.checkNull(request, "ctype");
            String appid = request.getParameter("appid");
            String chaId = request.getParameter("chaId");
            String pid = request.getParameter("pid");
            String cdata = BlankUtils.checkNull(request, "cdata");
            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            String creator = loginUser == null ? "Default" : loginUser.getLogin_name();

            result = clientConfigService.insertClientConfig(ctype, appid, chaId, pid, cdata, creator);

        } catch (IllegalArgumentException e){
            return ReturnJson.toErrorJson("参数数值错误");
        }
        catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }

    /**
     * 接收客户端上报修改客户端配置文件信息
     * @param request
     * @return
     */
    @PostMapping("/update")
    public String updateClientConfig(HttpServletRequest request) {
        int result = 0;
        try {
            int id = BlankUtils.checkBlank(request.getParameter("id")) ? null : Integer.parseInt(request.getParameter("id"));
            String cdata = BlankUtils.checkNull(request, "cdata");
            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            String updater = loginUser == null ? "Default" : loginUser.getLogin_name();
            result = clientConfigService.updateClientConfig(id, cdata, updater);
        } catch (Exception e) {
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }

        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }

    /**
     * 接收客户端上报删除客户端配置文件信息
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    public String deleteClientConfig(@RequestParam("id") String[] ids) {
        int result = 0;
        try {
            if (ids.length == 0) {
                return ReturnJson.toErrorJson("id数组为空");
            }
            for (String id: ids) {
                if (!BlankUtils.checkBlank(id)) {
                    result += clientConfigService.deleteClientConfig(Integer.parseInt(id));
                }
            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson("错误信息： " + e.getMessage());
        }
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }
    }
}
