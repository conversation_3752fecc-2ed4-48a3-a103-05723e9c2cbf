package com.wbgame.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.custom.AppDurationVo;
import com.wbgame.pojo.custom.CustomKeepUserVo;
import com.wbgame.pojo.custom.CustomStatsVo;
import com.wbgame.pojo.custom.PartnerStatsVo;
import com.wbgame.service.CustomService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

///**
// * 微信小游戏统计相关查询 -待删除
// * <AUTHOR>
// */
//@Controller
public class CustomController {
	
	@Autowired
	private CustomService customService;
	
	@CrossOrigin
	@RequestMapping(value="/custom/selectCustomStats", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String selectCustomStats(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String eventid = request.getParameter("tj_eventid");
		String label = request.getParameter("tj_label");
		String label_name = request.getParameter("tj_label_name");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer
				.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer
				.parseInt(limit);

		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String today = DateTime.now().toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		String ids = "'888888'";
		String regex = "^[A-Za-z0-9_-]+$";
		if(!BlankUtils.checkBlank(label) && !label.matches(regex)){ // 即为中文时替换
			Iterator<Entry<String, Map<String, Object>>> iterator = CommonUtil.gMap.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Map<String, Object>> next = iterator.next();
				if(label.equals(next.getValue().get("game_name"))){
					// 将中文名称替换成ID
					ids = ids +",'"+ next.getValue().get("id")+"'";
				}
			}
			paramMap.put("ids", ids);
		}else{
			paramMap.put("label", label);
		}
		
		paramMap.put("appid", appid);
		paramMap.put("pid", pid);
		paramMap.put("eventid", eventid);
		paramMap.put("label_name", label_name);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		
		List<CustomStatsVo> list = new ArrayList<>();
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		list = customService.selectCustomStatsInfo(paramMap);
		long size = ((Page) list).getTotal();
		for (CustomStatsVo cust : list) {
			cust.setTdate(cust.getTdate().replace("-", ""));
			
			Map<String, Object> map = CommonUtil.gMap.get(cust.getLabel());
			// label为ID，替换成名称
			if(map != null && map.size() > 0){
				cust.setLabel(map.get("game_name")+"");
			}
		}
		
		String jsonString = JSONArray.toJSONString(list);
		String respStr = "{'totalCount':"+size+",'root':"+jsonString+"}";

		return respStr;
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/exportCustomStats", method=RequestMethod.POST)
	public @ResponseBody void exportCustomStats(HttpServletRequest request, HttpServletResponse response){

		String fileName = DateTime.now().toString("yyyyMMddHHmmssSS")+".xls";
		File file = new File(request.getRealPath("/"), fileName);
		try {
			String appid = request.getParameter("tj_appid");
			String pid = request.getParameter("tj_pid");
			String eventid = request.getParameter("tj_eventid");
			String label = request.getParameter("tj_label");
			String label_name = request.getParameter("tj_label_name");
			String start_date = request.getParameter("tj_start_date");
			String end_date = request.getParameter("tj_end_date");
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			String today = DateTime.now().toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = today;
				end_date = today;
			}
			
			String ids = "'888888'";
			String regex = "^[A-Za-z0-9_-]+$";
			if(!BlankUtils.checkBlank(label) && !label.matches(regex)){ // 即为中文时替换
				Iterator<Entry<String, Map<String, Object>>> iterator = CommonUtil.gMap.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Map<String, Object>> next = iterator.next();
					if(label.equals(next.getValue().get("game_name"))){
						// 将中文名称替换成ID
						ids = ids +",'"+ next.getValue().get("id")+"'";
					}
				}
				paramMap.put("ids", ids);
			}else{
				paramMap.put("label", label);
			}
			
			paramMap.put("appid", appid);
			paramMap.put("pid", pid);
			paramMap.put("eventid", eventid);
			paramMap.put("label_name", label_name);
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			
			List<CustomStatsVo> list = customService.selectCustomStatsInfo(paramMap);
			for (CustomStatsVo cust : list) {
				
				Map<String, Object> map = CommonUtil.gMap.get(cust.getLabel());
				// label为ID，替换成名称
				if(map != null && map.size() > 0){
					cust.setLabel(map.get("game_name")+"");
				}
			}

			WritableWorkbook wwb = Workbook.createWorkbook(file);
        	//创建Excel工作表               创建Excel表的左下角表名(名称，第几个)
	        WritableSheet sheet = wwb.createSheet("微信统计数据详情", 0);//创建sheet
            //ws.mergeCells(0, 0, 2, 1);//合并单元格(左列，左行，右列，右行)从第1行第1列到第2行第3列
            
	        String[] title={"日期","APPID","PID","内容位置","点击内容","点击类型","展示数","点击数","人数","人均点击数"};
	        
            for(int i = 0; i < title.length; i++){//添加标题
            	Label l = new Label(i, 0, title[i], JxlUtil.getTitle());
            	sheet.setColumnView(i, 20);
            	sheet.addCell(l);
            }
            	
            for(int k = 0; k < list.size(); k++){ // 每一行的内容
            	String[] vals = {
            			list.get(k).getTdate(),
            			list.get(k).getAppid(),
            			list.get(k).getPid(),
            			list.get(k).getLabel_name(),
            			list.get(k).getLabel(),
            			list.get(k).getEventid(),
            			list.get(k).getShow_num()==null?"":list.get(k).getShow_num()+"",
            			list.get(k).getClick_num()+"",
            			list.get(k).getClick_user_num()+"",
            			list.get(k).getAve_click_num()
            	};

            	for(int m = 0; m < title.length; m++){ // 每一列的内容，从第二行开始插入
            		Label l = new Label(m, k+1, vals[m], JxlUtil.getNormolCell());
            		sheet.addCell(l);
            	}
            }
	     /*ws.setColumnView(0, 20);//设置列宽
	       ws.setRowView(0, 400);//设置行高 */
           wwb.write();
           wwb.close();

		} catch (Exception e){
			e.printStackTrace();
			fileName = "无数据.xls";
		}
		
		response.addHeader("Cache-Control","no-cache");
		response.addDateHeader("Expries",0);
		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		response.addHeader("Content-Disposition","attachment;filename=" + fileName);
		OutputStream pw = null;
		FileInputStream input = null;
		try {
			pw = response.getOutputStream();
			input = new FileInputStream(file);
			int length = 0;
			byte buffer[] = new byte[2048];
			while((length = input.read(buffer)) != -1){
				pw.write(buffer, 0, length);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				if(pw != null) {
					pw.close();
				}
				if(input != null) {
					input.close();
				}
				file.delete();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/selectCustomRetained", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String selectCustomRetained(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer
				.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer
				.parseInt(limit);

		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		String yesterday = DateTime.now().minusDays(1).toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = yesterday;
			end_date = yesterday;
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("pid", pid);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<CustomKeepUserVo> list = customService.selectCustomRetained(paramMap);
		long size = ((Page) list).getTotal();
		
		String jsonString = JSONArray.toJSONString(list);
		String respStr = "{'totalCount':"+size+",'root':"+jsonString+"}";

		return respStr;
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/channelList", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String channelList(HttpServletRequest request,HttpServletResponse response){
		
		List<String> channelList = customService.selectCustomChannelList(new HashMap<String,Object>());
		return JSONArray.toJSONString(channelList);
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/selectPartnerStats", method=RequestMethod.POST)
	public @ResponseBody String selectPartnerStats(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String channel = request.getParameter("tj_channel");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		String tj_creater = request.getParameter("tj_creater");
		String creater = "-1";
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer
				.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer
				.parseInt(limit);

		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		String today = DateTime.now().toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		if(!BlankUtils.checkBlank(tj_creater)){
			creater = tj_creater;
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("channel", channel);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("pid", pid);
		
		paramMap.put("table_name", "app_partner_info_total");
		
		Map<String, Map<String, Object>> createrMap = null;
		if(!"1".equals(creater) && !"2".equals(creater)){ // 非管理员组
			// 获取到该用户的所有pid信息，如没有则直接返回
			createrMap = customService.selectPartnerByCreater(creater);
			if(createrMap != null && createrMap.size() != 0){
				// 只查询本人创建的pid
				List<String> pidList = new ArrayList<String>(createrMap.keySet());
				if(BlankUtils.checkBlank(pid) || !pidList.contains(pid)){
					String allpid = "'"+String.join("','", pidList)+"'";
					paramMap.put("allpid", allpid);
					paramMap.remove("pid");
				}
			}else{
				return "{\"totalCount\":0,\"root\":[]}";
			}
					
		}
		
		List<PartnerStatsVo> list = new ArrayList<PartnerStatsVo>();
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		list = customService.selectPartnerStatsInfo(paramMap);
		long size = ((Page) list).getTotal();
		
		if(!"1".equals(creater) && list != null){
			for (PartnerStatsVo ps : list) {
				ps.setAdd_num(ps.getBus_num());
			}
		}
		
		String jsonString = JSONArray.toJSONString(list);
		String respStr = "{\"totalCount\":"+size+",\"root\":"+jsonString+"}";

		return respStr;
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/exportPartnerStats", method=RequestMethod.POST)
	public @ResponseBody void exportPartnerStats(HttpServletRequest request, HttpServletResponse response){

		String fileName = DateTime.now().toString("yyyyMMddHHmmssSS")+".xls";
		File file = new File(request.getRealPath("/"), fileName);
		try {
			String appid = request.getParameter("tj_appid");
			String pid = request.getParameter("tj_pid");
			String channel = request.getParameter("tj_channel");
			String start_date = request.getParameter("tj_start_date");
			String end_date = request.getParameter("tj_end_date");
			String creater = request.getParameter("tj_creater");
			
			String today = DateTime.now().toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = today;
				end_date = today;
			}
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("appid", appid);
			paramMap.put("pid", pid);
			paramMap.put("channel", channel);
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			
			paramMap.put("table_name", "app_partner_info_total");
			
			Map<String, Map<String, Object>> createrMap = null;
			if(!"1".equals(creater)){ // 非管理员组
				// 获取到该用户的所有pid信息，如没有则直接返回
				createrMap = customService.selectPartnerByCreater(creater);
				if(createrMap != null && createrMap.size() != 0){
					List<String> pidList = new ArrayList<String>(createrMap.keySet());
					if(BlankUtils.checkBlank(pid) || !pidList.contains(pid)){
						String allpid = "'"+String.join("','", pidList)+"'";
						paramMap.put("allpid", allpid);
						paramMap.remove("pid");
					}
				}
			}
			
			List<PartnerStatsVo> list = customService.selectPartnerStatsInfo(paramMap);
			if(!"1".equals(creater) && list != null){
				for (PartnerStatsVo ps : list) {
					ps.setAdd_num(ps.getBus_num());
				}
			}

			WritableWorkbook wwb = Workbook.createWorkbook(file);
        	//创建Excel工作表               创建Excel表的左下角表名(名称，第几个)
	        WritableSheet sheet = wwb.createSheet("渠道新增详情", 0);//创建sheet
            //ws.mergeCells(0, 0, 2, 1);//合并单元格(左列，左行，右列，右行)从第1行第1列到第2行第3列
            
	        String[] title={"日期","APPID","PID","渠道","新增数"};
	        
            for(int i = 0; i < title.length; i++){//添加标题
            	Label l = new Label(i, 0, title[i], JxlUtil.getTitle());
            	sheet.setColumnView(i, 20);
            	sheet.addCell(l);
            }
            	
            for(int k = 0; k < list.size(); k++){ // 每一行的内容
            	String[] vals = {
            			list.get(k).getTdate(),
            			list.get(k).getAppid(),
            			list.get(k).getPid(),
            			list.get(k).getChannel(),
            			list.get(k).getAdd_num()+""
            	};

            	for(int m = 0; m < title.length; m++){ // 每一列的内容，从第二行开始插入
            		Label l = new Label(m, k+1, vals[m], JxlUtil.getNormolCell());
            		sheet.addCell(l);
            	}
            }
	     /*ws.setColumnView(0, 20);//设置列宽
	       ws.setRowView(0, 400);//设置行高 */
           wwb.write();
           wwb.close();

		} catch (Exception e){
			e.printStackTrace();
			fileName = "无数据.xls";
		}
		
		response.addHeader("Cache-Control","no-cache");
		response.addDateHeader("Expries",0);
		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		response.addHeader("Content-Disposition","attachment;filename=" + fileName);
		OutputStream pw = null;
		FileInputStream input = null;
		try {
			pw = response.getOutputStream();
			input = new FileInputStream(file);
			int length = 0;
			byte buffer[] = new byte[2048];
			while((length = input.read(buffer)) != -1){
				pw.write(buffer, 0, length);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				if(pw != null) {
					pw.close();
				}
				if(input != null) {
					input.close();
				}
				file.delete();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/selectPartnerStatsKeep", method=RequestMethod.POST)
	public @ResponseBody String selectPartnerStatsKeep(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String channel = request.getParameter("tj_channel");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer
				.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer
				.parseInt(limit);

		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		String today = DateTime.now().toString("yyyyMMdd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appid", appid);
		paramMap.put("channel", channel);
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("pid", pid);
		
		
		List<PartnerStatsVo> list = new ArrayList<PartnerStatsVo>();
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		list = customService.selectPartnerKeepInfo(paramMap);
		long size = ((Page) list).getTotal();
		
		String jsonString = JSONArray.toJSONString(list);
		String respStr = "{\"totalCount\":"+size+",\"root\":"+jsonString+"}";

		return respStr;
	}
	
	@CrossOrigin
	@RequestMapping(value="/custom/selectAppDurationInfo", method=RequestMethod.POST)
	public @ResponseBody String selectAppDurationInfo(HttpServletRequest request,HttpServletResponse response){
		String pStart = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String appid = request.getParameter("tj_appid");
		String pid = request.getParameter("tj_pid");
		String channel = request.getParameter("tj_channel");
		String start_date = request.getParameter("tj_start_date");
		String end_date = request.getParameter("tj_end_date");
		
		// pageStart 和 limit设置值
		int pageStart = "".equals(pStart) == true ? 0 : Integer
				.parseInt(pStart);
		int pageSize = "".equals(limit) == true ? 100 : Integer
				.parseInt(limit);

		// 当前页
		int pageNo = (pageStart / pageSize) + 1;
		
		String today = DateTime.now().toString("yyyy-MM-dd");
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = today;
			end_date = today;
		}
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("appid", appid);
		paramMap.put("pid", pid);
		paramMap.put("channel", channel);
		
		PageHelper.startPage(pageNo, pageSize); // 进行分页
		List<AppDurationVo> list = customService.selectAppDurationInfo(paramMap);
		long size = ((Page) list).getTotal();
		for (AppDurationVo app : list) {
			// 整除不进行4舍5入，保留两位小数
			app.setPer_rate(new BigDecimal(app.getDuration()).divide(new BigDecimal(app.getNumber()), 2, RoundingMode.FLOOR).toString());
		}
		
		String jsonString = JSONArray.toJSONString(list);
		String respStr = "{\"totalCount\":"+size+",\"root\":"+jsonString+"}";

		return respStr;
	}
	
}
