package com.wbgame.controller.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.test.ForbiddenResultVo;
import com.wbgame.pojo.test.query.SingleCommonQueryVo;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @Classname TestBlackController
 * @Description TODO
 * @Date 2022/8/3 16:42
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/test/forbidden")
@Api(tags = "黑名单单独服务")
@ApiSupport(author = "chensq")
public class TestForbiddenController {

    private static final String URL = "https://ddz.vigame.cn:6601/forbidden/v1/get";
    private static final String URL_TEST = "http://************:6409/forbidden/v1/get";

    @Autowired
    private AdService adService;

    @RequestMapping("get")
    @ApiOperation(value = "查询是否被封禁", notes = "查询是否被封禁单独服务", httpMethod = "POST")
    public Result<ForbiddenResultVo> getForbidden(@Valid SingleCommonQueryVo vo){

        String query = "select concat(id,'') as mapkey,app_id from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        if (appMap.get(vo.getAppid())==null){
            return ResultUtils.failure("未配置接口");
        }

        SortedMap<String,String> params = JSON.parseObject(JSON.toJSONString(vo), SortedMap.class);
        String signKey = appMap.get(vo.getAppid()).get("app_id") + "";
        String sign = StringUtils.sign(params,signKey);

        params.put("sign",sign);
        String value = org.apache.commons.codec.binary.Base64.encodeBase64String((JSON.toJSONString(params)).getBytes());

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("value", value);
        String result = HttpRequest.httpPost(URL,paramMap,new HashMap<>());
        JSONObject retJson = null;
        if (BlankUtils.checkBlank(result)){
            return ResultUtils.failure("服务器无响应");
        }
        retJson = JSONObject.parseObject(result);
        JSONObject dataJson = retJson.getJSONObject("data")!=null?retJson.getJSONObject("data").getJSONObject("forbidden"):null;
        if (dataJson!=null){
            ForbiddenResultVo forbiddenResult = JSON.toJavaObject(dataJson, ForbiddenResultVo.class);
            return ResultUtils.success(forbiddenResult);
        }else {
            return ResultUtils.success();
        }

    }
}
