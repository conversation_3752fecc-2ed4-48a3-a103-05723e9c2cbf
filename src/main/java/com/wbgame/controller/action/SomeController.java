package com.wbgame.controller.action;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.ConfigVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.MarketSysFind;
import com.wbgame.pojo.RequestVo;
import com.wbgame.pojo.RespUserInfo;
import com.wbgame.pojo.SDKConfigInfo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;


@Controller
public class SomeController { // 通用协议
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private SomeService someService;
	
	@CrossOrigin
	@RequestMapping(value="/some/giftconfig", method=RequestMethod.POST)
	public @ResponseBody String giftconfig(RequestVo rv, HttpServletResponse resp){
		List<ConfigVo> configlist = new ArrayList<ConfigVo>();
		
		List<ConfigVo> giftList = someService.selectGiftConfig();
		for(ConfigVo gift : giftList){
			if(gift.getPid().equals(rv.getPid())){
				configlist.add(gift);
			}
		}
		JSONObject result = new JSONObject();
		result.put("result", 0);
		result.put("data", JSONArray.toJSONString(configlist));
		return result.toJSONString();
	}
	
	@CrossOrigin
	@RequestMapping(value="/some/getUserInfo", method=RequestMethod.POST)
	public @ResponseBody String postscore(RequestVo rv, HttpServletResponse resp){
		if(BlankUtils.checkBlank(rv.getAppid())
			|| BlankUtils.checkBlank(rv.getPid())){
			return "{\"msg\":\"错误信息: 缺少指定参数\"}";
		}
		try {
//			String value = new String(Base64.decodeBase64(req.getParameter("value")),"UTF-8");
//			GameLogVo gv = JSONObject.parseObject(value, GameLogVo.class);
//			gv.setCreate_date(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
			
			ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
			ListOperations<String, Object> opsForList = redisTemplate.opsForList();
//			System.out.println("传送参数...");
//			BlankUtils.classForeachEcho(rv);
			
			RespUserInfo ru = null;
			if(BlankUtils.checkBlank(rv.getId())){ // id为空
				if(!BlankUtils.checkBlank(rv.getWxid())){ // 微信id存在
					// 读取缓存中wxid绑定的用户id
					String userid = (String)opsForValue.get(CommonUtil.REDIS_STRING_SOME_WXIDTOID+rv.getWxid());
					
					rv.setId(userid);
					RespUserInfo data = someService.queryUserByAttr(rv, "wx");
					if(data != null){
						JSONObject result = new JSONObject();
						result.put("result", 0);
						result.put("data", JSONObject.toJSONString(data));
						return result.toJSONString();
					}
				}
				if(!BlankUtils.checkBlank(rv.getImei())){ // imei存在
					// 读取缓存中imei绑定的用户id
					String userid = (String)opsForValue.get(CommonUtil.REDIS_STRING_SOME_IMEITOID+rv.getImei());
					
					rv.setId(userid);
					RespUserInfo data = someService.queryUserByAttr(rv, "imei");
					if(data != null){
						JSONObject result = new JSONObject();
						result.put("result", 0);
						result.put("data", JSONObject.toJSONString(data));
						return result.toJSONString();
					}
				}
				
				// 初始化注册信息
				Long number = opsForValue.increment(CommonUtil.REDIS_STRING_SOME_USERIDNUM, 1);
				if(number == null || number < 10000){
					number = (DateTime.now().getMillis() / 60000 + 1000);
					opsForValue.set(CommonUtil.REDIS_STRING_SOME_USERIDNUM, number);
				}
				ru = new RespUserInfo();
				ru.setId(number + "");
				ru.setName("游客"+String.valueOf(number).substring(3));
				ru.setWxid(rv.getWxid());
				ru.setWxhead(rv.getWxhead());
				ru.setWxname(rv.getWxname());
				ru.setLsn(rv.getLsn());
				ru.setImsi(rv.getImsi());
				ru.setImei(rv.getImei());
				ru.setMmid(rv.getMmid());
				ru.setPid(rv.getPid());
				ru.setAppid(rv.getAppid());
				
				ru.setIssign(0);
				ru.setLv(1);
				ru.setGold(0);
				ru.setDiamond(0);
				ru.setExp(1);
				
				ru.setCreate_date(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
				ru.setLogin_time(String.valueOf(DateTime.now().getMillis() / 1000));
				
				opsForValue.set(CommonUtil.REDIS_STRING_SOME_USERINFO+ru.getId(),ru);
//				opsForList.leftPush(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+rv.getAppid(), ru);
				if(!BlankUtils.checkBlank(ru.getWxid()))
					opsForValue.set(CommonUtil.REDIS_STRING_SOME_WXIDTOID+ru.getWxid(),ru.getId());
				if(!BlankUtils.checkBlank(ru.getImei()))
					opsForValue.set(CommonUtil.REDIS_STRING_SOME_IMEITOID+ru.getImei(),ru.getId());
				
			}else{
				ru = someService.queryUserByAttr(rv, "id");
			}
			
			JSONObject result = new JSONObject();
			result.put("result", 1);
			result.put("data", JSONObject.toJSONString(ru));
			return result.toJSONString();
			
		} catch (Exception e) {
//			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("result", 0);
			result.put("msg", "错误信息: "+e.getMessage());
			return result.toJSONString();
		}
		
	}
	
	@CrossOrigin
	@RequestMapping(value="/some/updateUserInfo", method = {
			RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String updateUserInfo(RespUserInfo ru, HttpServletResponse resp){
		if(BlankUtils.checkBlank(ru.getId()) || BlankUtils.checkBlank(ru.getAppid())){
			return "{\"msg\":\"错误信息: 缺少指定参数\"}";
		}
		try {
			ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
			ListOperations<String, Object> opsForList = redisTemplate.opsForList();
			
			RespUserInfo obj = (RespUserInfo)opsForValue.get(CommonUtil.REDIS_STRING_SOME_USERINFO+ru.getId());
			// 修改缓存中用户数据，加入队列同步
			ru.setLogin_time(obj.getLogin_time());
			opsForValue.set(CommonUtil.REDIS_STRING_SOME_USERINFO+ru.getId(),ru);
//			opsForList.leftPush(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+ru.getAppid(), ru);
			if(!BlankUtils.checkBlank(ru.getWxid()))
				opsForValue.set(CommonUtil.REDIS_STRING_SOME_WXIDTOID+ru.getWxid(),ru.getId());
			if(!BlankUtils.checkBlank(ru.getImei()))
				opsForValue.set(CommonUtil.REDIS_STRING_SOME_IMEITOID+ru.getImei(),ru.getId());
			
			
			JSONObject result = new JSONObject();
			result.put("result", 1);
			return result.toJSONString();
			
		} catch (Exception e) {
//			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("result", 0);
			result.put("msg", "错误信息: "+e.getMessage());
			return result.toJSONString();
		}
	}
	
	/**
	 * 
	 * 国内广告-产品收支权限配置
	 * @param record
	 * @param request
	 * @param response
	 * @return
	 */
	@CrossOrigin
	@RequestMapping(value = "/ad/selectfindVal", method = {
			RequestMethod.GET, RequestMethod.POST })
	public @ResponseBody String selectfindVal(HttpServletRequest request,HttpServletResponse response,String type) {
		
		JSONObject result = new JSONObject();
		result.put("ret", 1);
		result.put("data", someService.selectfindVal(type));
	
		return result.toJSONString();
	}
	
}
