package com.wbgame.controller.clean;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.WaibaoUserVo;
import com.wbgame.pojo.clean.kxp.KxpMaterialVo;
import com.wbgame.pojo.clean.kxp.KxpTitleVo;
import com.wbgame.service.clean.CleanKxpService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname CleanKxpController
 * @Description TODO
 * @Date 2021/11/15 10:01
 */
@RequestMapping("clean")
@RestController
@CrossOrigin
@Api(tags = "素材配置")
public class CleanKxpController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    CleanKxpService cleanKxpService;

    private static final Map<String, String> FIXED_MODEL_ID_MAP = new HashMap<String, String>() {
        {
            put("1", "模板");
            put("2", "贴纸");
            put("3", "特效");
            put("4", "音乐");
        }
    };


    /**
     * 查询开心拍素材标题列表
     *
     * @param request
     * @param vo
     * @return
     */
    @ApiOperation(value = "素材标题列表", notes = "素材标题列表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
    })
    @RequestMapping("kxp/getTitleList")
    public Object getKxpTitleList(HttpServletRequest request, KxpTitleVo vo) {

        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<KxpTitleVo> list = cleanKxpService.getKxpTitleList(vo);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();

    }

    /**
     * 查询开心拍素材标题列表下拉接口
     *
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("kxp/getTitleSelectList")
    public Object getTitleSelectList(HttpServletRequest request) {

        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        List<KxpTitleVo> list = cleanKxpService.getKxpTitleList(new KxpTitleVo());
        for (KxpTitleVo vo : list) {
            vo.setModelDesc(FIXED_MODEL_ID_MAP.get(vo.getModelType()) + "-" + vo.getModelTitle());
        }
        for (Map.Entry<String, String> map : FIXED_MODEL_ID_MAP.entrySet()) {
            KxpTitleVo kxpTitleVo = new KxpTitleVo();
            kxpTitleVo.setModelId(map.getKey());
            kxpTitleVo.setModelDesc(map.getKey() + "-" + map.getValue());
            list.add(kxpTitleVo);
        }
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        return result.toJSONString();

    }

    /**
     * 操作开心拍素材标题配置
     *
     * @param request
     * @param vo
     * @return
     */
    @ApiOperation(value = "素材标题配置", notes = "素材标题配置", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "handle", value = "操作", dataType = "String")
    })
    @RequestMapping("kxp/titleHandle")
    public Object handKxpTitle(HttpServletRequest request, KxpTitleVo vo) {
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }
        if (!BlankUtils.checkBlank(username)) {
            vo.setCreateUser(username);
            vo.setModifyUser(username);
        }
        String handle = request.getParameter("handle");
        int succ = 0;
        if ("add".equals(handle)) {
            List<KxpTitleVo> list = cleanKxpService.getKxpTitleList(new KxpTitleVo());
            KxpTitleVo dataBaseVo = list
                    .stream()
                    .filter(t -> t.getModelTitle().equals(vo.getModelTitle()) && t.getModelType().equals(vo.getModelType()))
                    .findFirst().orElse(null);
            if (dataBaseVo != null) {
                return ReturnJson.toErrorJson("已经存在重复的模板标题");
            }
            succ = cleanKxpService.addKxpTitle(vo);
        } else if ("update".equals(handle)) {
            List<KxpTitleVo> list = cleanKxpService.getKxpTitleList(new KxpTitleVo());
            KxpTitleVo dataBaseVo = list
                    .stream()
                    .filter(t -> t.getModelTitle().equals(vo.getModelTitle()) && !t.getModelId().equals(vo.getModelId()) && t.getModelType().equals(vo.getModelType()))
                    .findFirst()
                    .orElse(null);
            if (dataBaseVo != null) {
                return ReturnJson.toErrorJson("已经存在重复的模板标题");
            }
            succ = cleanKxpService.updateKxpTitle(vo);
        } else if ("del".equals(handle)) {
            succ = cleanKxpService.delKxpTitle(vo);
        }
        if (succ > 0) {
            return ReturnJson.success();
        }
        return ReturnJson.toErrorJson("操作失败,请联系管理员!");
    }


    /**
     * 查询开心拍素材详情列表
     *
     * @param request
     * @param vo
     * @return
     */
    @ApiOperation(value = "素材详情配置查询", notes = "查询", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
    })
    @RequestMapping("kxp/getMaterialList")
    public Object getKxpMaterialList(HttpServletRequest request, KxpMaterialVo vo) {

        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<KxpMaterialVo> list = cleanKxpService.getKxpMaterialList(vo);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();

    }

    /**
     * 操作开心拍素材详情配置
     *
     * @param request
     * @param vo
     * @return
     */
    @ApiOperation(value = "素材详情配置", notes = "素材详情配置", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "handle", value = "操作", dataType = "String")
    })
    @RequestMapping("kxp/materialHandle")
    public Object handKxpMaterial(HttpServletRequest request, KxpMaterialVo vo) {
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }
        if (!BlankUtils.checkBlank(username)) {
            vo.setCreateUser(username);
            vo.setModifyUser(username);
        }
        String handle = request.getParameter("handle");
        int succ = 0;
        if ("add".equals(handle)) {
            succ = cleanKxpService.addKxpMaterial(vo);
        } else if ("update".equals(handle)) {
            succ = cleanKxpService.updateKxpMaterial(vo);
        } else if ("del".equals(handle)) {
            succ = cleanKxpService.delKxpMaterial(vo);
        }
        if (succ > 0) {
            return ReturnJson.success();
        }
        return ReturnJson.toErrorJson("操作失败,请联系管理员!");
    }

}
