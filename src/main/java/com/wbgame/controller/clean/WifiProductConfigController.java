package com.wbgame.controller.clean;

import com.wbgame.common.Constants;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.clean.WaibaoUserVo;
import com.wbgame.pojo.clean.WifiProductConfigDTO;
import com.wbgame.pojo.clean.WifiProductConfigVO;
import com.wbgame.service.clean.IWifiProductConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * @authoer: zhangY
 * @createDate: 2022/6/20 16:18
 * @class: WifiProductConfigController
 * @description:
 */
@Api(tags = "海外清理商品配置")
@RestController
@CrossOrigin
@RequestMapping(value = "/clean")
public class WifiProductConfigController {

    @Autowired
    private IWifiProductConfigService wifiProductConfigService;


    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteById")
    public String deleteById(HttpServletRequest request, @RequestParam(value = "id") Integer id) {

        try {
            checkTokenAndReturnUserName(request, null);
        } catch (Exception e) {
            return ReturnJson.error(Constants.ParamError);
        }

        return wifiProductConfigService.deleteById(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @PostMapping(value = "/insertProductConfig")
    public String insertProductConfig(HttpServletRequest request, @Validated WifiProductConfigDTO productConfig) {

        try {
            String username = checkTokenAndReturnUserName(request, null);
            productConfig.setCreateUser(username);
        } catch (Exception e) {

            return ReturnJson.error(Constants.ParamError);
        }
        return wifiProductConfigService.insertProductConfig(productConfig);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @PostMapping(value = "/updateProductConfigById")
    public String updateProductConfigById(HttpServletRequest request, @Validated WifiProductConfigDTO productConfig) {

        try {
            String username = checkTokenAndReturnUserName(request, null);
            productConfig.setModifyUser(username);
        } catch (Exception e) {

            return ReturnJson.error(Constants.ParamError);
        }
        return wifiProductConfigService.updateProductConfigById(productConfig);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = WifiProductConfigVO.class)
    })
    @PostMapping(value = "/selectProductConfigAllList")
    public String selectProductConfigAllList(HttpServletRequest request, @Validated(QueryGroup.class) WifiProductConfigDTO productConfigDTO) {

        try {
            checkTokenAndReturnUserName(request, null);
        } catch (Exception e) {
            return ReturnJson.error(Constants.ParamError);
        }
        return wifiProductConfigService.selectProductConfigAllList(productConfigDTO);
    }

    @ApiOperation(value = "批量修改排序", notes = "批量修改排序")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @PostMapping(value = "/produceSort")
    public String produceSort(HttpServletRequest request, @RequestParam(value = "ids") String ids,
                              @RequestParam(value = "cha") String cha) {

        try {
            checkTokenAndReturnUserName(request, null);
        } catch (Exception e) {
            return ReturnJson.error(Constants.ParamError);
        }
        return wifiProductConfigService.produceSort(ids, cha);
    }

    @ApiOperation(value = "商品排序", notes = "商品排序")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = WifiProductConfigVO.class)
    })
    @PostMapping(value = "/selectProductOrder")
    public String selectProductOrder(HttpServletRequest request, @RequestParam(value = "cha") String cha) {

        try {
            checkTokenAndReturnUserName(request, null);
        } catch (Exception e) {
            return ReturnJson.error(Constants.ParamError);
        }
        return wifiProductConfigService.commoditySortingQuery(cha);
    }
    /**
     * 校验token，获取用户名
     */
    public String checkTokenAndReturnUserName(HttpServletRequest request, String token) throws Exception {

        String headerToken = request.getHeader("token");
        token = org.apache.commons.lang3.StringUtils.isBlank(token) ?
                (org.apache.commons.lang3.StringUtils.isBlank(headerToken) ? request.getParameter("token") : headerToken) : token;

        if (org.apache.commons.lang3.StringUtils.isBlank(token) || !redisTemplate.hasKey(token)) {

            throw new RuntimeException();
        } else {

            String username;
            if (token.startsWith("wbtoken")) {
                com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
                username = currUserVo.getLogin_name();
            } else {
                com.wbgame.pojo.clean.WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
                username = WaibaoUser.getUser_name();
            }
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            return username;
        }
    }
}
