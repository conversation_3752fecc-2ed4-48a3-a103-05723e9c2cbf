package com.wbgame.controller.clean;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.WindControlLevelDTO;
import com.wbgame.pojo.operate.WindControlLevelVO;
import com.wbgame.service.clean.IWindControlLevelService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/22
 * @class: WindControlLevelController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/windControlLevel")
@Api(tags = "新风控v3分数配置页面")
public class WindControlLevelController {

    private IWindControlLevelService windControlLevelService;

    @Autowired
    public void setWindControlLevelService(IWindControlLevelService windControlLevelService) {
        this.windControlLevelService = windControlLevelService;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteByIdList")
    public Result<Integer> deleteByIdList(@RequestParam("idList") List<Integer> idList) {

        return windControlLevelService.deleteByIdList(idList);
    }


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectCoinsGroupConfig")
    public Result<PageResult<WindControlLevelVO>> selectCoinsGroupConfig(@ApiNeed({"eventGroup",
            "eventName","start",
            "limit"}) WindControlLevelDTO dto) {

        return windControlLevelService.selectWindControlLevel(dto);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertCoinsGroupConfig")
    public Result<Integer> insertCoinsGroupConfig(
            HttpServletRequest request,
            @RequestParam("appid") String appid,
            @RequestParam("prjid") String prjid,
            @RequestParam("version") Integer version) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");

        return windControlLevelService.insertWindControlLevel(appid, prjid, version, loginUser.getLogin_name());
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/updateCoinsGroupConfig")
    public Result<Integer> updateCoinsGroupConfig(
            HttpServletRequest request,
            @ApiNeed({
                    "id",
                    "eventGroup",
                    "eventName",
                    "hclass",
                    "hkey",
                    "hvalue",
                    "note",
                    "score"})@Validated(UpdateGroup.class) WindControlLevelDTO dto) {


        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setUpdateUser(loginUser.getLogin_name());
        return windControlLevelService.updateWindControlLevel(dto);
    }

    @ApiOperation(value = "复制", notes = "复制")
    @PostMapping(value = "/copyWindControlLevel")
    public Result<Integer> copyWindControlLevel(
            HttpServletRequest request,
            @ApiNeed({
                    "appid",
                    "prjid",
                    "eventGroup",
                    "eventName",
                    "hclass",
                    "hkey",
                    "hvalue",
                    "note",
                    "score"})@Validated WindControlLevelDTO dto) {


        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setUpdateUser(loginUser.getLogin_name());
        return windControlLevelService.copyWindControlLevel(dto);
    }
}
