package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.pojo.game.PayAdValueVo;
import com.wbgame.pojo.game.report.PayAdValueReportVo;
import com.wbgame.service.AdService;
import com.wbgame.service.mobile.UserPayInfoService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname PayAdValueController
 * @Description 付费用户广告价值
 * @Date 2022/5/6 10:17
 */
@CrossOrigin
@RestController
@RequestMapping("/game/payAdValue")
public class PayAdValueController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    private AdService adService;

    /**
     * 付费用户广告价值-当日数据-查询
     * @param request
     * @return
     */
    @RequestMapping("getTodayList")
    public Object getTodayList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        String order_str = request.getParameter("order_str");

        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }
            }
        }

        String tableName = "";
        if (!BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_3d_hourly";
        }
        if (!BlankUtils.checkBlank(appid_group)&&BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_2d_hourly";
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("start_date", start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("tableName",tableName);
        paramMap.put("group", group);
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);
        List<PayAdValueVo> list = userPayInfoService.getPayAdValueList(paramMap);

        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayAdValueVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())){
                vo.setAppname(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"":"");
            }
        }
        PayAdValueVo total = userPayInfoService.getPayAdValueSum(paramMap);

        PayAdValueReportVo chart = userPayInfoService.getPayAdValueDayReportList(paramMap);
        if (chart!=null){
            keepTwoDecimal(chart);
        }

        long size = ((Page) list).getTotal();
        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",list);
        ret.put("total",total);
        ret.put("chart",chart);
        ret.put("totalSize",size);
        return ret;
    }


    /**
     * 付费用户广告价值-当日数据-导出
     * @param request
     * @param response
     */
    @RequestMapping("exportTodayList")
    public void exportTodayList(HttpServletRequest request, HttpServletResponse response) {
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        String order_str = request.getParameter("order_str");
        String export_file_name = request.getParameter("export_file_name");

        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }
            }
        }

        String tableName = "";
        if (!BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_3d_hourly";
        }
        if (!BlankUtils.checkBlank(appid_group)&&BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_2d_hourly";
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("tableName",tableName);
        paramMap.put("group", group);
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);
        List<PayAdValueVo> list = userPayInfoService.getPayAdValueList(paramMap);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayAdValueVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())){
                vo.setAppid(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"-"+vo.getAppid():"");
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name+ DateTime.now().toString("yyyyMMdd")+".xlsx");
    }


    /**
     * 付费用户广告价值-累计数据-查询
     * @param request
     * @return
     */
    @RequestMapping("getTotalList")
    public Object getTotalList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        String order_str = request.getParameter("order_str");

        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }
            }
        }

        String tableName = "";
        if (!BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_3d_hourly";
        }
        if (!BlankUtils.checkBlank(appid_group)&&BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_2d_hourly";
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("start_date", start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("tableName",tableName);
        paramMap.put("group", group);
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);
        List<PayAdValueVo> list = userPayInfoService.getPayAdValueTotalList(paramMap);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayAdValueVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())) {
                vo.setAppname(appMap.get(vo.getAppid()) != null ? appMap.get(vo.getAppid()).get("app_name") + "" : "");
            }
        }
        PayAdValueVo total = userPayInfoService.getPayAdValueTotalSum(paramMap);
        PayAdValueReportVo chart = userPayInfoService.getPayAdValueTotalReportList(paramMap);
        if (chart!=null){
            keepTwoDecimal(chart);
        }
        long size = ((Page) list).getTotal();
        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",list);
        ret.put("total",total);
        ret.put("chart",chart);
        ret.put("totalSize",size);
        return ret;
    }


    /**
     * 付费用户广告价值-累计数据-导出
     * @param request
     * @param response
     */
    @RequestMapping("exportTotalList")
    public void exportTotalList(HttpServletRequest request, HttpServletResponse response) {
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        String order_str = request.getParameter("order_str");
        String export_file_name = request.getParameter("export_file_name");

        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("t_date".equals(str)){

                }
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }
            }
        }

        String tableName = "";
        if (!BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_3d_hourly";
        }
        if (!BlankUtils.checkBlank(appid_group)&&BlankUtils.checkBlank(download_channel_group)){
            tableName = "dnwx_bi.ads_dim_users_info_2d_hourly";
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("tableName",tableName);
        paramMap.put("group", group);
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);
        List<PayAdValueVo> list = userPayInfoService.getPayAdValueTotalList(paramMap);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayAdValueVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())){
                vo.setAppname(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"":"");
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name+ DateTime.now().toString("yyyyMMdd")+".xlsx");
    }

    public PayAdValueVo keepTwoDecimal(PayAdValueVo source) {
        if (source != null) {
            Class clazz = source.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                //设置属性是可以访问的(私有的也可以)
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(source);
                    // 属性值不为空且包含小数点则保留一位小数
                    if (value != null&&value.toString().contains(".")&& StringUtils.isNumberByRegex(value.toString())) {
                        value = new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP).toString();
                        field.set(source,value);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

        }
        return source;
    }
    /**
     * 保存两位小数
     * @param source
     * @return
     */
    public PayAdValueReportVo keepTwoDecimal(PayAdValueReportVo source) {
        if (source != null) {
            Class clazz = source.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                //设置属性是可以访问的(私有的也可以)
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(source);
                    // 属性值不为空且包含小数点则保留一位小数
                    if (value != null&&value.toString().contains(".")) {
                        if (field.getName().contains("ipu")||field.getName().contains("ecpm")){
                            value = new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP).toString();
                        }else {
                            value = new BigDecimal(value.toString()).intValue()+"";
                        }
                        field.set(source,value);
                    }
                    if (value==null){
                        field.set(source,"0.00");
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return source;
    }

}
