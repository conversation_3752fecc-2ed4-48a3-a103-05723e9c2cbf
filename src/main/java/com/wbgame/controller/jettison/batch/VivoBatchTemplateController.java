package com.wbgame.controller.jettison.batch;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.aop.NewPageLimit;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.param.BatchUpdateTfDateParam;
import com.wbgame.pojo.jettison.param.VivoBatchQueryParam;
import com.wbgame.pojo.jettison.param.VivoBatchTemplateParam;
import com.wbgame.pojo.jettison.vo.VivoBatchTemplateVo;
import com.wbgame.service.jettison.VivoBatchTemplateService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description vivo批量计划创建模板
 * <AUTHOR>
 * @Date 2024/10/23 18:06
 */
@CrossOrigin
@RestController
@RequestMapping("/batch/template/vivo")
@Api(tags = "vivo批量计划创建模板")
@ApiSupport(author = "huangmb")
@LoginCheck
@Slf4j
public class VivoBatchTemplateController {

    @Autowired
    VivoBatchTemplateService vivoBatchTemplateService;

    @Autowired
    HttpServletRequest request;

    @RequestMapping("/getTags")
    @ApiOperation(value = "获取基本数据源",httpMethod = "GET")
    public String getTags() {
        JSONObject data = vivoBatchTemplateService.getTags();
        return ReturnJson.success(data);
    }

    @RequestMapping(value = "/list",method = {RequestMethod.POST })
    @ApiOperation(value = "查询模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = VivoBatchTemplateVo.class)
    })
    @NewPageLimit
    public String list(VivoBatchQueryParam param){

        //获取创建人
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        //开发可全部查看
        if (!"13".equals(user.getDepartment())) {
            param.setCreate_owner(user.getLogin_name());
        }
        List<VivoBatchTemplateVo> list = vivoBatchTemplateService.selectVivoBatchTemplateList(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("totalSize",((Page) list).getTotal());
        return ReturnJson.success(result);

    }

    @RequestMapping(value = "/view",method = {RequestMethod.POST })
    @ApiOperation(value = "查询模板详情", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = VivoBatchTemplateParam.class)
    })
    public String view(Long temp_id){

        if (temp_id == null) {
            return ReturnJson.toErrorJson("模板id不能为空");
        }

        VivoBatchTemplateParam vo = vivoBatchTemplateService.selectVivoBatchTemplateDetail(temp_id);

        return ReturnJson.success(vo);
    }

    @RequestMapping(value = "/create",method = {RequestMethod.POST })
    @ApiOperation(value = "创建模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String create(@RequestBody VivoBatchTemplateParam param){

        //插入模板
        try {
            CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
            param.setCreate_owner(user.getLogin_name());
            Boolean bool = vivoBatchTemplateService.create(param);
            if (bool) {
                return ReturnJson.success("创建成功");
            }else {
                return ReturnJson.toErrorJson("创建失败");
            }
        }catch (Exception e) {
            log.error("创建失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("创建失败,原因="+e.getMessage());
        }

    }


    @RequestMapping(value = "/update",method = {RequestMethod.POST })
    @ApiOperation(value = "更新模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String update(@RequestBody VivoBatchTemplateParam param){

        //更新模板
        try {
            CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
            param.setUpdate_owner(user.getLogin_name());
            Boolean bool = vivoBatchTemplateService.update(param);
            if (bool) {
                return ReturnJson.success("更新成功");
            }else {
                return ReturnJson.toErrorJson("更新失败");
            }
        }catch (Exception e) {
            log.error("更新失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("更新失败,原因="+e.getMessage());
        }

    }


    @RequestMapping(value = "/delete",method = {RequestMethod.POST })
    @ApiOperation(value = "删除模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String delete(Long temp_id){

        //删除模板
        try {
            Boolean bool = vivoBatchTemplateService.delete(temp_id);
            if (bool) {
                return ReturnJson.success("删除成功");
            }else {
                return ReturnJson.toErrorJson("删除失败");
            }
        }catch (Exception e) {
            log.error("删除失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("删除失败,原因="+e.getMessage());
        }

    }

    @RequestMapping(value = "/batchUpdateTfDate",method = {RequestMethod.POST })
    @ApiOperation(value = "批量修改投放时间", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String batchUpdateTfDate(BatchUpdateTfDateParam param){
        try {
            if (BlankUtils.checkBlank(param.getEndDate())) {
                param.setEndDate("2038-01-19");
            }
            vivoBatchTemplateService.batchUpdateTfDate(param);
            return ReturnJson.success();
        }catch (Exception e) {
            log.error("更新失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("更新失败,原因="+e.getMessage());
        }
    }


}
