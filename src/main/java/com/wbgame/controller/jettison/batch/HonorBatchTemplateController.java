package com.wbgame.controller.jettison.batch;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.aop.NewPageLimit;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.param.HonorBatchQueryParam;
import com.wbgame.pojo.jettison.param.HonorBatchTemplateParam;
import com.wbgame.pojo.jettison.vo.HonorBatchTemplateVo;
import com.wbgame.service.jettison.HonorBatchTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 16:15
 */
@CrossOrigin
@RestController
@RequestMapping("/batch/template/honor")
@Api(tags = "荣耀批量计划创建模板")
@ApiSupport(author = "huangmb")
@LoginCheck
@Slf4j
public class HonorBatchTemplateController {

    @Autowired
    HonorBatchTemplateService honorBatchTemplateService;

    @Autowired
    HttpServletRequest request;

    @RequestMapping("/rule")
    public void rule(String value){

        List<JSONObject> list = JSONArray.parseArray(value, JSONObject.class);

        for (JSONObject jsonObject : list) {
            List<JSONObject> specs = (List)jsonObject.get("specsTypeDtoList");
            for (JSONObject spec : specs) {
                JSONObject j = new JSONObject();
                j.put("promotion_purpose",jsonObject.getString("promotionPurpose"));
                j.put("traffic_type",jsonObject.getString("trafficScene"));
                j.put("ad_placement_id",jsonObject.getString("adPlacementId"));
                j.put("ad_placement_name",jsonObject.getString("adPlacementName"));
                j.put("ad_creative_spec_id",spec.getString("adCreativeSpecId"));
                j.put("ad_creative_spec_name",spec.getString("adCreativeSpecName"));
                j.put("required",spec.getString("elements"));
                List<JSONObject> elements = (List)spec.get("elements");
                for (JSONObject element : elements) {
                    //图片
                    if (element.getInteger("eleType") == 3) {
                        JSONObject eleKeys = element.getJSONObject("eleKeys");
                        j.put("wide",eleKeys.getInteger("width"));
                        j.put("height",eleKeys.getInteger("height"));
                        j.put("size",eleKeys.getInteger("fileMax"));
                        j.put("format",eleKeys.getString("fileExtension"));
                        j.put("material_type","image");
                    }
                    //视频
                    if (element.getInteger("eleType") == 1) {
                        JSONObject eleKeys = element.getJSONObject("eleKeys");
                        j.put("wide",eleKeys.getInteger("width"));
                        j.put("height",eleKeys.getInteger("height"));
                        j.put("size",eleKeys.getInteger("fileMax"));
                        j.put("format",eleKeys.getString("fileExtension"));
                        j.put("material_type","video");
                    }
                    //封面
                    if (element.getInteger("eleType") == 2) {
                        JSONObject eleKeys = element.getJSONObject("eleKeys");
                        j.put("cover_wide",eleKeys.getInteger("width"));
                        j.put("cover_height",eleKeys.getInteger("height"));
                        j.put("cover_size",eleKeys.getInteger("fileMax"));
                        j.put("cover_format",eleKeys.getString("fileExtension"));
                    }
                    //logo
                    if (element.getInteger("eleType") == 7) {
                        JSONObject eleKeys = element.getJSONObject("eleKeys");
                        j.put("logo_wide",eleKeys.getInteger("width"));
                        j.put("logo_height",eleKeys.getInteger("height"));
                        j.put("logo_size",eleKeys.getInteger("fileMax"));
                        j.put("logo_format",eleKeys.getString("fileExtension"));
                    }
                }
                honorBatchTemplateService.InsertRule(j);
            }
        }

    }

    @RequestMapping("/getTags")
    @ApiOperation(value = "获取基本数据源",httpMethod = "GET")
    public String getTags() {
        JSONObject data = honorBatchTemplateService.getTags();
        return ReturnJson.success(data);
    }

    @RequestMapping(value = "/list",method = {RequestMethod.POST })
    @ApiOperation(value = "查询模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = HonorBatchTemplateVo.class)
    })
    @NewPageLimit
    public String list(HonorBatchQueryParam param){

        //获取创建人
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        //开发可全部查看
        if (!"13".equals(user.getDepartment())) {
            param.setCreate_owner(user.getLogin_name());
        }
        List<HonorBatchTemplateVo> list = honorBatchTemplateService.selectVivoBatchTemplateList(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("totalSize",((Page) list).getTotal());
        return ReturnJson.success(result);

    }

    @RequestMapping(value = "/view",method = {RequestMethod.POST })
    @ApiOperation(value = "查询模板详情", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = HonorBatchTemplateParam.class)
    })
    public String view(Long temp_id){

        if (temp_id == null) {
            return ReturnJson.toErrorJson("模板id不能为空");
        }

        HonorBatchTemplateParam vo = honorBatchTemplateService.selectVivoBatchTemplateDetail(temp_id);

        return ReturnJson.success(vo);
    }

    @RequestMapping(value = "/create",method = {RequestMethod.POST })
    @ApiOperation(value = "创建模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String create(@RequestBody HonorBatchTemplateParam param){

        //插入模板
        try {
            CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
            param.setCreate_owner(user.getLogin_name());
            Boolean bool = honorBatchTemplateService.create(param);
            if (bool) {
                return ReturnJson.success("创建成功");
            }else {
                return ReturnJson.toErrorJson("创建失败");
            }
        }catch (Exception e) {
            log.error("创建失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("创建失败,原因="+e.getMessage());
        }

    }


    @RequestMapping(value = "/update",method = {RequestMethod.POST })
    @ApiOperation(value = "更新模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String update(@RequestBody HonorBatchTemplateParam param){

        //更新模板
        try {
            CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
            param.setUpdate_owner(user.getLogin_name());
            Boolean bool = honorBatchTemplateService.update(param);
            if (bool) {
                return ReturnJson.success("更新成功");
            }else {
                return ReturnJson.toErrorJson("更新失败");
            }
        }catch (Exception e) {
            log.error("更新失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("更新失败,原因="+e.getMessage());
        }

    }


    @RequestMapping(value = "/delete",method = {RequestMethod.POST })
    @ApiOperation(value = "删除模板", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String delete(Long temp_id){

        //删除模板
        try {
            Boolean bool = honorBatchTemplateService.delete(temp_id);
            if (bool) {
                return ReturnJson.success("删除成功");
            }else {
                return ReturnJson.toErrorJson("删除失败");
            }
        }catch (Exception e) {
            log.error("删除失败,原因={}",e.getMessage(),e);
            return ReturnJson.toErrorJson("删除失败,原因="+e.getMessage());
        }

    }

}
