package com.wbgame.controller.jettison.report;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Constants;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.advert.oversea.BasePageResultResponse;
import com.wbgame.pojo.advert.oversea.BaseResult;
import com.wbgame.pojo.advert.oversea.OverseaPlacementReport;
import com.wbgame.pojo.advert.oversea.PlacementParam;
import com.wbgame.pojo.jettison.report.AccountCashDetailReport;
import com.wbgame.pojo.jettison.report.param.AccountCashDetailParam;
import com.wbgame.service.advert.oversea.PlacementIncomeService;
import com.wbgame.service.jettison.report.AccountReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/6/19
 * @description
 **/
@CrossOrigin
@RestController
@RequestMapping("/tfxt/account")
@Api(tags = "投放账户相关数据")
@ApiSupport(author = "shenl")
@Slf4j
public class AccountReportController {

    @Resource
    private AccountReportService accountReportService;

    @RequestMapping(value = "/placement/list",method = {RequestMethod.POST })
    @ApiOperation(value = "账户转入转出详细数据", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = AccountCashDetailReport.class)
    })
    public BaseResult<BasePageResultResponse<AccountCashDetailReport>> placementList(@RequestBody AccountCashDetailParam param, HttpServletRequest request) {
        BaseResult<BasePageResultResponse<AccountCashDetailReport>> response = accountReportService.getAccountReport(param);
        return response;
    }

    @RequestMapping(value = "/placement/export",method = {RequestMethod.POST })
    @ApiOperation(value = "账户转入转出详细数据导出", httpMethod = "POST")
    public void placementExport(@RequestBody AccountCashDetailParam param, HttpServletResponse response) {
        accountReportService.exportAccountReport(param, response);
    }
}
