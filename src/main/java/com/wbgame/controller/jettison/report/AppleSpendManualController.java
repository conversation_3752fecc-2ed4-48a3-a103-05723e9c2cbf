package com.wbgame.controller.jettison.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.mysql.jdbc.StringUtils;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.jettison.report.AppleManualSpendVo;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.report.TableResponse;
import com.wbgame.pojo.jettison.report.param.OperationReportParam;
import com.wbgame.pojo.jettison.report.param.SpendReportParam;
import com.wbgame.service.jettison.report.AppleSpendManualService;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/5
 * @description
 **/
@CrossOrigin
@RestController
@RequestMapping("/report")
@Api(tags = "投放数据管理")
@Slf4j
public class AppleSpendManualController {

    @Resource
    private AppleSpendManualService appleSpendManualService;

    @ApiOperation(value = "苹果两拍手动录入数据列表")
    @PostMapping("/apple/list")
    @ControllerLoggingEnhancer
    public Object appleSpendManualList(@RequestBody SpendReportParam param) {
        PageHelper.startPage(param.getStart(), param.getLimit());
        PageInfo<AppleManualSpendVo> data = new PageInfo<>(appleSpendManualService.appleSpendManualList(param));
        return TableResponse.success(data.getList(), null, data.getTotal());
    }

    @ApiOperation(value = "苹果两拍手动录入数据列表导出")
    @PostMapping("/apple/export")
    @ControllerLoggingEnhancer
    public void appleSpendManualExport(@RequestBody SpendReportParam param, HttpServletResponse response) {
        List<AppleManualSpendVo> data = appleSpendManualService.appleSpendManualList(param);
        export(param, response, data, "苹果ADS消耗录入表_");
    }

    @ApiOperation(value = "苹果两拍手动录入")
    @PostMapping("/apple/insert")
    @ControllerLoggingEnhancer
    public Object appleSpendManualInsert(@RequestBody JSONObject param) {
        List<AppleManualSpendVo> list = new ArrayList<>();
        JSONArray jsonArray = param.getJSONArray("list");
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getObject(i, AppleManualSpendVo.class));
        }

        appleSpendManualService.appleSpendManualInsert(list);
        return TableResponse.success();
    }

    @ApiOperation(value = "苹果两拍手动录入删除")
    @PostMapping("/apple/delete")
    @ControllerLoggingEnhancer
    public Object appleSpendManualDelete(@RequestBody JSONObject param) {
        List<AppleManualSpendVo> list = new ArrayList<>();
        JSONArray jsonArray = param.getJSONArray("list");
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getObject(i, AppleManualSpendVo.class));
        }

        appleSpendManualService.appleSpendManualDelete(list);
        return TableResponse.success();
    }

    @ApiOperation(value = "热云收入修正")
    @PostMapping("/reyun/list")
    @ControllerLoggingEnhancer
    public Object reyunIncomeList(@RequestBody SpendReportParam param) {
        PageHelper.startPage(param.getStart(), param.getLimit());
        PageInfo<AppleManualSpendVo> data = new PageInfo<>(appleSpendManualService.reyunIncomeList(param));
        return TableResponse.success(data.getList(), null, data.getTotal());
    }
    @ApiOperation(value = "热云收入修正 导出")
    @PostMapping("/reyun/export")
    @ControllerLoggingEnhancer
    public void reyunIncomeExport(@RequestBody SpendReportParam param, HttpServletResponse response) {
        List<AppleManualSpendVo> data = appleSpendManualService.reyunIncomeList(param);
        export(param, response, data, "热云收入修正表_");
    }

    private void export(SpendReportParam param, HttpServletResponse response, List<AppleManualSpendVo> data, String fileName) {
        String value = param.getValue();
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        if (!StringUtils.isNullOrEmpty(value)) {
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0; i < split.length; i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, data, headerMap, fileName + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    @ApiOperation(value = "热云收入修正")
    @PostMapping("/reyun/insert")
    @ControllerLoggingEnhancer
    public Object reyunIncomeUpdate(@RequestBody JSONObject param) {
        List<AppleManualSpendVo> list = new ArrayList<>();
        JSONArray jsonArray = param.getJSONArray("list");
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getObject(i, AppleManualSpendVo.class));
        }
        appleSpendManualService.reyunIncomeUpdate(list);
        return TableResponse.success();
    }

    @ApiOperation(value = "热云收入修正 清空修正金额")
    @PostMapping("/reyun/delete")
    @ControllerLoggingEnhancer
    public Object reyunIncomeDelete(@RequestBody JSONObject param) {
        List<AppleManualSpendVo> list = new ArrayList<>();
        JSONArray jsonArray = param.getJSONArray("list");
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getObject(i, AppleManualSpendVo.class));
        }
        appleSpendManualService.reyunIncomeDelete(list);
        return TableResponse.success();
    }

}
