package com.wbgame.controller.jettison.report;


import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.pojo.finance.DnConfigFinance;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.report.MaterialMD5Vo;
import com.wbgame.pojo.jettison.report.dto.CreativeXiaomiReportDTO;
import com.wbgame.pojo.jettison.report.dto.CreativeXiaomiUpdateStatusDto;
import com.wbgame.pojo.jettison.report.param.CreativeSpendReportParam;
import com.wbgame.pojo.jettison.report.param.CreativeXiaomiParam;
import com.wbgame.pojo.jettison.report.param.CreativeXiaomiUpdatePriceParam;
import com.wbgame.pojo.jettison.report.param.MaterialReportParam;
import com.wbgame.service.budgetWarning.platformImpl.XiaomiCampaignPriceService;
import com.wbgame.service.jettison.report.CreativeReportService;
import com.wbgame.task.budgetWarning.HardcoreImpl.XiaomiBudgetWarningTask;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jxl.Sheet;
import jxl.Workbook;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.alicp.jetcache.Cache.logger;

@CrossOrigin
@RestController
@RequestMapping("/creative")
@Api(tags = "创意数据管理")
public class CreativeReportController {

    @Autowired
    CreativeReportService creativeReportService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @ApiOperation(value = "素材报表")
    @PostMapping("/material/list")
    @LoginCheck
    public InfoResult getMaterialReport(@RequestBody MaterialReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.getMaterialReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getMaterialReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "素材报表导出")
    @PostMapping(value = "/material/export")
    @LoginCheck
    public void getMaterialReportExport(@RequestBody MaterialReportParam param, HttpServletResponse response) {
        try {
            creativeReportService.getMaterialReportExport(param,response);
        } catch (Exception e) {
            logger.error("getMaterialReportExport: ", e);
        }
    }

    @ApiOperation(value = "素材折线图")
    @PostMapping("/materialline/list")
    public InfoResult getMaterialLineChart(@RequestBody MaterialReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = param.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            String role = currUserVo.getOrg_id();

            infoResult = creativeReportService.getMaterialLineChart(param, username,role);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getMaterialLineChart: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "快手素材MD5录入")
    @PostMapping("/material/importMD5")
    @LoginCheck
    public String importMD5(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request) {
        //获取登录信息
        CurrUserVo currUserVo = (CurrUserVo) request.getAttribute("LoginUser");
        String username = currUserVo.getLogin_name();
        try {
            if (file == null || file.isEmpty() || (!file.getOriginalFilename().endsWith(".xls") && !file.getOriginalFilename().endsWith(".xlsx"))) {
                return ReturnJson.toErrorJson("上传文件有误，需要excel文件");
            }

            //读取excel文件
            List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(file);
            //录入数据
            List<MaterialMD5Vo> list = new ArrayList<>();
            for (int i = 0; i < arrayLists.size(); i++) {
                if (i == 0) {
                    //第一行为标题，不能为空
                    for (int k = 0;k<4;k++) {
                        if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                            return ReturnJson.toErrorJson("第一行标题不能出现空的情况");
                        }
                    }
                } else {
                    //第二行为内容
                    MaterialMD5Vo vo = new MaterialMD5Vo();
                    //平台
                    vo.setAd_platform(arrayLists.get(i).get(0));
                    //账号
                    vo.setAccount(arrayLists.get(i).get(1));
                    //视频id
                    vo.setVideo_id(arrayLists.get(i).get(2));
                    //md5
                    vo.setMd5(arrayLists.get(i).get(3));
                    vo.setUpdate_owner(username);
                    vo.setCreate_owner(username);

                    if (BlankUtils.checkBlank(vo.getAd_platform()) || BlankUtils.checkBlank(vo.getAccount()) ||
                            BlankUtils.checkBlank(vo.getMd5()) || BlankUtils.checkBlank(vo.getVideo_id())){
                        continue;
                    }

                    list.add(vo);
                }
            }

            int i = creativeReportService.insertMaterialMD5Matchs(list);

            return ReturnJson.success("快手素材MD5录入成功");

        } catch (Exception e) {
            return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:"+e.getMessage());
        }
    }

    @ApiOperation(value = "广告创意报表")
    @PostMapping("/report/list")
    @LoginCheck
    public InfoResult getCreativeReport(@RequestBody CreativeSpendReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.getCreativeReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getCreativeReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "广告创意报表导出")
    @PostMapping(value = "/report/export")
    @LoginCheck
    public void getCreativeReportExport(@RequestBody CreativeSpendReportParam param, HttpServletResponse response) {
        try {
            creativeReportService.getCreativeReportExport(param,response);
        } catch (Exception e) {
            logger.error("getCreativeReportExport: ", e);
        }
    }

    @ApiOperation(value = "创意折线图")
    @PostMapping("/creativeline/list")
    @LoginCheck
    public InfoResult getCreativeLineChart(@RequestBody CreativeSpendReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            String token = param.getToken();
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            String role = currUserVo.getOrg_id();

            infoResult = creativeReportService.getCreativeLineChart(param, username,role);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getCreativeLineChart: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米创意层级投放报表")
    @PostMapping("/xiaomi/list")
    @LoginCheck
    public InfoResult getCreativeXiaomiReport(@RequestBody CreativeXiaomiParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.getCreativeXiaomiReportNew(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米创意层级投放报表获取渠道产品名称")
    @PostMapping("/xiaomi/gameNames")
    @LoginCheck
    public InfoResult getCreativeXiaomigameNames(@RequestBody CreativeXiaomiParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.getCreativeXiaomigameNames(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getCreativeXiaomigameNames: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米创意层级投放报表获取创意id")
    @PostMapping("/xiaomi/creativeId/list")
    @LoginCheck
    public InfoResult getCreativeXiaomiCreativeIdReport(@RequestBody CreativeXiaomiParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.getCreativeXiaomiCreativeIdReport(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米批量改价任务状态查询")
    @PostMapping("/xiaomi/creative/update/status/list")
    @LoginCheck
    public InfoResult xiaomiCreativeUpdateStatusList(@RequestBody CreativeXiaomiUpdateStatusDto param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.xiaomiCreativeUpdateStatusList(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }
    @ApiOperation(value = "小米批量改价任务历史修改查询")
    @PostMapping("/xiaomi/creative/update/record/list")
    @LoginCheck
    public InfoResult xiaomiCreativeUpdateRecords(@RequestBody CreativeXiaomiUpdateStatusDto param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.xiaomiCreativeUpdateRecords(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }

    @Resource
    private XiaomiCampaignPriceService xiaomiCampaignPriceService;

    @ApiOperation(value = "小米创意批量改价")
    @PostMapping("/xiaomi/priceUpdate")
    @LoginCheck
    public InfoResult priceUpdateCreativeXiaomi(@RequestBody CreativeXiaomiUpdatePriceParam para) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = xiaomiCampaignPriceService.updatePrice(para);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("操作失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米创意批量改价(多选)")
    @PostMapping("/xiaomi/batchPriceUpdate")
    @LoginCheck
    public InfoResult batchPriceUpdateCreativeXiaomi(@RequestBody List<CreativeXiaomiUpdatePriceParam> params) {
        InfoResult infoResult = new InfoResult();
        try {
            logger.info("开始小米创意批量改价(多选):开始执行");
            long start = System.currentTimeMillis();
            for (CreativeXiaomiUpdatePriceParam param : params) {
                //获取对应创意
                if (param.getGetCreativeParam() == null) {
                    logger.info("开始小米创意批量改价(多选):无法找到获取创意的条件参数,跳过,任务id={}",param.getTaskId());
                    continue;
                }
                InfoResult res1 = creativeReportService.getCreativeXiaomiCreativeIdReport(param.getGetCreativeParam());
                Map data = (Map) res1.getData();
                if (data.get("list") == null) {
                    logger.info("开始小米创意批量改价(多选):无法找到创意id,跳过,任务id={}",param.getTaskId());
                    continue;
                }
                List<CreativeXiaomiReportDTO> reportList = (List<CreativeXiaomiReportDTO>)data.get("list");
                if (reportList.size() == 0) {
                    logger.info("开始小米创意批量改价(多选):创意数为0,跳过,任务id={}",param.getTaskId());
                    continue;
                }
                param.setCreativeXiaomiParams(reportList);
                xiaomiCampaignPriceService.updatePrice(param);
                logger.info("开始小米创意批量改价(多选):执行成功,创意数量={},任务id={}",reportList.size(),param.getTaskId());
            }
            logger.info("开始小米创意批量改价(多选):结束执行,任务数={},执行时间={}秒",params.size(),(double)(System.currentTimeMillis()-start)/1000);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("操作失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        infoResult.setRet(1);
        infoResult.setMsg("执行成功,请观察创意id汇总");
        return infoResult;
    }

    @Resource
    private XiaomiBudgetWarningTask xiaomiBudgetWarningTask;
    @ApiOperation(value = "小米创意同步按钮")
    @GetMapping("/xiaomi/priceUpdate/sync")
    @LoginCheck
    public InfoResult creativeXiaomiSync() {
        InfoResult infoResult = new InfoResult();
        try {
            xiaomiBudgetWarningTask.xiaomiCampaignInfoUpdate();
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("操作失败");
            logger.error("getXiaomiCreativeReport: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "小米创意层级投放报表导出")
    @PostMapping(value = "/xiaomi/export")
    @LoginCheck
    public void getCreativeXiaomiReportExport(@RequestBody CreativeXiaomiParam param, HttpServletResponse response) {
        try {
            creativeReportService.getCreativeXiaomiReportExport(param,response);
        } catch (Exception e) {
            logger.error("getXiaomiCreativeReportExport: ", e);
        }
    }
    @ApiOperation(value = "人效管理")
    @PostMapping("/effective/manage/list")
    @LoginCheck
    public InfoResult effectiveManage(@RequestBody MaterialReportParam param) {
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = creativeReportService.effectiveManage(param);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("effectiveManage error: ", e.getMessage());
            e.printStackTrace();
        }
        return infoResult;
    }
    @ApiOperation(value = "人效管理导出")
    @PostMapping(value = "/effective/manage/export")
    @LoginCheck
    public void effectiveManageExport(@RequestBody MaterialReportParam param, HttpServletResponse response) {
        try {
            creativeReportService.effectiveManageExport(param,response);
        } catch (Exception e) {
            logger.error("getXiaomiCreativeReportExport: ", e);
        }
    }
    
}
