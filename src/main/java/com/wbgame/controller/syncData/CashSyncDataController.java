package com.wbgame.controller.syncData;

import com.google.common.collect.Lists;
import com.wbgame.annotation.ControllerEnhancer;
import com.wbgame.aop.ApiNeed;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.SyncDataServiceHolder;
import com.wbgame.pojo.adv2.resultEntity.ResultBean;
import com.wbgame.pojo.adv2.resultEntity.SyncResult;
import com.wbgame.service.syncdata.CashSyncDataService;
import com.wbgame.service.syncdata.impl.KuaishouXyxCashService;
import com.wbgame.task.CashUpdateTask;
import com.wbgame.task.StatisticReportTask;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @date 4.12
 * @description
 **/

@RestController
@RequestMapping("/adv2/cash/syncdata")
public class CashSyncDataController {

    @Resource
    private SyncDataServiceHolder syncDataServiceHolder;
    @Resource
    private StatisticReportTask statisticReportTask;
    @Resource
    private CashUpdateTask cashUpdateTask;
    @Resource
    private KuaishouXyxCashService kuaishouXyxCashService;

    @ControllerEnhancer
    @PostMapping("/business")
    public String syncData(@ApiNeed({
            "business",
            "startDate",
            "endDate"}) String business, String startDate, String endDate) {
        CashSyncDataService cashSyncDataService = syncDataServiceHolder.getBusinessMap().get(business);
        if (cashSyncDataService == null) {
            return ReturnJson.toErrorJson(String.format("no business named %s", business));
        }
        SyncResult syncResult = cashSyncDataService.syncData(startDate, endDate);
        return ReturnJson.success(syncResult.getMessage());
    }

    @ControllerEnhancer
    @PostMapping("/business/batch")
    public String batchSyncData(@ApiNeed({
            "businesses",
            "startDate",
            "endDate"}) String businesses, String startDate, String endDate) {

        List<String> busArr = Lists.newArrayList(businesses.split(","));
        if (CollectionUtils.isEmpty(busArr)) {
            return ReturnJson.success("no valid platform");
        }

        for (String s : busArr) {
            if (!syncDataServiceHolder.getBusinessMap().containsKey(s)) {
                return ReturnJson.toErrorJson(String.format("%s is not a platform", s));
            }
        }

        try {
            String ret = cashUpdateTask.lockedAsyncGetChinaReport(startDate, endDate, busArr, 1);
            if (BlankUtils.isNotBlank(ret)) {
                return ret;
            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson(e.getMessage());
        }
        return ReturnJson.success();
    }

    @ControllerEnhancer
    @PostMapping("/statistic/update")
    public String statisticUpdate(String date) {
        statisticReportTask.statisticInvoke(date);
        return ReturnJson.success();
    }

    @ControllerEnhancer
    @PostMapping("/ksxyx/sync")
    public String ksxyxSync(@ApiNeed({
            "startDate",
            "endDate"}) String startDate, String endDate) throws ParseException {
        List<String> hyphenDateList = DateUtils.getHyphenDateList(startDate, endDate);
        for (String date : hyphenDateList) {
            kuaishouXyxCashService.ksXyxSync(date);
        }
        return ReturnJson.success();
    }
}
