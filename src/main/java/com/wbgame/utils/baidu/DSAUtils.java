package com.wbgame.utils.baidu;

import java.io.IOException;
import java.io.StringWriter;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;

import org.bouncycastle.openssl.PEMWriter;


public class DSAUtils {
    public static void main(String[] args) throws NoSuchAlgorithmException, IOException {

        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("DSA");
        keyGen.initialize(1024);
        KeyPair keypair = keyGen.genKeyPair();
        PrivateKey privateKey = keypair.getPrivate();
        PublicKey publicKey = keypair.getPublic();

        System.out.println(toPemKey(privateKey));
        System.out.println(toPemKey(publicKey));


    }

    private static String toPemKey(Object key) throws IOException {
        StringWriter stringWriter = new StringWriter();
        PEMWriter pemWriter = new PEMWriter(stringWriter);
        pemWriter.writeObject(key);
        pemWriter.flush();
        pemWriter.close();

        return stringWriter.toString();
    }
}
