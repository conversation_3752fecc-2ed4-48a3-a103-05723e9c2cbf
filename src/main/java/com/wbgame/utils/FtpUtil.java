package com.wbgame.utils;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.io.InputStream;
import java.net.SocketException;

/**
 * <AUTHOR>
 * @Classname FtpUtil
 * @Description TODO
 * @Date 2021/3/15 18:55
 */
public class FtpUtil {

    private static Logger logger = Logger.getLogger(FtpUtil.class);

    /**
     * 获取FTPClient对象
     *
     * @param ftpHost     服务器IP
     * @param ftpPort     服务器端口号
     * @param ftpUserName 用户名
     * @param ftpPassword 密码
     * @return FTPClient
     */
    public FTPClient getFTPClient(String ftpHost, int ftpPort,
                                  String ftpUserName, String ftpPassword) throws Exception{

        FTPClient ftp = null;
        ftp = new FTPClient();
        // 连接FPT服务器,设置IP及端口
        ftp.connect(ftpHost, ftpPort);
        // 设置用户名和密码
        ftp.login(ftpUserName, ftpPassword);
        // 设置连接超时时间,5000毫秒
        ftp.setConnectTimeout(50000);
        // 设置中文编码集，防止中文乱码
        ftp.setControlEncoding("UTF-8");
        ftp.setBufferSize(1024 * 10);
        if (!FTPReply.isPositiveCompletion(ftp.getReplyCode())) {
            logger.info("未连接到FTP，用户名或密码错误");
            ftp.disconnect();
        } else {
            logger.info("FTP连接成功");
        }
        return ftp;
    }

    /**
     * 关闭FTP方法
     *
     * @param ftp
     * @return
     */
    public boolean closeFTP(FTPClient ftp) {

        try {
            ftp.logout();
        } catch (Exception e) {
            logger.error("FTP关闭失败");
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                    logger.error("FTP关闭失败");
                }
            }
        }

        return false;

    }

    /**
     * FTP文件上传工具类
     *
     * @param ftp
     * @param in
     * @param fileName
     * @param ftpPath
     * @return
     */
    public boolean uploadFile(FTPClient ftp, InputStream in, String fileName, String ftpPath) {
        boolean flag = false;
        try {
            // 设置PassiveMode传输
            ftp.enterLocalPassiveMode();
            //设置二进制传输，使用BINARY_FILE_TYPE，ASC容易造成文件损坏
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            //判断FPT目标文件夹时候存在不存在则创建
            if (!ftp.changeWorkingDirectory(ftpPath)) {
                ftp.makeDirectory(ftpPath);
            }
            //跳转目标目录
            ftp.changeWorkingDirectory(ftpPath);

            //上传文件
//            File file = new File(filePath);
//            in = new FileInputStream(file);
            flag = ftp.storeFile(new String(fileName.getBytes("UTF-8"), "ISO-8859-1"), in);
            if (flag) {
                logger.info("上传成功");
            } else {
                logger.error("上传失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("上传失败");
        } finally {
            try {
                in.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return flag;
    }


    /**
     * FTP文件上传工具类
     *
     * @param ftp
     * @param fileName
     * @param ftpPath
     * @return
     */
    public boolean deleteFile(FTPClient ftp,String fileName, String ftpPath) {
        boolean flag = false;
        try {
            //判断FPT目标文件夹时候存在不存在则创建
            if (ftp.changeWorkingDirectory(ftpPath)) {
                //跳转目标目录
                ftp.changeWorkingDirectory(ftpPath);
                flag = ftp.deleteFile(fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("删除文件失败");
        }
        return flag;
    }

}
