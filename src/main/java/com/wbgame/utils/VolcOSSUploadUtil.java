package com.wbgame.utils;

import com.alibaba.fastjson.JSONObject;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;
import com.volcengine.tos.model.object.ObjectMetaRequestOptions;
import com.volcengine.tos.model.object.PutObjectFromFileInput;
import com.volcengine.tos.model.object.PutObjectFromFileOutput;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;

import java.io.File;
import java.io.InputStream;

/**
 * OSS上传资源相关函数工具-火山引擎
 * <AUTHOR>
 *
 */
public class VolcOSSUploadUtil {

	public static final String endpoint = "tos-cn-guangzhou.volces.com";
    public static final String region = "cn-guangzhou";
    public static final String accessKey = "AKLTZjNlZjQ3NTkyOTdmNDYwYThhMmVkNTBhZWMyZGE3MDc";
    public static final String secretKey = "TXprMFpXSmpaR0ZtWlRNNU5HWXhOamt4T0RReVl6ZGhOamcyWXpFeU5XVQ==";

	/**
	 * 使用普通上传方式，上传常规文件到OSS
	 * @param bucketName 上报到OSS的存储空间Bucket，例如 nnjy-oa
	 * @param objectName 上报到OSS的对象路径，例如 mmreq/wjy0.log
	 * @param file 本地文件内容，例如 new File("temple.png")
	 * @param objectMetadata 元数据配置对象，传null则不单独设置
	 * @return
	 */
	public static String uploadDataOSS(String bucketName,String objectName,File file,ObjectMetaRequestOptions objectMetadata) {
		JSONObject result = new JSONObject();
		try {

			// 创建OSSClient实例。
			TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);
            PutObjectFromFileInput putObjectFile = new PutObjectFromFileInput().setBucket(bucketName).setKey(objectName).setFile(file);
            
            // 额外设置元策略
            if(objectMetadata != null){
            	putObjectFile.setOptions(objectMetadata);
            }
			PutObjectFromFileOutput output = tos.putObjectFromFile(putObjectFile);
			
			result.put("errCode", "200");
			result.put("errMsg", "success");
		} catch (TosClientException oe) {
			
			result.put("errCode", oe.getCode());
			result.put("errMsg", "错误信息: "+oe.getMessage());
		} catch (TosServerException oe) {
			
			result.put("errCode", oe.getCode());
			result.put("errMsg", "错误信息: "+oe.getMessage());
        } catch (Exception e) {
			e.printStackTrace();
			
			result.put("errCode", "500");
            result.put("errMsg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	/**
	 * 使用普通上传方式，上传常规文件到OSS
	 * @param bucketName 上报到OSS的存储空间Bucket，例如 nnjy-oa
	 * @param objectName 上报到OSS的对象路径，例如 mmreq/wjy0.log
	 * @param inputStream 文件输入流，例如new FileInputStream("temple.png")
	 * @param objectMetadata 元数据配置对象，传null则不单独设置
	 * @return
	 */
	public static String uploadDataOSS(String bucketName,String objectName,InputStream inputStream,ObjectMetaRequestOptions objectMetadata) {
		
        JSONObject result = new JSONObject();
        try {

			// 创建OSSClient实例。
			TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);
			PutObjectInput putObjectInput = new PutObjectInput().setBucket(bucketName).setKey(objectName).setContent(inputStream);

            // 额外设置元策略
            if(objectMetadata != null){
            	putObjectInput.setOptions(objectMetadata);
            }
			PutObjectOutput output = tos.putObject(putObjectInput);
			
			result.put("errCode", "200");
			result.put("errMsg", "success");
		} catch (TosClientException oe) {
			
			result.put("errCode", oe.getCode());
			result.put("errMsg", "错误信息: "+oe.getMessage());
		} catch (TosServerException oe) {
			
			result.put("errCode", oe.getCode());
			result.put("errMsg", "错误信息: "+oe.getMessage());
        } catch (Exception e) {
			e.printStackTrace();
			
			result.put("errCode", "500");
            result.put("errMsg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

}
