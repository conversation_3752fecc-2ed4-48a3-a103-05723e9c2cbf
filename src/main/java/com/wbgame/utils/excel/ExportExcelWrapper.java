package com.wbgame.utils.excel;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collection;
 
import javax.servlet.http.HttpServletResponse;

import com.wbgame.utils.BlankUtils;
 
 
/**
 * 包装类
 * <AUTHOR>
 *
 * @param <T>
 */
public class ExportExcelWrapper<T> extends ExportExcelUtil<T> {
	/**
	 * <p>
	 * 导出带有头部标题行的Excel <br>
	 * 时间格式默认：yyyy-MM-dd hh:mm:ss <br>
	 * </p>
	 * 
	 * @param title 表格标题
	 * @param headers 头部标题集合
	 * @param dataset 数据集合
	 * @param out 输出流
	 * @param dateFormat 时间格式
	 * @param version 2003 或者 2007，不传时默认生成2003版本
	 */
	public void exportExcel(String fileName, String title, String[] headers, Collection<T> dataset, HttpServletResponse response,OutputStream out,String version,String dateFormate) {
		try {
			response.setContentType("application/vnd.ms-excel");  
    		response.addHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(fileName, "UTF-8") + ".xls");
			if(BlankUtils.checkBlank(version) || EXCEL_FILE_2003.equals(version.trim())){
				exportExcel2003(title, headers, dataset, out, dateFormate);
			}else{
				exportExcel2007(title, headers, dataset, out, dateFormate);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * <p>
	 * 导出带有头部标题行的Excel <br>
	 * 时间格式默认：yyyy-MM-dd hh:mm:ss <br>
	 * </p>
	 *
	 * @param title 表格标题
	 * @param headers 头部标题集合
	 * @param dataset 数据集合
	 * @param out 输出流
	 * @param dateFormat 时间格式
	 * @param version 2003 或者 2007，默认生成2007版本
	 */
	public void exportExcelXLSX(String fileName, String title, String[] headers, Collection<T> dataset, HttpServletResponse response,OutputStream out,String version,String dateFormate) {
		try {
			response.setContentType("application/vnd.ms-excel");
    		response.addHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
			if(BlankUtils.checkBlank(version) || EXCEL_FILE_2003.equals(version.trim())){
				exportExcel2003(title, headers, dataset, out, dateFormate);
			}else{
				exportExcel2007(title, headers, dataset, out, dateFormate);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}