package com.wbgame.utils.redpack;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.SortedMap;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.wbgame.utils.BlankUtils;


/**
 * XML 数据接收对象转换工具类
 *
 */
public class XMLConverUtil {
	
	public static void main(String[] args) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("appid", "111");
		map.put("attach", "222");
		map.put("body", "333");
		map.put("mch_id", "444");
		String converterXml = converterXml(map);
		System.out.println(converterXml);
		
		String[] mm = {
				"appid",
				"mch_id",
				"nonce_str",
				"sign",
				"body",
				"attach",
				"out_trade_no",
				"total_fee",
				"notify_url",
				"trade_type"
		};
		String[] nn = {
				"小程序ID",
				"商户号",
				"随机字符串",
				"签名",
				"商品描述",
				"附加数据",
				"商户订单号",
				"标价金额",
				"通知地址",
				"交易类型"
		};
		for (int i = 0; i < nn.length; i++) {
			System.out.println("map.put(\""+mm[i]+"\", \"\");");
		}
		
	}

	/**
	 * 将Xml格式的字符串转换为Map对象
	 * @param strXml
	 * @return
	 */
	public static Map<String, String> parseXml(String strXml) {
		Document document;
		try {
			document = DocumentHelper.parseText(strXml);
			return parseXml(document);
		} catch (DocumentException e) {
			e.printStackTrace();
		}  
		return null;
	}
	
	public static Map<String, String> parseXml(InputStream inputStream) {
		// 读取输入流
		try {
			SAXReader reader = new SAXReader();
			Document document = reader.read(inputStream);
			return parseXml(document);
		} catch (DocumentException e) {
			e.printStackTrace();
		} finally {
			// 释放资源
			try {
				inputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			inputStream = null;
		}
		return null;
	}
	
	public static Map<String, String> parseXml(Document document) {
		// 将解析结果存储在HashMap中
		Map<String, String> map = new HashMap<String, String>();
		// 得到xml根元素
		Element root = document.getRootElement();
		// 得到根元素的所有子节点
		List<Element> elementList = root.elements();

		// 遍历所有子节点
		for (Element e : elementList)
			map.put(e.getName(), e.getText());
		
		return map;
	}
	
	/**
	 * 将Map数据转换为xml标签中的属性字段
	 * @param flag 标签名
	 * @param dataMap 属性集合
	 * @return
	 */
	public static String converterXmlProperties(String flag, Map<String, Object> dataMap) {
		
		StringBuilder strBuilder = new StringBuilder();
		strBuilder.append("<").append(flag);
		Iterator<Entry<String, Object>> iterator = dataMap.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<String, Object> next = iterator.next();
			strBuilder.append(" "+next.getKey()).append("=").append("\""+next.getValue()+"\"");
		}
		strBuilder.append(">");
		return strBuilder.toString();
	}
	
	/**
	 * 将Map数据转换为xml格式的字符串
	 * @param dataMap
	 * @return
	 */
	public static String converterXml(Map<String, Object> dataMap) {
    
        StringBuilder strBuilder = new StringBuilder();
        strBuilder.append("<xml>\n");
        Iterator<Entry<String, Object>> iterator = dataMap.entrySet().iterator();
        while(iterator.hasNext()){
        	Entry<String, Object> next = iterator.next();
        	strBuilder.append("<").append(next.getKey()).append(">");
            strBuilder.append(next.getValue());
            strBuilder.append("</").append(next.getKey()).append(">\n");
        }
        strBuilder.append("</xml>");
        return strBuilder.toString();
    }
	/**
	 * 将Map数据转换为xml格式的字符串
	 * @param dataMap
	 * @return
	 */
	public static String converterXml(SortedMap<String, String> dataMap) {
		
		StringBuilder strBuilder = new StringBuilder();
		strBuilder.append("<xml>");
		Iterator<Entry<String, String>> iterator = dataMap.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<String, String> next = iterator.next();
			strBuilder.append("<").append(next.getKey()).append(">");
			strBuilder.append(next.getValue());
			strBuilder.append("</").append(next.getKey()).append(">");
		}
		strBuilder.append("</xml>");
		return strBuilder.toString();
	}

}
