package com.wbgame.utils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import B.D;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;


public class DateUtil {
	public final static String DATE_A="yyyy-MM-dd";
	public final static String DATE_B="yyyy-MM-dd HH:mm:ss";
	public final static String DATE_C="yyyyMMddHHmmss";
	public final static String DATE_D="yyyyMMdd-HHmmss-SS";
	public final static String DATE_E="M月d日";
	public final static String DATE_F="MM-dd";
	public final static String DATE_T="yyyyMMdd";
	public final static String DATE_Y="yyyyMM";
	public final static String DATE_G="yyyy/M/d";
	public final static int RATE_M=60;

	public final static int RATE_H=24;
	private static final long RATE_D = 24 * 60 * 60;

	public final static int RATE_MS=1000;

	public static int getTimeStamp() {
		return (int)(System.currentTimeMillis()/1000);
	}

	/**
	 * 获取当前年和月份
	 * @return today  当前年和月份  yyyyMM格式
	 */
	public static String getYearAndMouth(){
		String ym = DateTime.now().toString(DateUtil.DATE_Y);
		return ym;
	}

	/**
	 * 获取当天日期
	 * @return today  当前日期  yyyyMMdd格式
	 */
	public static String getToday(){
		String today = DateTime.now().toString(DateUtil.DATE_T);
		return today;
	}

	/**
	 * 获取昨日日期
	 * @return today  昨日日期  yyyyMMdd格式
	 */
	public static String getyesterday(){
		String yesterday =  DateTime.now().minusDays(1).toString(DateUtil.DATE_T);
		return yesterday;
	}

	/**
	 * 获取昨日日期
	 * @return today  昨日日期  yyyy-MM-dd格式
	 */
	public static String getTypeAyesterday(){
		String yesterday =  DateTime.now().minusDays(1).toString(DateUtil.DATE_A);
		return yesterday;
	}

	/**
	 * 字符串时间转sql Timestamp
	 * @return v    yyyy-MM-dd HH:mm:ss格式
	 */
	public static Timestamp dateTimeStrToSqlStamp(String v) throws Exception {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		if (!v.contains(".")){
			v+=".000";
		}
		return new Timestamp(sdf.parse(v).getTime());
	}

	public static Timestamp dateTimeStrToSqlStamp1(String v)  {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
			if (!v.contains(".")){
				v+=".000";
			}
			return new Timestamp(sdf.parse(v).getTime());
		} catch (ParseException e) {
			return new Timestamp(System.currentTimeMillis());
		}
	}

	/**
	 * 将时间转换为时间戳
	 * @Title : dateToStamp
	 * @throws ：ParseException
	 */
	public static long dateToStamp(String s)throws ParseException{
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_B);
		Date date = simpleDateFormat.parse(s);
		return date.getTime()/RATE_MS;
	}

	public static long dateToStamp1(String s){
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_B);
			Date date;
			date = simpleDateFormat.parse(s);
			return date.getTime()/RATE_MS;
		} catch (ParseException e) {
			return System.currentTimeMillis();
		}
	}

	/**
	 * 字符串时间转Date
	 * @Title : StrToDate
	 * @throws ：ParseException
	 */
	public static Date StrToDate(String s)throws ParseException{
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_A);
		Date date = simpleDateFormat.parse(s);
		return date;
	}

	/**
	 * 根据字符串日期 获取当日最早时间戳及最晚时间戳  （秒级别）
	 * @Title : getDateTimeStamp
	 * @throws ：ParseException
	 */
	public static Map<String,Long> getDateTimeStamp(String strDate)throws ParseException{
		Map<String,Long> timeMap=new HashMap<String, Long>();
		String startTime=strDate+" 00:00:00";
		String endTime=strDate+" 23:59:59";
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_B);
		Date startDate = simpleDateFormat.parse(startTime);
		Date endtDate = simpleDateFormat.parse(endTime);
		timeMap.put("startTime", startDate.getTime()/RATE_MS);
		timeMap.put("endTime", endtDate.getTime()/RATE_MS);
		return timeMap;
	}

	/**
	 * 根据日期获取 星期
	 * @param date
	 * @return
	 */
	public static String dateToWeek(Date date) {
		String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
		Calendar cal = Calendar.getInstance();
		try {
			cal.setTime(date);
		} catch (Exception e) {
			e.printStackTrace();
		}
		//一周的第几天
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0)
			w = 0;
		return weekDays[w];
	}

	/**
	 * 计算两时间戳想差多少天
	 * @param : firstOpenTime
	 * @param : nowTime
	 * @throws ：
	 */
	public static int intervalDays(Timestamp firstOpenTime,Timestamp nowTime)throws ParseException{
		LocalDate start = new LocalDate(firstOpenTime);
		LocalDate end = new LocalDate(nowTime);
		int days = Days.daysBetween(start,end).getDays();
		return days;
	}

	/**
	 　　 *字符串的日期格式的计算
	 　　 */
	public static int daysBetween(String smdate,String bdate) throws ParseException{
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		Calendar cal = Calendar.getInstance();
		cal.setTime(sdf.parse(smdate));
		long time1 = cal.getTimeInMillis();
		cal.setTime(sdf.parse(bdate));
		long time2 = cal.getTimeInMillis();
		long between_days=(time2-time1)/(1000*3600*24);

		return Integer.parseInt(String.valueOf(between_days));
	}

	/**
	 * 计算前N个小时时间
	 * @param : n 几小时前
	 */
	public static String  getBeforeNHoursTime(int n){
		SimpleDateFormat df = new SimpleDateFormat(DATE_B);
		Calendar calendar = Calendar.getInstance();
		int now = calendar.get(Calendar.HOUR);
		calendar.set(Calendar.HOUR, now-n);
		return df.format(calendar.getTime());
	}

	/**
	 * 时间加/减 N分钟
	 * @param dateTime
	 * @param n
	 * @throws ParseException
	 */
	public static  long getTime(String dateTime,int n) throws ParseException{
		SimpleDateFormat df = new SimpleDateFormat(DateUtil.DATE_B);
		Date date =df.parse(dateTime);
		long time=date.getTime()/DateUtil.RATE_MS + n*DateUtil.RATE_M;
		return time;
	}

	/**
	 * 时间加一分钟
	 * @return dateTime
	 * @throws ParseException
	 */
	public static  long addTime(String dateTime) throws ParseException{
		SimpleDateFormat df = new SimpleDateFormat(DateUtil.DATE_B);
		Date date =df.parse(dateTime);
		long time=date.getTime()/DateUtil.RATE_MS + 1*DateUtil.RATE_M;
		return time;
	}

	/**
	 * 时间减一分钟
	 * @return dateTime
	 * @throws ParseException
	 */
	public static  long subtracTime(String dateTime) throws ParseException{
		SimpleDateFormat df = new SimpleDateFormat(DateUtil.DATE_B);
		Date date =df.parse(dateTime);
		long time=date.getTime()/DateUtil.RATE_MS -1*DateUtil.RATE_M;
		return time;
	}

	/**
	 * 根据时间戳(毫秒)计算所在时间段
	 * @param timeStamp
	 * @return timePeriod  1:10点之前  2:10点-14点  3:14点-18点  4:18点之后
	 */
	public static int getTimePeriod(Long timeStamp) throws ParseException {
		int timePeriod=0;
		SimpleDateFormat df=new SimpleDateFormat(DATE_B);
		String ts=df.format(timeStamp);//2020-09-18 17:24:57
		String[] dateArr=ts.split(" ");
		String[] timeArr=dateArr[1].split(":");
		int hour=Integer.parseInt(timeArr[0]);
		if(hour>=18){
			timePeriod=4;
		}else if(hour>=14&&hour<18){
			timePeriod=3;
		}else if(hour>=10&&hour<14){
			timePeriod=2;
		}else{
			timePeriod=1;
		}
		return timePeriod;
	}

	/**
	 * 获取当前时间 Timestamp
	 */
	public static Timestamp getTimes() throws ParseException {
		return getTimes(Long.valueOf(System.currentTimeMillis()));
	}

	/**
	 * 时间戳(毫秒)转Timestamp
	 */
	public static Timestamp getTimes(Long timeStamp) throws ParseException {
		SimpleDateFormat df=new SimpleDateFormat(DATE_B);
		String ts=df.format(timeStamp);
		Timestamp ti=Timestamp.valueOf(ts);
		return ti;
	}

	/**
	 * Timestamp转秒
	 */
	public static long getSeconds(Timestamp t) throws ParseException {
		//定义时间格式?
		SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_C);
		String str = dateFormat.format(t);
		//此处转换为秒数??
		long millionSeconds = dateFormat.parse(str).getTime()/1000;
		return millionSeconds;
	}

	/**
	 * 获取过去第几天的日期
	 *
	 * @param past
	 * @return
	 */
	public static String getPastDate(int past) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
		Date today = calendar.getTime();
		SimpleDateFormat format = new SimpleDateFormat(DATE_T);
		String result = format.format(today);
		return result;
	}

	/**
	 * 获取过去第几天的日期
	 *
	 * @param past
	 * @return
	 */
	public static String getTypeAPastDate(int past) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
		Date today = calendar.getTime();
		SimpleDateFormat format = new SimpleDateFormat(DATE_A);
		String result = format.format(today);
		return result;
	}

	/**
	 * 获取两个日期间的所有日期
	 * @param startTime 开始时间
	 * @param endTime    结束时间
	 * @return days    日期列表  yyyy-MM-dd格式
	 */
	public static List<String> getDays(String startTime, String endTime) {
		// 返回的日期集合
		List<String> days = new ArrayList<String>();

		DateFormat dateFormat = new SimpleDateFormat(DATE_A);
		try {
			Date start = dateFormat.parse(startTime);
			Date end = dateFormat.parse(endTime);

			Calendar tempStart = Calendar.getInstance();
			tempStart.setTime(start);

			Calendar tempEnd = Calendar.getInstance();
			tempEnd.setTime(end);
			tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
			while (tempStart.before(tempEnd)) {
				days.add(dateFormat.format(tempStart.getTime()));
				tempStart.add(Calendar.DAY_OF_YEAR, 1);
			}

		} catch (ParseException e) {
			e.printStackTrace();
		}

		return days;
	}

	public static boolean isInDate(String beginDay,String endDay) {
		boolean ret = false;
		String curDate = DateUtil.getDate();

		if(curDate.compareTo(beginDay)>=0 && curDate.compareTo(endDay)<=0) {
			ret = true;
		} else {
			ret = false;
		}
		return ret;
	}


	/**
	 * 获取精确到秒的时间戳
	 * @return
	 */
	public static int getSecondTimestamp(Date date){
		if (null == date) {
			return 0;
		}
		String timestamp = String.valueOf(date.getTime());
		int length = timestamp.length();
		if (length > 3) {
			return Integer.valueOf(timestamp.substring(0,length-3));
		} else {
			return 0;
		}
	}

	/**
	 * 获取当前时间前N天的时间
	 * @return
	 */
	public static long getPastDaySecondTimestamp(int past){
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, past);
		long fiveDaysAgo = cal.getTimeInMillis();
		return fiveDaysAgo;
	}

	/**
	 * 获取某日期后的第几天
	 * @param  nowDate
	 * @param addDays
	 * @return
	 */
	public static String getNextDay(String nowDate,int addDays){
		DateFormat dateFormat = new SimpleDateFormat(DATE_A);
		Date date;
		try {
			date = dateFormat.parse(nowDate);
			Calendar   calendar   =   new   GregorianCalendar();
			calendar.setTime(date);
			calendar.add(calendar.DATE,addDays);//把日期往后增加N天.整数往后推,负数往前移动
			date=calendar.getTime();   //这个时间就是日期往后推N天的结果
			String putDate = dateFormat.format(date); //增加N天后的日期
			return putDate;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	//自动检测字符串形式然后转换
	public static Date strToDate(String dateStr) {
		Date date=null;
		SimpleDateFormat sdf=null;
		if (dateStr == null || dateStr.equals("")) {
			throw new RuntimeException("DateUtil.strToDate():" +dateStr);
		} else if(dateStr.indexOf(":")>0){
			sdf = new SimpleDateFormat(DATE_B);
		} else if(dateStr.indexOf("-")>0){
			sdf = new SimpleDateFormat(DATE_A);
		} else if(dateStr.length()==14){
			sdf = new SimpleDateFormat(DATE_C);
		}
		try {
			if(sdf!=null){
				date=sdf.parse(dateStr);
			}
		} catch (Exception e) {
			throw new RuntimeException("DateUtil.strToDate():" +dateStr);
		}
		return date;
	}

	//特殊的日期格式转换
	public static Date strToDate(String dateStr, String dateFormat) {
		if (dateStr == null || dateStr.equals("")) {
			return null;
		}
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			return sdf.parse(dateStr);
		} catch (Exception e) {
			throw new RuntimeException("DateUtil.strToDate():" + e.getMessage());
		}
	}

	//普通的当前时间转字符串方法，格式为yyyy-MM-dd
	public static String getDate() {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_A);
		return sdf.format(new Date());
	}

	public static String getDate(java.util.Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_A);
		return sdf.format(date);
	}

	public static String getDateTime() {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_B);
		return sdf.format(new Date());
	}

	public static String getTime() {
		String ret;
		String datestr;
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_B);
		datestr = sdf.format(new Date());
		ret = datestr.substring(11, datestr.length());
		return ret;
	}

	public static String getDateTime(java.util.Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_B);
		return sdf.format(date);
	}

	public static String getTwoHoursAgoTime() {
		SimpleDateFormat dft=new SimpleDateFormat(DATE_B);
		Date date = new Date();
		Calendar   dar=Calendar.getInstance();
		dar.setTime(date);
		dar.add(java.util.Calendar.HOUR_OF_DAY, -2);
		return dft.format(dar.getTime());
	}

	/**
	 * 根据两个时间计算时间差  返回时间的单位：秒；
	 * 传入时间格式：16:20:24
	 * @param time1  现在的时间
	 * @param time2 过去的时间
	 * @return timeusage 时间差
	 */
	public static int getUsageOfTime(String time1, String time2) {
		String[] timeNow = new String[3];
		String[] timeOld = new String[3];
		timeNow = time1.split(":");
		timeOld = time2.split(":");
		int h=0;
		int m=0;
		int s=0;

		if (!timeNow[0].equals(timeOld[0])) {
			h = Integer.valueOf(timeNow[0])-Integer.valueOf(timeOld[0]);
		}
		if (!timeNow[1].equals(timeOld[1])) {
			m = Integer.valueOf(timeNow[1])-Integer.valueOf(timeOld[1]);
		}
		if (!timeNow[2].equals(timeOld[2])) {
			s = Integer.valueOf(timeNow[2])-Integer.valueOf(timeOld[2]);
		}
		int timeusage=h*3600+m*60+s;
		return timeusage;

	}

	/**
	 * 针对数据库取出的datetime有小数点的问题做一个处理，如：
	 * 将2014-01-03 14:30:25.0转换为2014-01-03 14:30:25
	 * @param dateTime
	 * @return
	 */
	public static String formatDateTime(String dateTime){
		String a[] = new String[2];
		String ret = "";
		if (dateTime!=null&&!dateTime.equals("")) {
			a = dateTime.split("\\.");
			ret = a[0];
		}
		return ret;
	}

	/**
	 * 从date中获取需要的内容：年 year，月 month，日 day
	 *
	 * @param content 所需要的字符串：year或者month或者day
	 * @param date 格式必须为：2014-03-15
	 * @return
	 */
	public static String getStrFromDate(String content,String date){
		String ret = "";
		String[] a = new String[3];
		if (date!=null&&!date.isEmpty()) {
			a = date.split("-");
		}

		if (content.equals("year")) {
			ret = a[0];
		}else if(content.equals("month")){
			ret = a[1];
		}else if(content.equals("day")){
			ret = a[2];
		}
		return ret;
	}

	/**
	 * 第一个时间是否在第二时间之后   yyyy-MM-dd HH:mm:ss
	 * @param date1 date2
	 * @return boolean
	 * @throws ParseException
	 */
	public static boolean isAfter(String date1,String date2) {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_A);
		Date dateA;
		Date dateB;
		boolean flag = false;
		try {
			dateA = sdf.parse(date1);
			dateB = sdf.parse(date2);
			flag = dateA.after(dateB);
		} catch (ParseException e) {
			e.printStackTrace();
			flag=false;
		}

		return flag;
	}

	/**
	 * 获取当前日期的前一天日期
	 * @return date java.util.Date类型
	 */
	public static Date getYesterday(){
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		date = (Date) calendar.getTime();
		return date;
	}

	/**
	 * 根据日期获取 星期
	 *
	 * @param datetime
	 * @return
	 */
	public static String dateToWeek(String datetime) {

		SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
		String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
		Calendar cal = Calendar.getInstance();
		Date date;
		try {
			date = f.parse(datetime);
			cal.setTime(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		//一周的第几天
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0)
			w = 0;
		return weekDays[w];
	}

	/**
	 * 同一年
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static Boolean isSameYear(Date date1,Date date2){
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
		return isSameYear;
	}

	/**
	 * 同一月
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static Boolean isSameMonth(Date date1,Date date2){
		if(!isSameYear(date1,date2)){
			return false;
		}
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		boolean isSameMonth = cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		return isSameMonth;
	}

	/**
	 * 同一月
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static Boolean isSameMonth(String date1,String date2){
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date d1 = sdf.parse(date1);
			Date d2 = sdf.parse(date2);
			return isSameMonth(d1,d2);
		}catch (Exception e) {
			return false;
		}
	}

	/**
	 * 判断是否为标准日期格式
	 * @param date_str
	 * @param format
	 * @return
	 */
	public static Boolean isFormatTime(String date_str,String format){
		boolean flag = false;
		DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern(format);
		try {
			DateTime dateTime = DateTime.parse(date_str,dateTimeFormat);
			flag = true;
		}catch (Exception e){

		}
		return flag;
	}

	public final static String DATE_HOUR="HH";
	private static final SimpleDateFormat HH = new SimpleDateFormat(DATE_HOUR);

	public static String getQualifyHour(long timestamp) {
		Date date = new Date(timestamp);
		return HH.format(date);
	}

	/**
	 * 获取指定日期的前一周
	 * @param time 日期
	 * @return
	 */
	public static String getlastweek (String time) throws java.text.ParseException {

		Calendar cal = Calendar.getInstance();
		cal.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(time));
		int d = 0;
		if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
			d = -6;
		} else {
			d = 2 - cal.get(Calendar.DAY_OF_WEEK);
		}
		cal.add(Calendar.DAY_OF_WEEK, d);
		// 所在周开始日期
		String data1 = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
		Date format = new SimpleDateFormat("yyyy-MM-dd").parse(data1);
		cal.setTime(format);
		int day=cal.get(Calendar.DATE);
		//  此处为获得前一天的数据
		// 此处修改为+1则是获取后一天
		cal.set(Calendar.DATE,day-1);
		String lastDay = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
		cal.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(lastDay));
		int da = 0;
		if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
			da = -6;
		} else {
			da = 2 - cal.get(Calendar.DAY_OF_WEEK);
		}
		cal.add(Calendar.DAY_OF_WEEK, da);
		// 所在周开始日期
		String data11 = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
		cal.add(Calendar.DAY_OF_WEEK, 6);
		// 所在周结束日期
		String data2 = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
		return data11 + "-" + data2;
	}

	/**
	 * @see DateUtil#timestamp2Date(long, String)
	 * @param timestamp
	 * @return
	 */
	public static String timestamp2Date(long timestamp) {
		return timestamp2Date(timestamp, DATE_A);
	}

	/**
	 * long 13位时间戳转换为 指定格式的字符串日期
	 * @param timestamp 13位时间戳
	 * @param dateFormat 日期格式
	 * @return 指定格式的日期
	 */
	public static String timestamp2Date(long timestamp, String dateFormat) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
		return simpleDateFormat.format(new Date(timestamp));
	}

	/**
	 * 时间加/减 N天
	 * @param dateTime
	 * @param n
	 * @throws ParseException
	 */
	private final static SimpleDateFormat DF_DATE_B = new SimpleDateFormat(DateUtil.DATE_B);

	public static  long getDateTimestamp(String dateTime,int n) throws ParseException{
		Date date =DF_DATE_B.parse(dateTime);
		long time=date.getTime()/DateUtil.RATE_MS + n*DateUtil.RATE_D;
		return time;
	}

	/**
	 * 获取当天日期,格式未  yyyy-mm-dd
	 * @return 当天日期
	 */
	public static String getTypeAToday(){
		String today = DateTime.now().toString(DateUtil.DATE_A);
		return today;
	}

	/**
	 * 获取指定年周的第一天
	 * @param year
	 * @param week
	 * @return
	 */
	public static String getFirstDayOfWeek(int year, int week) {
		WeekFields weekFields = WeekFields.ISO;
		java.time.LocalDate startDate = java.time.LocalDate.of(year, 1, 1)
				.with(weekFields.weekOfYear(), week)
				.with(weekFields.dayOfWeek(), 1);
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
		return startDate.format(formatter);
	}

	/**
	 * 获取指定年周的最后一天
	 * @param year
	 * @param week
	 * @return
	 */
	public static String getLastDayOfWeek(int year, int week) {
		WeekFields weekFields = WeekFields.ISO;
		java.time.LocalDate startDate = java.time.LocalDate.of(year, 1, 1)
				.with(weekFields.weekOfYear(), week)
				.with(weekFields.dayOfWeek(), 1);
		java.time.LocalDate endDate = startDate.plusDays(6);
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
		return endDate.format(formatter);
	}

	/**
	 * 根据年月获取当前周的起始日期和结束日期
	 * @param tdate 年-月：2024-08
	 * @param dateJoin 连接符号
	 * @param prefix 对日期范围的前缀
	 * @param suffix 对日期范围的后缀
	 * @return 起始日期和结束日期
	 */
	public static String convertDateToWeek(String tdate, String dateJoin,String prefix,String suffix) {
		if (StringUtils.isBlank(tdate)) return null;
		String firstDayOfWeek = null;
		String lastDayOfWeek = null;
		int week;
		try {
			dateJoin = StringUtils.isBlank(dateJoin) ? "~" : dateJoin;
			prefix = prefix == null ? "" : prefix;
			suffix = suffix == null ? "" : suffix;
			int year = Integer.parseInt(tdate.substring(0, 4));
			week = Integer.parseInt(tdate.substring(5));
			firstDayOfWeek = getFirstDayOfWeek(year, week);
			lastDayOfWeek = getLastDayOfWeek(year, week);
		} catch (Exception e) {
			return null;
		}
		return "第" + week + "周" + prefix + firstDayOfWeek + dateJoin + lastDayOfWeek + suffix;
	}

	/**
	 * 获取当前 的小时 int值
	 * @return
	 */
	public static int getNowHour() {
		GregorianCalendar calendar = new GregorianCalendar();
		return calendar.get(Calendar.HOUR_OF_DAY);
	}


	private static final String HYPHEN = "-";

	/**
	 * yyyyMMdd to yyyy-MM-dd
	 * */
	public static String changeDateStringToHyphen(String origin) {
		if (!StringUtils.isNumeric(origin) || origin.length() != 8) {
			return origin;
		}
		StringBuilder sb = new StringBuilder(origin.substring(0, 4))
				.append(HYPHEN)
				.append(origin, 4, 6)
				.append(HYPHEN)
				.append(origin, 6, 8);
		return sb.toString();
	}

	/**
	 * 如果是 YYYY-MM-dd 直接返回， 如果是 YYYY/MM/dd 则 转化
	 * @param date 待转换日期
	 * @return YYYY-MM-dd
	 */
	public static String transferAndGetTypeADate(String date) throws UnSupportDateFormatException{
		if (BlankUtils.isBlank(date)) {
			throw new UnSupportDateFormatException("日期不能为空");
		}

		if (date.length() == 10 && date.charAt(4) == '-' && date.charAt(7) == '-') {
			return date;
		}

		if (date.contains("/")) {
			try {
				java.time.format.DateTimeFormatter source = java.time.format.DateTimeFormatter.ofPattern(DATE_G);
				java.time.format.DateTimeFormatter target = java.time.format.DateTimeFormatter.ofPattern(DATE_A);
				TemporalAccessor parse = source.parse(date);
				return target.format(parse);
			} catch (Exception e) {
				throw new UnSupportDateFormatException(date + " 日期格式错误",e);
			}
		}

		throw new UnSupportDateFormatException(date + " 日期不合规");
	}

	/**
	 * type T 2 type A
	 * yyyyMMdd 2 yyyy-MM-dd
	 * @param date
	 * @return
	 */
	public static String typeT2TypeA(String date) {
		java.time.format.DateTimeFormatter dateT = java.time.format.DateTimeFormatter.ofPattern(DATE_T);
		LocalDateTime parse = LocalDateTime.parse(date, dateT);
		java.time.format.DateTimeFormatter dateA = java.time.format.DateTimeFormatter.ofPattern(DATE_A);
		return parse.format(dateA);
	}

	/**
	 * type A 2 type T
	 *  yyyy-MM-dd 2 yyyyMMdd
	 * @param date
	 * @return
	 */
	public static String typeA2TypeT(String date) {
		java.time.format.DateTimeFormatter d = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
		TemporalAccessor t = d.parse(date);
		return java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd").format(t);
	}

	public static class UnSupportDateFormatException extends RuntimeException {
		public UnSupportDateFormatException(String message) {
			super(message);
		}

		public UnSupportDateFormatException(String message, Throwable cause) {
			super(message, cause);
		}
	}


	/**
	 * 获取开始日期到截止日期之间存在几个月，并且会返回每个月包含多少天
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static Map<String, Integer> getMonthDayList(String startDate, String endDate) {
		HashMap<String, Integer> map = new HashMap<>();
		List<Date> dateList = null;
		try {
			dateList = getDateList(startDate, endDate);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");

		for (Date date : dateList) {
			String format = simpleDateFormat.format(date);
			map.putIfAbsent(format, 0);
			map.put(format, map.get(format) + 1);
		}
		return map;
	}

	/**
	 * 获取开始日期到截止日期之间存在几个周，并且会返回每个周包含多少天
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static Map<String, Integer> getWeekDayList(String startDate, String endDate) {
		HashMap<String, Integer> map = new HashMap<>();
		List<Date> dateList = null;
		try {
			dateList = getDateList(startDate, endDate);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-ww");

		for (Date date : dateList) {
			String format = date.toInstant()
					.atZone(ZoneId.systemDefault())
					.toLocalDate()
					.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
					.format(formatter);
			map.putIfAbsent(format, 0);
			map.put(format, map.get(format) + 1);
		}
		return map;
	}

	/**
	 * 获取开始日期到截止日期之间存在几个年份，并且会返回每个年份包含多少天
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static Map<String, Integer> getYearDayList(String startDate, String endDate) {
		HashMap<String, Integer> map = new HashMap<>();
		List<Date> dateList = null;
		try {
			dateList = getDateList(startDate, endDate);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy");

		for (Date date : dateList) {
			String format = date.toInstant()
					.atZone(ZoneId.systemDefault())
					.toLocalDate().format(formatter);
			map.putIfAbsent(format, 0);
			map.put(format, map.get(format) + 1);
		}
		return map;
	}

	/**
	 * 获取开始日期到截止日期之间存在几个季度，并且会返回每个季度包含多少天
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static Map<String, Integer> getSeasonDayList(String startDate, String endDate) {
		HashMap<String, Integer> map = new HashMap<>();
		List<Date> dateList = null;
		try {
			dateList = getDateList(startDate, endDate);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy");

		for (Date date : dateList) {
			java.time.LocalDate localDate = date.toInstant()
					.atZone(ZoneId.systemDefault())
					.toLocalDate();
			String format = localDate.format(formatter);
			String key = format + "-Q" + ((localDate.getMonthValue() - 1) / 3 +1);
			map.putIfAbsent(key, 0);
			map.put(key, map.get(key) + 1);
		}
		return map;
	}


	public static void main(String[] args) {
		Map<String, Integer> weekDayList = getWeekDayList("2024-06-25", "2024-07-02");
		System.out.println(weekDayList);
	}

	/**
	 * 获取日期列表 默认格式 yyyy-MM-dd
	 * @param startDate
	 * @param endDate
	 * @return
	 * @throws ParseException
	 */
	public static List<Date> getDateList(String startDate, String endDate) throws ParseException {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_A);
		if (startDate.equals(endDate)) {
			return Lists.newArrayList(simpleDateFormat.parse(startDate));
		}

		Date start = simpleDateFormat.parse(startDate);
		Date end = simpleDateFormat.parse(endDate);

		Calendar startCal = Calendar.getInstance();
		startCal.setTime(start);

		Calendar endCal = Calendar.getInstance();
		endCal.setTime(end);

		List<Date> list = new ArrayList<>();
		while (endCal.compareTo(startCal) >= 0) {
			list.add(startCal.getTime());
			startCal.add(Calendar.DATE, 1);
		}
		return list;
	}

	/**
	 * 获取当月的所有日期
	 * @return days    日期列表  yyyy-MM-dd格式
	 */
	public static List<String> getDaysToMonth() {
		// 返回的日期集合
		List<String> days = new ArrayList<String>();

		DateFormat dateFormat = new SimpleDateFormat(DATE_A);
		try {
			List<String> tday = getThisMonthFirstAndEnd();
			Date start = dateFormat.parse(tday.get(0));
			Date end = dateFormat.parse(tday.get(1));

			Calendar tempStart = Calendar.getInstance();
			tempStart.setTime(start);

			Calendar tempEnd = Calendar.getInstance();
			tempEnd.setTime(end);
			tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
			while (tempStart.before(tempEnd)) {
				days.add(dateFormat.format(tempStart.getTime()));
				tempStart.add(Calendar.DAY_OF_YEAR, 1);
			}

		} catch (ParseException e) {
			e.printStackTrace();
		}

		return days;
	}

	/**
	 * 获取当月的第一天和最后一天
	 * @return
	 */
	public static List<String> getThisMonthFirstAndEnd() {
		LocalDateTime date = LocalDateTime.now();
		LocalDateTime firstday = date.with(TemporalAdjusters.firstDayOfMonth());
		LocalDateTime lastday = date.with(TemporalAdjusters.lastDayOfMonth());
		String firstDay = firstday.format(java.time.format.DateTimeFormatter.ofPattern(DATE_A));
		String lastDay = lastday.format(java.time.format.DateTimeFormatter.ofPattern(DATE_A));
		return Lists.newArrayList(firstDay, lastDay);
	}

	/**
	 * 根据时间和格式化模板对时间进行格式化操作
	 *
	 * @param date   Date类型时间数据
	 * @param format 格式化模板 eg:yyyy-MM-dd HH:mm:ss
	 * @return 格式化后的时间 eg: 2024-10-10 10:10:10
	 */
	public static String dateToStr(Date date, String format) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
		return simpleDateFormat.format(date);
	}

	public static String convertDateToWeek(String tdate) {
		String convertDate = tdate;
		String[] split = tdate.split("-");
		if (split.length >= 2) {
			int year = Integer.parseInt(split[0]);
			int week = Integer.parseInt(split[1]);
			String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
			String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
			convertDate = year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek;
		}
		return convertDate;
	}


	public static String convertDateToBeek(String tdate) {
		String convertDate = tdate;
		String[] split = tdate.split("-");
		if (split.length >= 3) {
			int year = Integer.parseInt(split[0]);
			int week1 = Integer.parseInt(split[1]);
			int week2 = Integer.parseInt(split[2]);
			String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
			String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
			convertDate = year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek;
		}
		return convertDate;
	}


}