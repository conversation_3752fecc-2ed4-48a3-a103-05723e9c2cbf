package com.wbgame.task;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.wbgame.pojo.wx.WxAppInfo;
import com.wbgame.service.LeyuanService;
import com.wbgame.utils.*;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.data.redis.core.script.DigestUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.slave.CustomMapper;
import com.wbgame.mapper.slave2.AdMsgMapper;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.SuperMathVo;
import com.wbgame.pojo.custom.AppChannelConfigVo;
import com.wbgame.pojo.custom.AppchannelAppidVo;
import com.wbgame.pojo.custom.CustomTotalStatsVo;
import com.wbgame.service.AdService;
import com.wbgame.service.CustomService;
import com.wbgame.service.SuperMathService;

/**
 * 游戏数据统计相关
 * <AUTHOR>
 */
@Component
public class GameTask {
	static	Logger logger = LoggerFactory.getLogger(GameTask.class);
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SuperMathService superMathService;
    @Autowired
    private CustomService customService;
    @Autowired
    private AdService adService;
    @Autowired
    AdMapper adMapper;
    @Autowired
    CustomMapper customMapper;
    @Autowired
    AdMsgMapper admsgMapper;
    @Autowired
    private LeyuanService leyuanService;


    /**
     * 233乐园数据抓取
     */
    @Scheduled(cron = "00 00 08,12,20 * * ?")
    public void syncLeyuan233Data() {
        try {
            String start = DateTime.now().minusDays(2).toString("yyyy-MM-dd");
            String end = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            leyuanService.synLeyuanData(start,end);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    //	@Scheduled(cron="10 */5 * * * ?")
    public void syncSendCcMail() {
        ListOperations<String, Object> opsForList = redisTemplate.opsForList();

        Long size = opsForList.size("send_ccmail_info_list");
        if (size >= 20) {
            size = 20L;
        }
        for (int i = 0; i < size; i++) {
            JSONObject obj = (JSONObject) opsForList.rightPop("send_ccmail_info_list");
            try {
                MailTool.sendCcMail(obj.getString("to"), obj.getString("cc"),
                        obj.getString("title"),
                        obj.getString("content") + "\n" + DateTime.now().toString("yyyy-MM-dd HH:mm:ss:SSS"), null);
            } catch (Exception e) {
                e.printStackTrace();
                // 异常失败后将邮件重新加入队列
                if (opsForList.size("send_ccmail_info_list") > 10000) {
                    redisTemplate.delete("send_ccmail_info_list");
                }
                opsForList.leftPush("send_ccmail_info_list", obj);
            }
        }
        logger.info("SendCcMail end...");
    }

    @Scheduled(cron = "10 */5 * * * ?")
    public void syncSuperMathList() {
        logger.info("superMath开始同步 --- ");
        long start = System.currentTimeMillis();
        List<SuperMathVo> dataList = new ArrayList<SuperMathVo>();
        ListOperations<String, Object> opsForList = redisTemplate.opsForList();

        int size = opsForList.size(CommonUtil.REDIS_LIST_SUPER_USERINFOLOG).intValue();
        logger.info("superMath有数据 " + size + "条");
        for (int i = 0; i < size; i++) {
            SuperMathVo user = (SuperMathVo) opsForList.rightPop(CommonUtil.REDIS_LIST_SUPER_USERINFOLOG);
            if (user != null && !BlankUtils.checkBlank(user.getUser_id())
            		&& !user.getUser_name().contains("\\x") && !user.getUser_name().contains("9F")
            		&& !user.getUser_name().contains("x9")){

            	user.setUser_name(filterEmoji(user.getUser_name()));
                dataList.add(user);
            }
        }

        if (dataList != null && dataList.size() > 0) { // 包含数据
            try {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("list", dataList);
                superMathService.insertSuperMathVoByAttr(map);

                long end = System.currentTimeMillis();
                logger.info("superMath消耗  " + (end - start) / 1000.00 + " 秒");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 过滤Emoji表情
     * @param str
     * @return
     */
    public static String filterEmoji(String str) {

        if(str == null || str.trim().isEmpty()){
            return str;
        }
        String pattern = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";
        String reStr = "";
        Pattern emoji = Pattern.compile(pattern);
        Matcher emojiMatcher = emoji.matcher(str);
        str = emojiMatcher.replaceAll(reStr);
        return str;
    }



//    @Scheduled(cron = "00 */30 * * * ?")
    public void syncSelectCustom() {
        logger.info("SelectCustom开始同步 --- ");
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("start_date", DateTime.now().toString("yyyyMMdd"));
        paramMap.put("end_date", DateTime.now().toString("yyyyMMdd"));
        try {
            customService.syncSelectCustomStatsInfo(paramMap);
        } catch (Exception e) {
            e.printStackTrace();
        }

        logger.info("SelectCustom完成同步 --- ");
    }

//    @Scheduled(cron = "00 21 01 * * ?")
    public void syncSelectCustom2() {

        logger.info("微信customStats 开始同步 --- ");
        long start1 = System.currentTimeMillis();
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("start_date", DateTime.now().minusDays(1).toString("yyyyMMdd"));
        paramMap.put("end_date", DateTime.now().minusDays(1).toString("yyyyMMdd"));
        try {
            customService.syncSelectCustomStatsInfo(paramMap);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("微信customStats 异常 --- ");
        }
        long end1 = System.currentTimeMillis();
        logger.info("微信customStats 消耗  " + (end1 - start1) / 1000.00 + " 秒");
    }


//    @Scheduled(cron = "00 41 01 * * ?")
    public void syncCustomStatsKeep() {
        String day = DateTime.now().minusDays(1).toString("yyyyMMdd");

        long start = System.currentTimeMillis();
        logger.info("customKeepUser 统计开始同步 --- ");

        try {
            customService.insertKeepUserOne(day);
            customService.syncInsertKeepUserList(day);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("留存统计异常 --- ");
        }

        long end2 = System.currentTimeMillis();
        logger.info("customKeepUser 留存消耗  " + (end2 - start) / 1000.00 + " 秒");

        try {
            customService.syncInsertAddUserList(day);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("新增统计异常 --- ");
        }

        long end3 = System.currentTimeMillis();
        logger.info("customKeepUser 新增消耗  " + (end3 - end2) / 1000.00 + " 秒");
    }


//    @Scheduled(cron = "10 00 */1 * * ?")
    public void syncCustomStatsKeepToday() {
        String today = DateTime.now().toString("yyyyMMdd");
        try {
            customService.insertKeepUserOne(today);
            customService.syncInsertAddUserList(today);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("实时新增统计异常 --- ");
        }

        String today2 = DateTime.now().toString("yyyy-MM-dd");
        try {
            customService.syncCustomStatsTotal(today2);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("实时微信customTotalStats 异常 --- ");
        }

    }


//    @Scheduled(cron = "00 00 04 * * ?")
    public void syncCustomTotal() {
        long start = System.currentTimeMillis();
        logger.info("微信customTotalStats 开始同步 --- ");
        String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        try {
            customService.syncCustomStatsTotal(yesterday);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("微信customTotalStats 异常 --- ");
        }
		
		/*String yesterday2 = DateTime.now().minusDays(1).toString("yyyyMMdd");
		try {
			customService.syncCustomStatsTotalTwo(yesterday2);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("微信customTotalStatsTwo 异常 --- ");
		}*/
        long end = System.currentTimeMillis();
        logger.info("微信customTotalStats 消耗  " + (end - start) / 1000.00 + " 秒");

    }


    /**
     * 小游戏实时数据报表
     */
//    @Scheduled(cron = "00 */20 * * * ?")
    public void syncAppTotalHour() {
        logger.info("小游戏实时数据报表  开始同步 --- ");

        DateTime dateTime = DateTime.now();
        String day = dateTime.toString("yyyy-MM-dd");
        String hournum = "hour" + dateTime.toString("HH");
        List<NpPostVo> list = new ArrayList<NpPostVo>();
        List<String> providers = Arrays.asList("qq", "wechat", "oppo", "vivo", "tt");

        List<CustomTotalStatsVo> ctsList = customMapper.selectCustomTotalStatsLog(day);
        for (CustomTotalStatsVo cut : ctsList) {
            if (providers.contains(cut.getPid())) {
                NpPostVo np = new NpPostVo();
                np.setTdate(cut.getTdate());
                np.setParam1("1");
                np.setParam2(cut.getAppid());
                np.setParam3(cut.getPid());
                np.setParam4(cut.getAdd_num());

                NpPostVo np2 = new NpPostVo();
                np2.setTdate(cut.getTdate());
                np2.setParam1("2");
                np2.setParam2(cut.getAppid());
                np2.setParam3(cut.getPid());
                np2.setParam4(cut.getOpen_user_num());

                NpPostVo np3 = new NpPostVo();
                np3.setTdate(cut.getTdate());
                np3.setParam1("3");
                np3.setParam2(cut.getAppid());
                np3.setParam3(cut.getPid());
                np3.setParam4(cut.getOpen_num());

                list.add(np);
                list.add(np2);
                list.add(np3);
            }
        }

        if(list != null && !list.isEmpty()){
	        Map<String, Object> paramMap = new HashMap<String, Object>();
	        paramMap.put("hournum", hournum);
	        paramMap.put("list", list);
	        customMapper.insertAppTotalHour(paramMap);
        }
    }

    /**
     * apk新增留存抽取
     */
//    @Scheduled(cron = "00 00 01 * * ?")
    public void syncApkStatsKeep() {
        String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

        long start = System.currentTimeMillis();
        logger.info("apkStatsKeep 统计开始同步 --- ");

        try {
            adService.syncInsertAppKeepUserList(day);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("留存统计异常 --- ");
        }

        long end = System.currentTimeMillis();
        logger.info("apkStatsKeep 新增留存消耗  " + (end - start) / 1000.00 + " 秒");
    }



//    @Scheduled(cron = "00 30 09,11,16 * * ?")
    public void syncWechatData() {

        String sql = "select tttoken,channel,account from app_channel_config where channel = 'wx' ORDER BY createtime asc";
        List<Map<String,Object>> list = adService.queryListMap(sql);

        String channel_appid_sql = "select appid,ttappid from  wx_channel_manage where cid='wechat' and ttappid!='' ";
        Map<String, Map<String, Object>> wxChannelManageMap = adService.queryListMapOfKey(channel_appid_sql);

        for (Map<String,Object> tttokenMap : list) {

            AppChannelConfigVo conf = new AppChannelConfigVo();
            conf.setTttoken(tttokenMap.get("tttoken").toString());
            conf.setAccount(tttokenMap.get("account").toString());
            conf.setChannel(tttokenMap.get("channel").toString());
            Map<String, Object> listMap = GameTask.getWxActAdd(null, conf,wxChannelManageMap);

            List<AppchannelAppidVo> sinkList = (List<AppchannelAppidVo>) listMap.get("sink");
            try {
                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("sql1", "insert into toutiao_app_info(tdate,channel,ttappid,cname,actnum,addnum) values ");
                paramMap.put("sql2", " (#{li.tdate},'wechat',#{li.appid_key},#{li.appid},#{li.act_num},#{li.add_num}) ");
                paramMap.put("sql3", " ON DUPLICATE KEY UPDATE actnum=VALUES(actnum),addnum=VALUES(addnum)");
                paramMap.put("list", sinkList);
                adService.batchExecSql(paramMap);
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {

                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("sql1", "insert into app_channel_appid(tdate,channel,appid,add_num,act_num,income,appid_key,times,two_rate,ban_income,screen_income,traffic_income,video_income,ban_pv,screen_pv,traffic_pv,video_pv,open_income,open_pv) values ");
                paramMap.put("sql2", " (#{li.tdate},'微信',#{li.appid},#{li.add_num},#{li.act_num},#{li.income},#{li.appid_key},#{li.times},#{li.two_rate},#{li.ban_income},#{li.screen_income},#{li.traffic_income},#{li.video_income},#{li.ban_pv},#{li.screen_pv},#{li.traffic_pv},#{li.video_pv},#{li.open_income},#{li.open_pv}) ");
                paramMap.put("sql3", " ON DUPLICATE KEY UPDATE act_num=VALUES(act_num),add_num=VALUES(add_num),times=VALUES(times)");
                paramMap.put("list", sinkList);
                adMapper.batchExecSql(paramMap);

                List<AppchannelAppidVo> keepList = (List<AppchannelAppidVo>) listMap.get("keep");
                adMapper.updateAppChannelForKeepList(keepList);
                adMapper.updateAppChannelForIncomeList(sinkList);

                logger.info("微信活跃新增数据抽取 --- ");
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("微信活跃新增数据抽取失败 --- ");
            }
        }
    }

    /**
     * 经过沟通可废弃 -20231117
     */
//    @Scheduled(cron = "00 30 09,11,14 * * ?")
    public void syncVivoData() {
        String sql = "select tttoken from app_channel_config where channel = 'vivo' ORDER BY createtime asc";
        List<String> list = adService.queryListString(sql);
        String[] array = list.toArray(new String[list.size()]);

        Map<String, Object> listMap = GameTask.getVivoActAdd(null, array);
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("sql1", "insert into toutiao_app_info(tdate,channel,ttappid,cname,actnum,addnum) values ");
            paramMap.put("sql2", " (#{li.tdate},'vivo',#{li.appid_key},#{li.appid},#{li.act_num},#{li.add_num})");
            paramMap.put("sql3", " ON DUPLICATE KEY UPDATE actnum=VALUES(actnum),addnum=VALUES(addnum)");
            paramMap.put("list", listMap.get("sink"));
            adMapper.batchExecSql(paramMap);

            logger.info("vivo活跃新增数据抽取 --- ");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("vivo活跃新增数据抽取失败 --- ");
        }


        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("sql1", "insert into app_channel_appid(tdate,channel,appid,add_num,act_num,appid_key) values ");
            paramMap.put("sql2", " (#{li.tdate},'vivo',#{li.appid},#{li.add_num},#{li.act_num},#{li.appid_key})");
            paramMap.put("sql3", " ON DUPLICATE KEY UPDATE act_num=VALUES(act_num),add_num=VALUES(add_num)");
            paramMap.put("list", listMap.get("sink"));
            adMapper.batchExecSql(paramMap);

            List<AppchannelAppidVo> keepList = (List<AppchannelAppidVo>) listMap.get("keep");
            adMapper.updateAppChannelForKeepList(keepList);
            List<AppchannelAppidVo> incomeList = (List<AppchannelAppidVo>) listMap.get("income");
            adMapper.updateAppChannelForIncomeList(incomeList);

            logger.info("vivoPV收入数据抽取 --- ");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("vivoPV收入数据抽取失败 --- ");
        }
    }

    /**
     * 头条平台小游戏数据抓取
     */
    @Scheduled(cron = "00 30 09,11,14 * * ?")
    public void syncHeadLineData() {
        String sql = "select tttoken from app_channel_config where channel = 'tt' ORDER BY createtime asc";
        List<String> list = adService.queryListString(sql);
        String[] array = list.toArray(new String[list.size()]);

        Map<String, Object> listMap = GameTask.getToutiaoMicGameData(null,null, array);
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("sql1", "insert into toutiao_app_info(tdate,channel,ttappid,cname,actnum,addnum) values ");
            paramMap.put("sql2", " (#{li.tdate},'字节',#{li.appid_key},#{li.appid},#{li.act_num},#{li.add_num})");
            paramMap.put("sql3", " ON DUPLICATE KEY UPDATE actnum=VALUES(actnum),addnum=VALUES(addnum)");
            paramMap.put("list", listMap.get("sink"));
            List<AppchannelAppidVo> sinkList = (List<AppchannelAppidVo>) listMap.get("sink");
            if (sinkList.size()>0){
                adMapper.batchExecSql(paramMap);
            }
            logger.info("头条活跃新增数据抽取 --- ");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("头条活跃新增数据抽取失败 --- ");
        }


        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("sql1", "insert into app_channel_appid(tdate,channel,appid,add_num,act_num,appid_key) values ");
            paramMap.put("sql2", " (#{li.tdate},'字节',#{li.appid},#{li.add_num},#{li.act_num},#{li.appid_key})");
            paramMap.put("sql3", " ON DUPLICATE KEY UPDATE act_num=VALUES(act_num),add_num=VALUES(add_num)");
            paramMap.put("list", listMap.get("sink"));
            List<AppchannelAppidVo> incomeList = (List<AppchannelAppidVo>) listMap.get("sink");
            if (incomeList.size()>0){
                adMapper.batchExecSql(paramMap);
                adMapper.updateAppChannelForIncomeList(incomeList);
            }

            List<AppchannelAppidVo> keepList = (List<AppchannelAppidVo>) listMap.get("keep");
            if (keepList.size()>0){
                adMapper.updateAppChannelForKeepList(keepList);
            }
            logger.info("头条PV收入数据抽取 --- ");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("头条PV收入数据抽取失败 --- ");
        }
    }


    /**
     * 通过四个账号凭证获取数据
     */
    public static Map<String, Object> getToutiaoActAddTwo(String tdate, String... cooks) {

        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();

        DateTime dateTime = DateTime.now().minusDays(1);
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate).withHourOfDay(11);
        String today = dateTime.toString("yyyy-MM-dd");
        String before = dateTime.minusDays(1).toString("yyyy-MM-dd");
        String[] cookies = cooks;
        for (String cook : cookies) {

            Map<String, String> headMap = new HashMap<>();
            headMap.put(":authority", "developer.toutiao.com");
            headMap.put(":path", "/api/v1/app/list");
            headMap.put(":method", "POST");
            headMap.put(":scheme", "https");
            headMap.put("accept", "application/json");
            headMap.put("accept-encoding", "gzip, deflate, br");
            headMap.put("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,und;q=0.6");
            headMap.put("cache-control", "no-cache");
            headMap.put("content-type", "application/json");
            headMap.put("cookie", cook);
            headMap.put("origin", "https://developer.toutiao.com");
            headMap.put("pragma", "no-cache");
            headMap.put("referer", "https://developer.toutiao.com/app/applist");
            headMap.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");

            // 获取应用列表，方便抽取所有应用信息
            String list = "https://developer.toutiao.com/api/v1/app/list";
            String json = HttpClientUtils.getInstance().httpGetWithBrReturn(list, headMap);
            DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
            JSONObject apps = JSONObject.parseObject(json);
            if (apps != null && "0".equals(apps.getString("error"))) {
                JSONArray array = apps.getJSONObject("data").getJSONArray("creator_list");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject ob = array.getJSONObject(i);
                    // 版本为未发布的不进行统计
                    if (BlankUtils.checkBlank(ob.getString("version")))
                        continue;

                    String appid = ob.getString("appid");
                    String name = ob.getString("name");

                    AppchannelAppidVo app = new AppchannelAppidVo();
                    AppchannelAppidVo keep = new AppchannelAppidVo();
                    app.setTdate(today);
                    app.setAppid(name);
                    app.setAppid_key(appid);
                    app.setChannel("字节");

                    BeanUtils.copyProperties(app, keep);
                    keep.setTdate(before);

                    // 根据头条appid抽取对应的活跃、新增、时长
                    headMap.put(":path", "/api/v1/app/" + appid + "/event_analysis");
                    headMap.put("referer", "https://microapp.bytedance.com/app/overview/" + appid);

                    DateTime t = dateTime.plusDays(6);

                    Map<String, String> headMaps = new HashMap<>();
                    Map<String,String> addAndActHeadMaps = new HashMap<>();
                    addAndActHeadMaps.put("cookie", cook);
                    headMaps.put("content-type", "application/json");
                    headMaps.put("cookie", cook);

                    headMaps.put(":path", "/api/v1/app/" + appid + "/event_analysis");
                    headMaps.put("referer", "https://microapp.bytedance.com/app/overview/" + appid);

                    try {

                        // 获取小程序的收入和PV，类型：banner、插屏、视频		URLEncoder在get请求url时需要把"+"转为"%20"
                        //banner
                        String getparam1 = ("?hostId=0&page=1&size=20&startDate=" + URLEncoder.encode(today + "") + "&endDate=" + URLEncoder.encode(today + "") + "&reqType=1&ritId=" + URLEncoder.encode("80037,40043,20037,30037,28043"));
                        //插屏
                        String getparam2 = ("?hostId=0&page=1&size=20&startDate=" + URLEncoder.encode(today + "") + "&endDate=" + URLEncoder.encode(today + "") + "&reqType=1&ritId=" + URLEncoder.encode("80043,40044,20043,28044"));
                        ////激励视频
                        String getparam3 = ("?hostId=0&page=1&size=20&startDate=" + URLEncoder.encode(today + "") + "&endDate=" + URLEncoder.encode(today + "") + "&reqType=1&ritId=" + URLEncoder.encode("80038,16038,40042,20038,30038,28042"));

                        headMap.put(":authority", "microapp.bytedance.com");
                        headMap.put(":method", "GET");
                        headMap.put("referer", "https://microapp.bytedance.com/app/"+appid+"/traffic");

                        String pvlink1 = "https://microapp.bytedance.com/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data" + getparam1;
                        headMap.put(":path", "/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data" + getparam1);
                        String result1 = HttpClientUtils.getInstance().httpGetWithBrReturn(pvlink1, headMap);
                        JSONObject pvobj1 = JSONObject.parseObject(result1);
                        app.setBan_pv("0");
                        app.setBan_income("0");
                        if (pvobj1 != null
                                && "0".equals(pvobj1.getString("error"))) {
                            JSONArray jsonArr = pvobj1.getJSONObject("data").getJSONArray("adData");
                            if (jsonArr.size() > 0) {
                                app.setBan_pv(jsonArr.getJSONObject(0).getString("show"));
                                app.setBan_income(jsonArr.getJSONObject(0).getString("realCost"));
                            } else {
                                app.setBan_pv("0");
                                app.setBan_income("0");
                            }
                        }

                        String pvlink2 = "https://microapp.bytedance.com/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data" + getparam2;
                        headMap.put(":path", "/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data"  + getparam2);
                        String result2 = HttpClientUtils.getInstance().httpGetWithBrReturn(pvlink2, headMap);
                        JSONObject pvobj2 = JSONObject.parseObject(result2);
                        app.setScreen_pv("0");
                        app.setScreen_income("0");
                        if (pvobj2 != null
                                && "0".equals(pvobj2.getString("error"))) {
                            JSONArray jsonArr = pvobj2.getJSONObject("data").getJSONArray("adData");
                            if (jsonArr.size() > 0) {
                                app.setScreen_pv(jsonArr.getJSONObject(0).getString("show"));
                                app.setScreen_income(jsonArr.getJSONObject(0).getString("realCost"));
                            } else {
                                app.setScreen_pv("0");
                                app.setScreen_income("0");
                            }
                        }

                        String pvlink3 = "https://microapp.bytedance.com/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data" + getparam3;
                        headMap.put(":path", "/platform_api/game/traffic/v1/" + appid + "/settlement/traffic/ad_data" + getparam3);
                        String result3 = HttpClientUtils.getInstance().httpGetWithBrReturn(pvlink3, headMap);
                        JSONObject pvobj3 = JSONObject.parseObject(result3);
                        app.setVideo_pv("0");
                        app.setVideo_income("0");
                        if (pvobj3 != null
                                && "0".equals(pvobj3.getString("error"))) {
                            JSONArray jsonArr = pvobj3.getJSONObject("data").getJSONArray("adData");
                            if (jsonArr.size() > 0) {
                                app.setVideo_pv(jsonArr.getJSONObject(0).getString("show"));
                                app.setVideo_income(jsonArr.getJSONObject(0).getString("realCost"));
                            } else {
                                app.setVideo_pv("0");
                                app.setVideo_income("0");
                            }
                        }
                        app.setIncome(new BigDecimal(app.getBan_income())
                                .add(new BigDecimal(app.getScreen_income()))
                                .add(new BigDecimal(app.getVideo_income())).doubleValue() + "");

                        sinkList.add(app);
                    }catch (Exception e){
                        logger.error("fetch data error:",e);
                        continue;
                    }
                }
            } else {
                paramMap.put("code", -1);
                paramMap.put("message", json);
            }
        }
        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        if (paramMap.get("code") == null && 0 == sinkList.size()) {
            paramMap.put("code", -1);
            paramMap.put("message", "抽取的活跃、新增数据返回为空！");
        }
        return paramMap;
    }


    /**
     * 通过四个账号凭证获取数据
     */
    public static Map<String, Object> getToutiaoMicGameData(String startDate,String endDate, String... cooks) {

        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();
        DateTime now = DateTime.now();
        if (BlankUtils.checkBlank(startDate)){
            startDate = now.minusDays(11).toString("yyyy-MM-dd");
        }
        if (BlankUtils.checkBlank(endDate)){
            endDate = now.minusDays(1).toString("yyyy-MM-dd");
        }
        String[] cookies = cooks;
        for (String cookie : cookies) {
            String url = "https://microapp.bytedance.com/platform_api/v1/app/getAppList?type=2";
            Map<String,String> headerMap = new HashMap<>();
            //banner
            String banner = "20037,30037,30037,28043,80037,40043";
            //激励视频
            String video = "20038,30038,30038,28042,26038,3589830,80038,16038,40042";
            //插屏
            String plaque = "20043,28044,80043,40044";
            headerMap.put("cookie",cookie);
            String result = HttpRequest.get(url,headerMap);
            if (!BlankUtils.checkBlank(result)) {
                headerMap.put("cookie", cookie);
                List<String> dateList = DateUtil.getDays(startDate, endDate);
                Map<String, AppchannelAppidVo> dataMap = new LinkedHashMap<>();
                Map<String, AppchannelAppidVo> bannerDataMap = new HashMap<>();
                Map<String, AppchannelAppidVo> videoDataMap = new HashMap<>();
                Map<String, AppchannelAppidVo> plaqueDataMap = new HashMap<>();
                if (!BlankUtils.checkBlank(result)) {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    JSONObject dataJson = resultJson.getJSONObject("data");
                    JSONArray appArray = dataJson.getJSONArray("appList");
                    //先初始化数据
                    for (Object obj : appArray) {

                        JSONObject appInfoDataJson = (JSONObject) obj;
                        JSONObject appInfoJson = appInfoDataJson.getJSONObject("appInfo");
                        String appid = appInfoJson.getString("appId");
                        String appname = appInfoJson.getString("name");
                        String state = appInfoJson.getString("state");
                        //只有状态为1的才进行数据查询
                        if ("1".equals(state)) {
                            for (String date : dateList) {
                                AppchannelAppidVo app = new AppchannelAppidVo();
                                String dataKey = date + "_" + appid;
                                app.setAppid(appname);
                                app.setAppid_key(appid);
                                app.setTdate(date);
                                app.setChannel("字节");
                                app.setBan_pv("0");
                                app.setBan_income("0");
                                app.setScreen_pv("0");
                                app.setScreen_income("0");
                                app.setVideo_pv("0");
                                app.setVideo_income("0");
                                dataMap.put(dataKey, app);
                            }
                        }
                    }
                    for (Object obj : appArray) {
                        Integer page = 1;
                        Integer pageSize = 31;

                        JSONObject appInfoDataJson = (JSONObject) obj;
                        JSONObject appInfoJson = appInfoDataJson.getJSONObject("appInfo");
                        String appid = appInfoJson.getString("appId");
                        String state = appInfoJson.getString("state");
                        if ("1".equals(state)) {
                            String bannerIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + banner + "&hostId=0";
                            String incomeResult = HttpRequest.get(bannerIncomeUrl, headerMap);
                            if (!BlankUtils.checkBlank(incomeResult)) {
                                JSONObject incomeResultJson = JSONObject.parseObject(incomeResult);
                                JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                                JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                                for (Object incomeObj : incomeResultDataArray) {
                                    AppchannelAppidVo app = new AppchannelAppidVo();
                                    JSONObject incomeData = (JSONObject) incomeObj;
                                    String pv = incomeData.getString("show");
                                    String income = incomeData.getString("realCost");
                                    String date = incomeData.getString("date");
                                    app.setBan_pv(pv);
                                    app.setBan_income(income);
                                    String dataKey = date + "_" + appid;
                                    bannerDataMap.put(dataKey, app);
                                }
                            }

                            String videoIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + video + "&hostId=0";
                            String videoIncomeResult = HttpRequest.get(videoIncomeUrl, headerMap);
                            if (!BlankUtils.checkBlank(videoIncomeResult)) {
                                JSONObject incomeResultJson = JSONObject.parseObject(videoIncomeResult);
                                JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                                JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                                for (Object incomeObj : incomeResultDataArray) {
                                    AppchannelAppidVo app = new AppchannelAppidVo();
                                    JSONObject incomeData = (JSONObject) incomeObj;
                                    String pv = incomeData.getString("show");
                                    String income = incomeData.getString("realCost");
                                    String date = incomeData.getString("date");
                                    app.setVideo_pv(pv);
                                    app.setVideo_income(income);
                                    String dataKey = date + "_" + appid;
                                    videoDataMap.put(dataKey, app);
                                }
                            }

                            String plaqueIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + plaque + "&hostId=0";
                            String plaqueIncomeResult = HttpRequest.get(plaqueIncomeUrl, headerMap);
                            if (!BlankUtils.checkBlank(plaqueIncomeResult)) {
                                JSONObject incomeResultJson = JSONObject.parseObject(plaqueIncomeResult);
                                JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                                JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                                for (Object incomeObj : incomeResultDataArray) {
                                    AppchannelAppidVo app = new AppchannelAppidVo();
                                    JSONObject incomeData = (JSONObject) incomeObj;
                                    String pv = incomeData.getString("show");
                                    String income = incomeData.getString("realCost");
                                    String date = incomeData.getString("date");
                                    app.setScreen_pv(pv);
                                    app.setScreen_income(income);
                                    String dataKey = date + "_" + appid;
                                    plaqueDataMap.put(dataKey, app);
                                }
                            }
                        }
                    }
                    //遍历汇总数据
                    for (Map.Entry<String, AppchannelAppidVo> each : dataMap.entrySet()) {
                        AppchannelAppidVo data = each.getValue();
                        String key = each.getKey();
                        //banner收入
                        if (bannerDataMap.get(key) != null) {
                            AppchannelAppidVo bannerData = bannerDataMap.get(key);
                            data.setBan_income(bannerData.getBan_income());
                            data.setBan_pv(bannerData.getBan_pv());
                        }
                        //video收入
                        if (videoDataMap.get(key) != null) {
                            AppchannelAppidVo videoData = videoDataMap.get(key);
                            data.setVideo_income(videoData.getVideo_income());
                            data.setVideo_pv(videoData.getVideo_pv());
                        }
                        //plaque收入
                        if (plaqueDataMap.get(key) != null) {
                            AppchannelAppidVo plaqueData = plaqueDataMap.get(key);
                            data.setScreen_income(plaqueData.getScreen_income());
                            data.setScreen_pv(plaqueData.getScreen_pv());
                        }

                        BigDecimal incomeData = new BigDecimal("0");
                        if (!BlankUtils.checkBlank(data.getBan_income())) {
                            incomeData = incomeData.add(new BigDecimal(data.getBan_income()));
                        }
                        if (!BlankUtils.checkBlank(data.getScreen_income())) {
                            incomeData = incomeData.add(new BigDecimal(data.getScreen_income()));
                        }
                        if (!BlankUtils.checkBlank(data.getVideo_income())) {
                            incomeData = incomeData.add(new BigDecimal(data.getVideo_income()));
                        }
                        data.setIncome(incomeData.doubleValue() + "");
                        sinkList.add(data);
                    }
                }
            }
        }
        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        if (paramMap.get("code") == null && 0 == sinkList.size()) {
            paramMap.put("code", -1);
            paramMap.put("message", "抽取的收入数据返回为空！");
        }
        return paramMap;
    }


    /**
     * 通过oppo账号应用获取数据
     */
    public static Map<String, Object> getOppoActAdd(String tdate, AppChannelConfigVo conf) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> incomeList = new ArrayList<AppchannelAppidVo>();

        // 有传入日期参数，则使用指定的日期
        DateTime dateTime = DateTime.now().minusDays(1);
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate).withHourOfDay(11);

        String today = dateTime.toString("yyyy-MM-dd");
        String before = dateTime.minusDays(1).toString("yyyy-MM-dd");

        String[] cookies = {
                conf.getTttoken()
        };
        String oadstk = conf.getTtappid();

        for (String cook : cookies) {

            Map<String, String> headMap = new HashMap<>();
            headMap.put(":authority", "open.oppomobile.com");
            headMap.put(":method", "POST");
            headMap.put(":scheme", "https");
            headMap.put("accept", "*/*");
            headMap.put("accept-encoding", "gzip, deflate, br");
            headMap.put("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,und;q=0.6");
            headMap.put("cache-control", "no-cache");
            headMap.put("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
            headMap.put("cookie", cook);
            headMap.put("pragma", "no-cache");
            headMap.put("x-requested-with", "XMLHttpRequest");
            headMap.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");
            headMap.put("origin", "https://open.oppomobile.com");
            headMap.put("pragma", "no-cache");

            // 获取oppo小游戏的应用列表
            String link = "https://open.oppomobile.com/h5game/index/list.json";
            HashMap<String, String> pmap = new HashMap<String, String>();
            pmap.put("offset", "0");
            pmap.put("limit", "40");
            String json = HttpClientUtils.getInstance().httpPost(link, pmap, headMap);

            JSONObject jsonObject = JSONObject.parseObject(json);
            if (jsonObject != null && 0 == jsonObject.getIntValue("errno")) {

                JSONArray array = jsonObject.getJSONObject("data").getJSONArray("rows");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject dd = array.getJSONObject(i);
                    // 移除掉未上线应用 TODO（爬数据受页面的数据计算影响，导致会出现无法查看现象）
                    if (dd.getString("game_active_uv") == null || "--".equals(dd.getString("game_active_uv")))
                        continue;

                    String id = dd.getString("app_id");
                    String name = dd.getString("app_name");

                    AppchannelAppidVo app = new AppchannelAppidVo();
                    AppchannelAppidVo keep = new AppchannelAppidVo();
                    app.setTdate(today);
                    app.setAppid(name);
                    app.setAppid_key(id);
                    app.setChannel("oppo");

                    BeanUtils.copyProperties(app, keep);
                    keep.setTdate(before);

                    // 获取新增、活跃数据和 留存率数据
//					String url2 = "https://open.oppomobile.com/h5game/data/retain";
                    String url2 = "https://open.oppomobile.com/h5game/index/access-retain-list";
                    HashMap<String, String> params = new HashMap<String, String>();
                    params.put("instant_id", id);
                    params.put("start_time", before);
                    params.put("end_time", today);
                    params.put("offset", "0");
                    params.put("limit", "10");

                    String httpPost2 = HttpClientUtils.getInstance().httpPost(url2, params, headMap);
                    JSONObject object2 = JSONObject.parseObject(httpPost2);
                    if (object2 != null && 0 == object2.getIntValue("errno")) {
                        JSONArray jsonArray = object2.getJSONObject("data").getJSONArray("rows");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            JSONObject data = jsonArray.getJSONObject(0);
                            app.setAct_num(data.getIntValue("game_active_uv"));
                            app.setAdd_num(data.getIntValue("game_new_uv"));
                            app.setTimes(data.getString("avg_duration"));
//                            System.out.println("活跃：" + app.getAct_num() + "\t新增：" + app.getAdd_num());
                            sinkList.add(app);

                            if (jsonArray.size() == 2) {

                                double number = new BigDecimal(jsonArray.getJSONObject(1).getString("game_new_rtt_2"))
                                        .multiply(new BigDecimal("100")).doubleValue();
                                String keep1 = BlankUtils.getNumFormat(number, 2);
                                keep.setTwo_rate(keep1 + "%");
                            } else {
                                keep.setTwo_rate("0%");
                            }
                        } else {
                            keep.setTwo_rate("0%");
                        }
                        keepList.add(keep);
                    }
                }

                // 获取PV与收入数据
                Map<String, AppchannelAppidVo> inMap = new HashMap<>();
                //TODO（2021-09-28 更新 从api获取收入等数据）
                String httpPost3 = getOppoApiData(conf.getTtparam(),conf.getTtappid(),today.replace("-", ""));
                JSONObject reObject = JSONObject.parseObject(httpPost3);
                    if (reObject != null && 0 == reObject.getIntValue("code")) {

                        JSONArray jsonArray = reObject.getJSONArray("data");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            for (int k = 0; k < jsonArray.size(); k++) {
                                JSONObject data = jsonArray.getJSONObject(k);
                                String[] split = data.getString("posName").split("-");
                                if (split == null || split.length != 2)
                                    continue;
                                String posName = split[0];
                                String adtype = split[1];
                                String showTypeName = data.getString("showTypeName");
                                if (!posName.contains("H5"))
                                    continue;

                                AppchannelAppidVo in = inMap.get(today + posName);
                                if (in == null) {
                                    in = new AppchannelAppidVo();
                                    in.setTdate(today);
                                    in.setAppid(posName.replace("H5", ""));
                                    in.setChannel("oppo");
                                }

                                String pv = data.getString("view");
                                String income = data.getString("income");
                                if (showTypeName.contains("原生")) {
                                    // 数据已废弃
                                    if ((BlankUtils.checkBlank(income) || Double.valueOf(income) == 0)
                                            && (BlankUtils.checkBlank(pv) || Double.valueOf(pv) < 50)) {
                                        continue;
                                    }
                                }
                                if (showTypeName.contains("开屏")) {
                                    in.setOpen_pv(pv);
                                    in.setOpen_income(income);
                                } else if (showTypeName.contains("banner")
                                        || showTypeName.contains("Banner")
                                        || showTypeName.contains("BANNER")) {
                                    in.setBan_pv(pv);
                                    in.setBan_income(income);
                                } else if (showTypeName.contains("插屏")) {
                                    in.setScreen_pv(pv);
                                    in.setScreen_income(income);
                                } else if (showTypeName.contains("视频")) {
                                    in.setVideo_pv(pv);
                                    in.setVideo_income(income);
                                } else if (showTypeName.contains("原生") && !("原生msg").equals(adtype)) { // 默认为原生
                                    in.setTraffic_pv(pv);
                                    in.setTraffic_income(income);
                                } else if (showTypeName.contains("原生") && ("原生MSG").equals(adtype)) { // 原生msg
                                    in.setOrimsg_pv(pv);
                                    in.setOrimsg_income(income);
                                }
                                in.setIncome(new BigDecimal(in.getOpen_income() == null ? "0" : in.getOpen_income())
                                        .add(new BigDecimal(in.getBan_income() == null ? "0" : in.getBan_income()))
                                        .add(new BigDecimal(in.getScreen_income() == null ? "0" : in.getScreen_income()))
                                        .add(new BigDecimal(in.getTraffic_income() == null ? "0" : in.getTraffic_income()))
                                        .add(new BigDecimal(in.getVideo_income() == null ? "0" : in.getVideo_income()))
                                        .add(new BigDecimal(in.getOrimsg_income() == null ? "0" : in.getOrimsg_income()))
                                        .doubleValue() + "");
                                inMap.put(today + posName, in);
                            }
                        }
                    }

                incomeList.addAll(inMap.values());
            } else {
                paramMap.put("code", -1);
                paramMap.put("message", json);
            }
        }
        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        paramMap.put("income", incomeList);
        return paramMap;
    }

    /**
     * 通过vivo账号应用获取数据
     */
    public static Map<String, Object> getVivoActAdd(String tdate, String... cooks) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> incomeList = new ArrayList<AppchannelAppidVo>();

        // 有传入日期参数，则使用指定的日期
        DateTime dateTime = DateTime.now().minusDays(1);
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate).withHourOfDay(11);

        long ts = (DateTime.now().getMillis());
        String today = dateTime.toString("yyyy-MM-dd");
        String before = dateTime.minusDays(1).toString("yyyy-MM-dd");

        String[] cookies = cooks;

        for (String cook : cookies) {

            Map<String, String> headMap = new HashMap<>();
            headMap.put("cookie", cook);
            // 获取vivo小游戏的应用列表
            Integer pageIndex = 1;
            Integer pageSize = 1000;
            JSONArray appArray = new JSONArray();

            String link = "https://adnet.vivo.com.cn/api/media/search?order=&orderBy=&status=-1&appName=&platformType=2&pageIndex=" + pageIndex + "&pageSize="+pageSize+ "&timestamp=" + ts;
            String json = HttpClientUtils.getInstance().httpGet(link, headMap);
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (jsonObject != null && 1 == jsonObject.getIntValue("code")) {
                appArray = jsonObject.getJSONObject("data") != null ? jsonObject.getJSONObject("data").getJSONArray("medias") : new JSONArray();
            } else {
                continue;
            }
            if (appArray.size()>0){

                for (int i = 0; i < appArray.size(); i++) {
                    AppchannelAppidVo app = new AppchannelAppidVo();
                    AppchannelAppidVo keep = new AppchannelAppidVo();
                    JSONObject dd = appArray.getJSONObject(i);
                    // 移除掉未上线应用
                    if ("0".equals(dd.getString("status")))
                        continue;

                    String appId = dd.getString("appId");
                    String name = dd.getString("appName");

                    app.setTdate(today);
                    app.setAppid(name);
                    app.setAppid_key(appId);
                    app.setChannel("vivo");

                    BeanUtils.copyProperties(app, keep);
                    keep.setTdate(before);

                    // 获取新增、活跃数据
                    long time = (DateTime.now().getMillis());
                    try {
                        String addUrl= "https://dev.vivo.com.cn/webapi/data-service/index-card?cardId=24&type=5&" +
                                "dataId="+appId+"&startDate="+today+"&endDate="+today+"&timestamp="+time;
                        String addGet = HttpClientUtils.getInstance().httpGet(addUrl, headMap);
                        JSONObject object = JSONObject.parseObject(addGet);
                        if (object != null && 0 == object.getIntValue("code")) {
                            JSONArray jsonArray = object.getJSONObject("data").getJSONArray("dataList");
                            if (jsonArray != null && jsonArray.size() > 0) {
                                JSONObject data = jsonArray.getJSONObject(0);
                                try {
                                    int intVal1 = data.getIntValue("new_user");
                                    app.setAdd_num(intVal1);
                                } catch (Exception e) {
                                    app.setAdd_num(0);
                                }
                            }
                        }
                        String actUrl= "https://dev.vivo.com.cn/webapi/data-service/index-card?cardId=25&type=5&" +
                                "dataId="+appId+"&startDate="+today+"&endDate="+today+"&timestamp="+time;
                        String actGet = HttpClientUtils.getInstance().httpGet(actUrl, headMap);
                        JSONObject actObject = JSONObject.parseObject(actGet);
                        if (actObject != null && 0 == actObject.getIntValue("code")) {
                            JSONArray jsonArray = actObject.getJSONObject("data").getJSONArray("dataList");
                            if (jsonArray != null && jsonArray.size() > 0) {
                                JSONObject data = jsonArray.getJSONObject(0);
                                try {
                                    int intVal0 = data.getIntValue("day_active_user");
                                    app.setAct_num(intVal0);
                                } catch (Exception e) {
                                    app.setAct_num(0);
                                }
                            }
                        }
                        sinkList.add(app);
                    }catch (Exception e){
                        logger.error("vivo获取新增、活跃数据error:",e);
                    }




                    try {
                        // 获取留存率数据
                        String url2 = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=82&menuType=1&type=5&currentPageNum=1&numPerPage=10" +
                                "&dataId="+appId+"&startDate="+before+"&endDate="+before+"&timestamp="+time;
                        String httpGet2 = HttpClientUtils.getInstance().httpGet(url2, headMap);
                        JSONObject object2 = JSONObject.parseObject(httpGet2);
                        if (object2 != null && 0 == object2.getIntValue("code")) {
                            JSONObject keepObject = object2.getJSONObject("data").getJSONObject("pageData");
                            JSONArray jsonArray = keepObject.getJSONArray("data");
                            if (jsonArray != null && jsonArray.size() > 0) {
                                JSONObject data = jsonArray.getJSONObject(0);
                                if(!BlankUtils.isNumeric(data.getString("next_day_left_rate"))){
                                    data.put("next_day_left_rate", "0.0000");
                                }
                                double number = new BigDecimal(data.getString("next_day_left_rate"))
                                        .multiply(new BigDecimal("100")).doubleValue();
                                String keep1 = BlankUtils.getNumFormat(number, 2);
                                keep.setTwo_rate(keep1 + "%");

                            } else {
                                keep.setTwo_rate("0%");
                            }
                        }
                        keepList.add(keep);
                    }catch (Exception e){
                        logger.error("vivo获取留存率数据error:",e);
                    }

                    try {
                        String mediaId = dd.getString("mediaId");
                        // 获取广告位展示和收入，开屏、banner、插屏、原生广告、激励视频
                        Map<String, AppchannelAppidVo> inMap = new HashMap<>();
                        //2:开屏 3:banner 4:插屏 5:原生广告 9:激励视频
                        String[] types = {"2", "3", "4", "5", "9"};
                        for (String p : types) {
                            String li = "https://adnet.vivo.com.cn/api/report/getReportChartData?metrics=&platformType=&flowType=1&dimensions=positionId" +
                                    "&mediaIds=" + mediaId + "&startDate=" + today + "&endDate=" + today + "&positionTypes=" + p + "&timestamp=" + time;
                            String result = HttpClientUtils.getInstance().httpGet(li, headMap);
                            JSONObject reObject = JSONObject.parseObject(result);
                            if (reObject != null && 1 == reObject.getIntValue("code")) {
                                JSONArray jsonArray = reObject.getJSONArray("data");
                                if (jsonArray != null && jsonArray.size() > 0) {
                                    for (int k = 0; k < jsonArray.size(); k++) {
                                        JSONObject data = jsonArray.getJSONObject(k);
                                        AppchannelAppidVo in = inMap.get(today + app.getAppid());
                                        if (in == null) {
                                            in = new AppchannelAppidVo();
                                            in.setTdate(today);
                                            in.setAppid(app.getAppid());
                                            in.setChannel("vivo");
                                        }

                                        String pv = data.getString("view");
                                        String income = data.getString("income");
                                        if ("2".equals(p)) {
                                            in.setOpen_pv(pv);
                                            in.setOpen_income(income);
                                        } else if ("3".equals(p)) {
                                            in.setBan_pv(pv);
                                            in.setBan_income(income);
                                        } else if ("4".equals(p)) {
                                            in.setScreen_pv(pv);
                                            in.setScreen_income(income);
                                        } else if ("5".equals(p)) {
                                            in.setTraffic_pv(pv);
                                            in.setTraffic_income(income);
                                        } else if ("9".equals(p)) {
                                            in.setVideo_pv(pv);
                                            in.setVideo_income(income);
                                            in.setIncome(new BigDecimal(in.getOpen_income() == null ? "0" : in.getOpen_income())
                                                    .add(new BigDecimal(in.getBan_income() == null ? "0" : in.getBan_income()))
                                                    .add(new BigDecimal(in.getScreen_income() == null ? "0" : in.getScreen_income()))
                                                    .add(new BigDecimal(in.getTraffic_income() == null ? "0" : in.getTraffic_income()))
                                                    .add(new BigDecimal(in.getVideo_income() == null ? "0" : in.getVideo_income()))
                                                    .doubleValue() + "");
                                        }
                                        inMap.put(today + app.getAppid(), in);
                                    }
                                }
                            }
                        }
                        incomeList.addAll(inMap.values());
                    }catch (Exception e){
                        logger.error("vivo获取广告位展示和收入error:",e);
                    }
                }
            } else {
                logger.info("vivo无效结果返回：");
                paramMap.put("code", -1);
                paramMap.put("message", "vivo无效结果返回");
            }
        }
        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        paramMap.put("income", incomeList);
        return paramMap;
    }

    /**
     * 获取qq新增活跃数据
     */
    public static Map<String, Object> getQQActAdd(String tdate, AppChannelConfigVo conf) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();

        DateTime dateTime = DateTime.now().minusDays(1);
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate).withHourOfDay(11);
        String today = dateTime.toString("yyyyMMdd");
        String before = dateTime.minusDays(1).toString("yyyyMMdd");

        Map<String, String> headMap = new HashMap<>();
        headMap.put(":authority", "q.qq.com");
        headMap.put(":method", "POST");
        headMap.put(":scheme", "https");
        headMap.put("accept", "*/*");
        headMap.put("accept-encoding", "gzip, deflate, br");
        headMap.put("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,und;q=0.6");
        headMap.put("cache-control", "no-cache");
        headMap.put("content-type", "application/json");

        headMap.put("cookie", conf.getTttoken());
        headMap.put("origin", "https://q.qq.com");
        headMap.put("pragma", "no-cache");
        headMap.put("referer", "https://q.qq.com/");
        headMap.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");

        String id = conf.getTtappid();
        String name = conf.getCname();

        AppchannelAppidVo in = new AppchannelAppidVo();
        AppchannelAppidVo keep = new AppchannelAppidVo();
        in.setTdate(today);
        in.setAppid(name);
        in.setAppid_key(id);
        in.setChannel("手Q");

        keep.setTdate(before);
        keep.setAppid(name);
        keep.setAppid_key(id);
        keep.setChannel("手Q");

        String url2 = "https://q.qq.com/pb/GetRetentionTrend";
        String pm = "{\"appid\":\"" + id + "\",\"real_time_begin\":" + before + ",\"real_time_end\":" + today + ",\"calculate_type\":3}";
        String json = HttpClientUtils.getInstance().httpPost(url2, pm, headMap);
//      logger.info("json2==" + json);
        JSONObject object2 = JSONObject.parseObject(json);
        if (object2 != null && 0 == object2.getIntValue("code")) {
            JSONArray jsonArray = object2.getJSONObject("data").getJSONArray("retentionDatas");
            if (jsonArray != null && jsonArray.size() >= 3) {

                for (int p = 0; p < jsonArray.size(); p++) {
                    JSONObject object = jsonArray.getJSONObject(p);
                    if (today.equals(object.getString("ftime")) && 1 == object.getIntValue("date_type")) {
                        in.setAct_num(object.getIntValue("data_value"));
                    } else if (today.equals(object.getString("ftime")) && 2 == object.getIntValue("date_type")) {
                        in.setAdd_num(object.getIntValue("data_value"));
                    } else if (today.equals(object.getString("ftime")) && 3 == object.getIntValue("date_type")) {

                        String number = object.getString("data_value");
                        double num = new BigDecimal(number)
                                .multiply(new BigDecimal("100")).doubleValue();
                        String keep1 = BlankUtils.getNumFormat(num, 2);
                        keep.setTwo_rate(keep1 + "%");
                    }
                }
//                logger.info("\t活跃：" + in.getAct_num() + "新增：" + in.getAdd_num() + "\t次留：" + keep.getTwo_rate());
                keepList.add(keep);
            } else {
                paramMap.put("code", -1);
                paramMap.put("message", "抽取的活跃、新增数据返回为空！");
            }
        } else {
            paramMap.put("code", -1);
            paramMap.put("message", json);
        }

        // PV与收入数据
        String url3 = "https://q.qq.com/pb/GetAdDataDaily";
        String param = "{\"appid\":\"" + id + "\",\"ftimeBegin\":" + today + ",\"ftimeEnd\":" + today + ",\"channelType\":0,\"needSubPosData\":0}";
        String httpPost3 = HttpClientUtils.getInstance().httpPost(url3, param, headMap);
//        logger.info("json3==" + httpPost3);
        JSONObject object3 = JSONObject.parseObject(httpPost3);
        if (object3 != null && 0 == object3.getIntValue("code")) {
            JSONArray jsonArray = object3.getJSONObject("data").getJSONArray("AdDataDailyList");
            if (jsonArray != null && jsonArray.size() > 0) {

                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject data = jsonArray.getJSONObject(i);
                    String adType = data.getString("adType");
                    String pv = data.getString("exposure");
                    String income = BlankUtils.getNumFormat(data.getLong("revenue") / 1000000.0d, 2);

                    if ("10".equals(adType)) { // loading广告
                        in.setOpen_pv(pv);
                        in.setOpen_income(income);
                    } else if ("9".equals(adType)) { // 积木广告位
                        in.setBrick_pv(pv);
                        in.setBrick_income(income);
                    } else if ("1".equals(adType)) {
                        in.setBan_pv(pv);
                        in.setBan_income(income);
                    } else if ("8".equals(adType)) {
                        in.setScreen_pv(pv);
                        in.setScreen_income(income);
                    } else if ("6".equals(adType)) {
                        in.setTraffic_pv(pv);
                        in.setTraffic_income(income);
                    } else if ("2".equals(adType)) {
                        in.setVideo_pv(pv);
                        in.setVideo_income(income);
                    } else if ("0".equals(adType)) {
                        in.setIncome(income);
                    }
                }
                sinkList.add(in);
            } else {
                paramMap.put("code", -1);
                paramMap.put("message", "抽取的PV、收入数据返回为空！");
            }
        } else {
            paramMap.put("code", -1);
            paramMap.put("message", httpPost3);
        }

        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        return paramMap;
    }

    /**
     * 获取wx新增活跃数据
     */
    public static Map<String, Object> getWxActAdd(String tdate, AppChannelConfigVo conf,Map<String, Map<String, Object>> wxChannelManageMap) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> sinkDataList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();
//      logger.info("同步时间：" + tdate);
        DateTime dateTime = DateTime.now().minusDays(1).withTimeAtStartOfDay();
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate);
        String today = dateTime.toString("yyyyMMdd");
        String before = dateTime.minusDays(1).toString("yyyyMMdd");
        long ts = (dateTime.getMillis() / 1000);
        long ts2 = (dateTime.minusDays(1).getMillis() / 1000);


        String session_id = conf.getTttoken();
        String url = "https://game.weixin.qq.com/cgi-bin/gamewxagdatawap/getwxagapplist"
                + "?session_id=" + session_id
                + "&data=" + URLEncoder.encode("{\"offset\":\"0\",\"limit\":50}");
        String json = HttpClientUtils.getInstance().httpGet(url);
//      logger.info("json==" + json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (jsonObject != null && 0 == jsonObject.getIntValue("errcode")) {
            JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("app_list");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject data = jsonArray.getJSONObject(i);
                String appid = data.getString("appid");
                String name = data.getString("appname");
//              logger.info(name + "--" + appid);
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject data = jsonArray.getJSONObject(i);
                String appid = data.getString("appid");
                String name = data.getString("appname");
//              logger.info(name + "--" + appid);
                AppchannelAppidVo in = new AppchannelAppidVo();
                AppchannelAppidVo keep = new AppchannelAppidVo();
                in.setTdate(today);
                in.setAppid(name);
                in.setAppid_key(appid);
                in.setChannel("微信");

                keep.setTdate(before);
                keep.setAppid(name);
                keep.setAppid_key(appid);
                keep.setChannel("微信");

                String param1 = "{\"need_app_info\":true,\"appid\":\"" + appid + "\",\"sequence_index_list\":[{\"size_type\":24,\"stat_type\":1000001,\"data_field_id\":5,\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400},\"filter_list\":[]},{\"size_type\":24,\"stat_type\":1000126,\"data_field_id\":7,\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400},\"filter_list\":[]},{\"size_type\":24,\"stat_type\":1000126,\"data_field_id\":5,\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400},\"filter_list\":[]},{\"size_type\":24,\"stat_type\":1000126,\"data_field_id\":6,\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400},\"filter_list\":[]},{\"size_type\":24,\"stat_type\":1000021,\"data_field_id\":3,\"filter_list\":[{\"name\":\"全部平台\",\"field_id\":8,\"value\":\"-9999\"}],\"time_period\":{\"start_time\":" + ts2 + ",\"duration_seconds\":86400}}],\"group_index_list\":[],\"rank_index_list\":[],\"version\":2}";
                String url1 = "https://game.weixin.qq.com/cgi-bin/gamewxagbdatawap/getwxagstat"
                        + "?session_id=" + session_id
                        + "&data=" + URLEncoder.encode(param1);

                String json1 = HttpClientUtils.getInstance().httpGet(url1);
                JSONObject jsonObject1 = JSONObject.parseObject(json1);
                if (jsonObject1 != null && 0 == jsonObject1.getIntValue("errcode")) {
                    JSONArray array = jsonObject1.getJSONObject("data").getJSONArray("sequence_data_list");
                    if (array != null && array.size() >= 3) {
                        int in0 = array.getJSONObject(0)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        int in1 = array.getJSONObject(1)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        int in2 = array.getJSONObject(2)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        int in3 = array.getJSONObject(3)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        String in4 = (BlankUtils.getNumFormat(array.getJSONObject(4)
                                .getJSONArray("point_list").getJSONObject(0).getDoubleValue("value") * 100, 2) + "%");

//                      logger.info("活跃：" + in0 + "\t新增：" + in1 + "\t留存：" + in2 + "\t回流：" + in3 + "\t次留：" + in4);
                        in.setAct_num(in0);
                        in.setAdd_num(in1);

                        keep.setTwo_rate(in4);
                        keepList.add(keep);
                    }
                }

                String param2 = "{\"need_app_info\":true,\"appid\":\"" + appid + "\",\"sequence_index_list\":[{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":5,\"filter_list\":[{\"name\":\"banner广告\",\"field_id\":2,\"value\":\"8040321819858439\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":5,\"filter_list\":[{\"name\":\"插屏广告\",\"field_id\":2,\"value\":\"3030046789020061\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":5,\"filter_list\":[{\"name\":\"激励视频广告\",\"field_id\":2,\"value\":\"1030436212907001\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":3,\"filter_list\":[{\"name\":\"banner广告\",\"field_id\":2,\"value\":\"8040321819858439\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":3,\"filter_list\":[{\"name\":\"插屏广告\",\"field_id\":2,\"value\":\"3030046789020061\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":3,\"filter_list\":[{\"name\":\"激励视频广告\",\"field_id\":2,\"value\":\"1030436212907001\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":5,\"filter_list\":[{\"name\":\"原生模板广告\",\"field_id\":2,\"value\":\"4071202390577885\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}},{\"size_type\":24,\"stat_type\":1000020,\"data_field_id\":3,\"filter_list\":[{\"name\":\"原生模板广告\",\"field_id\":2,\"value\":\"4071202390577885\"}],\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400}}],\"group_index_list\":[],\"rank_index_list\":[],\"version\":2}";
                String url2 = "https://game.weixin.qq.com/cgi-bin/gamewxagbdatawap/getwxagstat"
                        + "?session_id=" + session_id
                        + "&data=" + URLEncoder.encode(param2);
                String json2 = HttpClientUtils.getInstance().httpGet(url2);
                JSONObject jsonObject2 = JSONObject.parseObject(json2);
                if (jsonObject2 != null && 0 == jsonObject2.getIntValue("errcode")) {
                    JSONArray array = jsonObject2.getJSONObject("data").getJSONArray("sequence_data_list");
                    if (array != null && array.size() >= 6) {
                        int in0 = array.getJSONObject(0)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        int in1 = array.getJSONObject(1)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        int in2 = array.getJSONObject(2)
                                .getJSONArray("point_list").getJSONObject(0).getIntValue("value");
//                      logger.info("bannerPV：" + in0 + "\t插屏PV：" + in1 + "\t视频PV：" + in2);

                        String in00 = BlankUtils.getNumFormat(array.getJSONObject(3)
                                .getJSONArray("point_list").getJSONObject(0).getDoubleValue("value"), 2);
                        String in11 = BlankUtils.getNumFormat(array.getJSONObject(4)
                                .getJSONArray("point_list").getJSONObject(0).getDoubleValue("value"), 2);
                        String in22 = BlankUtils.getNumFormat(array.getJSONObject(5)
                                .getJSONArray("point_list").getJSONObject(0).getDoubleValue("value"), 2);
//                      logger.info("banner收入：" + in00 + "\t插屏收入：" + in11 + "\t视频收入：" + in22);

                        int in3 = array.getJSONObject(6)
                        		.getJSONArray("point_list").getJSONObject(0).getIntValue("value");
                        String in33 = BlankUtils.getNumFormat(array.getJSONObject(7)
                        		.getJSONArray("point_list").getJSONObject(0).getDoubleValue("value"), 2);
//                      logger.info("原生模板曝光PV："+in3+"\t原生模板收入："+in33);

                        in.setBan_pv(in0 + "");
                        in.setBan_income(in00);
                        in.setScreen_pv(in1 + "");
                        in.setScreen_income(in11);
                        in.setVideo_pv(in2 + "");
                        in.setVideo_income(in22);
                        in.setTraffic_pv(in3 + "");
                        in.setTraffic_income(in33);
                        in.setIncome(new BigDecimal(in.getBan_income() == null ? "0" : in.getBan_income())
                                .add(new BigDecimal(in.getScreen_income() == null ? "0" : in.getScreen_income()))
                                .add(new BigDecimal(in.getVideo_income() == null ? "0" : in.getVideo_income()))
                                .add(new BigDecimal(in.getTraffic_income() == null ? "0" : in.getTraffic_income()))
                                .doubleValue() + "");
                        sinkList.add(in);
                    } else {
                        paramMap.put("code", -1);
                        paramMap.put("message", "获取PV与收入数据为空！");
                    }
                }

            }
        } else {
            paramMap.put("code", -1);
            paramMap.put("message", json);
        }
        //过滤只写入配置表wx_channel_manage app_channel_config  app_channel_collect  内产品
        if (sinkList.size()>0){
            for (AppchannelAppidVo vo:sinkList){
                Object exits = wxChannelManageMap.get(vo.getAppid_key());
                if (exits!=null){
                    sinkDataList.add(vo);
                }
            }
        }
        paramMap.put("sink", sinkDataList);
        paramMap.put("keep", keepList);
        return paramMap;
    }


    /**
     * 获取wx新增用户渠道分析
     */
    public static List<JSONObject> getWxActAddTwo(String tdate, AppChannelConfigVo conf) {
        List<JSONObject> sinkList = new ArrayList<JSONObject>();

        DateTime dateTime = DateTime.now().minusDays(1).withTimeAtStartOfDay();
        if (!BlankUtils.checkBlank(tdate))
            dateTime = DateTime.parse(tdate);
        String today = dateTime.toString("yyyyMMdd");
        String before = dateTime.minusDays(1).toString("yyyyMMdd");
        long ts = (dateTime.getMillis() / 1000);
        long ts2 = (dateTime.minusDays(1).getMillis() / 1000);


        String session_id = conf.getTttoken();
        String url = "https://game.weixin.qq.com/cgi-bin/gamewxagdatawap/getwxagapplist"
                + "?session_id=" + session_id
                + "&data=" + URLEncoder.encode("{\"offset\":\"0\",\"limit\":50}");
        String json = HttpClientUtils.getInstance().httpGet(url);
//      logger.info("json==" + json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (jsonObject != null && 0 == jsonObject.getIntValue("errcode")) {
            JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("app_list");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject data = jsonArray.getJSONObject(i);
                String appid = data.getString("appid");
                String name = data.getString("appname");
//              logger.info(name + "--" + appid);

                String param1 = "{\"need_app_info\":true,\"appid\":\"" + appid + "\",\"sequence_index_list\":[],\"group_index_list\":[],\"rank_index_list\":[{\"size_type\":24,\"main_index\":{\"stat_type\":1000036,\"key_field_id\":5,\"data_field_id\":6,\"name\":\"来源\",\"size_type\":24,\"filter_list\":[{\"field_id\":3,\"value\":\"小游戏\"},{\"value\":\"-9999\",\"field_id\":4}]},\"join_index_list\":[{\"name\":\"新增用户\",\"stat_type\":1000036,\"key_field_id\":5,\"data_field_id\":6,\"size_type\":24,\"filter_list\":[{\"field_id\":3,\"value\":\"小游戏\"},{\"value\":\"-9999\",\"field_id\":4}]},{\"name\":\"首次付费用户数\",\"stat_type\":1000036,\"key_field_id\":5,\"data_field_id\":7,\"size_type\":24,\"filter_list\":[{\"field_id\":3,\"value\":\"小游戏\"},{\"value\":\"-9999\",\"field_id\":4}]},{\"name\":\"首次付费总收入\",\"stat_type\":1000036,\"key_field_id\":5,\"data_field_id\":8,\"size_type\":24,\"unit\":\"元\",\"filter_list\":[{\"field_id\":3,\"value\":\"小游戏\"},{\"value\":\"-9999\",\"field_id\":4}]},{\"name\":\"注册用户次日留存率\",\"stat_type\":1000036,\"key_field_id\":5,\"data_field_id\":9,\"size_type\":24,\"unit\":\"%\",\"filter_list\":[{\"field_id\":3,\"value\":\"小游戏\"},{\"value\":\"-9999\",\"field_id\":4}]}],\"cur_page\":0,\"per_page\":50,\"time_period\":{\"start_time\":" + ts + ",\"duration_seconds\":86400},\"is_stat_order_asc\":false}],\"version\":2}";
                String url1 = "https://game.weixin.qq.com/cgi-bin/gamewxagbdatawap/getwxagstat"
                        + "?session_id=" + session_id
                        + "&data=" + URLEncoder.encode(param1);

                String json1 = HttpClientUtils.getInstance().httpGet(url1);
                JSONObject jsonObject1 = JSONObject.parseObject(json1);
                if (jsonObject1 != null && 0 == jsonObject1.getIntValue("errcode")) {
                    JSONArray array = jsonObject1.getJSONObject("data").getJSONArray("rank_data_list");
                    if (array != null && array.size() == 1) {
                        // 数据列表
                        JSONArray list = array.getJSONObject(0).getJSONArray("stat_list");
                        if (list != null && list.size() > 0) {
                            for (int p = 0; p < list.size(); p++) {
                                JSONObject object = list.getJSONObject(p);
                                JSONObject in = new JSONObject();
                                in.put("tdate", today);
                                in.put("wxname", name);
                                in.put("wxappid", appid);
                                in.put("channel", "动能");
                                in.put("add_name", object.getString("key_field_label"));
                                in.put("add_appid", object.getString("key_field_value"));
                                in.put("addnum", object.getJSONObject("main_point").getIntValue("value"));
                                sinkList.add(in);
                            }
                        }

                    }
                }

            }
        } else {
            return null;
        }

        return sinkList;
    }

    /**
     * 根据vivo应用ID转换为动能产品ID
     *
     * @param ttappid
     * @return
     */
    public static String vivoToDnwx(String ttappid) {
        if ("7657".equals(ttappid))
            return "37830"; // 超级木旋3D
        else if ("7565".equals(ttappid))
            return "37829"; // 推开一切
        else if ("7141".equals(ttappid))
            return "37801"; // 救救宝宝
        else if ("8292".equals(ttappid))
            return "37854"; // 无限跑酷
        else if ("9462".equals(ttappid))
            return "37883"; // 我开了间珠宝店
        else if ("8050".equals(ttappid))
            return "37831"; // 硬币向前冲
        else if ("8666".equals(ttappid))
            return "37853"; // 方块鸟别跑
        else if ("7956".equals(ttappid))
            return "37840"; // 画个腿快跑
        else if ("9013".equals(ttappid))
            return "37895"; // 来切我鸭
        else
            return "10086";
    }

    @Scheduled(cron = "05 0 0 * * MON")
    public void initUserList() {

        logger.info("周一数据清零  " + new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
        long start = System.currentTimeMillis();
        ZSetOperations<String, Object> opsForZSet = redisTemplate.opsForZSet();

        // 清理排行榜数据
        List<String> appidList = superMathService.selectSuperMathAppidList();
        for (String str : appidList) { // 默认为升序，保留前1万名和后2万名，确保升序或降序排名都不变
            opsForZSet.removeRange(CommonUtil.REDIS_ZSET_SUPERM_TOP + str, 10000, -20000);
        }

        // 使用scan循环将所有super_userinfo_vo开头的用户设置失效时间，获取数据库中过期用户，删除其在积分排行中数据

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("user_score", 600000);
        map.put("user_rank", 10000);
        map.put("project_id", 20462);
        superMathService.updateSuperMathVoByAttr(map);
        map.clear();

        // 批量修改内存中排行数据
        Set<TypedTuple<Object>> tuples = new HashSet<TypedTuple<Object>>();
        Iterator<Object> iterator = opsForZSet
                .range(CommonUtil.REDIS_ZSET_SUPERM_TOP + "20462", 0, -1)
                .iterator();
        while (iterator.hasNext()) {
            String value = (String) iterator.next();
            tuples.add(new DefaultTypedTuple(value, 600000d));
        }
        opsForZSet.add(CommonUtil.REDIS_ZSET_SUPERM_TOP + "20462", tuples);

        long end = System.currentTimeMillis();
        logger.info("数据清零消耗 " + (end - start) / 1000.00d + " 秒");
    }

    /**
     * 获取wx新增活跃数据,从微信文档接口中获取
     * https://developers.weixin.qq.com/minigame/dev/api-backend/open-api/data-analysis/analysis.getGameAnalysisData.html
     */
    public static Map<String, Object> getWxActAddNew(String tdate) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        List<AppchannelAppidVo> keepList = new ArrayList<AppchannelAppidVo>();
        logger.info("同步时间：" + tdate);
        //获取需要同步的游戏集合,静态方法需要手动导入bean
        AdMapper adMapper = ApplicationContextUtils.get(AdMapper.class);
        List<WxAppInfo> useAppInfos = adMapper.getUseAppInfo();
        for (WxAppInfo wxAppInfo : useAppInfos) {
            Map<String, Object> res = getWxActAddByAppid(tdate, wxAppInfo.getAppid(), wxAppInfo.getSecret(), wxAppInfo.getName());
            if (res.get("code") == null) {
                sinkList.add((AppchannelAppidVo)res.get("link"));
                keepList.add((AppchannelAppidVo)res.get("keep"));
            }else{
                return res;
            }
        }
        paramMap.put("sink", sinkList);
        paramMap.put("keep", keepList);
        return paramMap;

    }

    /**
     * 单个微信小游戏汇总活跃数据
     * @param date 日期
     * @param appid 小游戏appid
     * @param scret 小游戏密钥
     * @param name 游戏名称
     * @return
     */
    public static Map<String, Object> getWxActAddByAppid(String date, String appid, String scret, String name) {
        //1.初始化返回参数
        Map<String, Object> result = new HashMap<String, Object>();
        AppchannelAppidVo in = new AppchannelAppidVo();
        AppchannelAppidVo keep = new AppchannelAppidVo();

        //2.获取小游戏token
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + scret;
        String json = HttpClientUtils.getInstance().httpGet(url);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (jsonObject == null || jsonObject.getInteger("errcode") != null) {
            result.put("code", -1);
            result.put("message", jsonObject.get("errmsg"));
            return result;
        }
        String token = jsonObject.getString("access_token");
        logger.info(name+"--"+appid+"--"+scret);

        //3.计算日期区间
        DateTime dateTime = DateTime.parse(date);
        Long todayTime = dateTime.getMillis() / 1000;
        Long endTime = dateTime.plusDays(1).getMillis() / 1000;
        Long startTime = dateTime.minusDays(1).getMillis() / 1000;

        //4.新增活跃用户汇总
        url = "https://api.weixin.qq.com/datacube/getgameanalysisdata?access_token=" + token;
        String res = "";
        //异常处理是为了抓取调用微信三方抛出异常返回
        try {
            res = returnWxData(todayTime, endTime, url, "1000001:5");
            in.setAct_num(Integer.parseInt(res));
        }catch (Exception e) {
            result.put("code", -1);
            result.put("message", res);
            return result;
        }
        try {
            res = returnWxData(todayTime, endTime, url, "1000011:3");
            in.setAdd_num(Integer.parseInt(res));

        }catch (Exception e) {
            result.put("code", -1);
            result.put("message", res);
            return result;
        }
        try {
            res = returnWxData(startTime, todayTime, url, "1000021:3");
            BigDecimal bd = new BigDecimal(res).multiply(new BigDecimal(100));
            keep.setTwo_rate(bd.setScale(2, BigDecimal.ROUND_HALF_UP)+"%");
        }catch (Exception e) {
            result.put("code", -1);
            result.put("message", res);
            return result;
        }

        //5.广告汇总
        String bannerUrl = "https://api.weixin.qq.com/publisher/stat?action=publisher_adpos_general&page=1&page_size=90&access_token=" + token
                + "&start_date=" + date + "&end_date=" + date;
        String bannerJson = HttpClientUtils.getInstance().httpGet(bannerUrl);
        JSONObject bannerObject = JSONObject.parseObject(bannerJson);
        if (bannerObject == null || bannerObject.getInteger("errcode") != null) {
            result.put("code", -1);
            result.put("message", bannerObject.getString("errmsg"));
            return result;
        }
        String in0 = "0", in1 = "0", in2 = "0", in00 = "0", in11 = "0", in22 = "0";
        if (bannerObject.getInteger("total_num") > 0) {
            JSONArray list = bannerObject.getJSONArray("list");
            for (int i = 0; i < list.size(); i++) {
                JSONObject object = list.getJSONObject(i);
                if ("SLOT_ID_WEAPP_BANNER".equals(object.get("ad_slot"))) {
                    in0 = object.getString("exposure_count");
                    in00 = (object.getDouble("income") / 100) + "";
                } else if ("SLOT_ID_WEAPP_INTERSTITIAL".equals(object.get("ad_slot"))) {
                    in1 = object.getString("exposure_count");
                    in11 = (object.getDouble("income") / 100) + "";
                } else if ("SLOT_ID_WEAPP_REWARD_VIDEO".equals(object.get("ad_slot"))) {
                    in2 = object.getString("exposure_count");
                    in22 = (object.getDouble("income") / 100) + "";
                }
            }
        }

        //6.插入汇总的数据
        in.setTdate(dateTime.toString("yyyyMMdd"));
        in.setAppid(name);
        in.setAppid_key(appid);
        in.setChannel("微信");
        in.setBan_pv(in0);
        in.setBan_income(in00);
        in.setScreen_pv(in1);
        in.setScreen_income(in11);
        in.setVideo_pv(in2);
        in.setVideo_income(in22);
        in.setIncome(new BigDecimal(in.getBan_income() == null ? "0" : in.getBan_income())
                .add(new BigDecimal(in.getScreen_income() == null ? "0" : in.getScreen_income()))
                .add(new BigDecimal(in.getVideo_income() == null ? "0" : in.getVideo_income()))
                .doubleValue() + "");
        keep.setTdate(dateTime.minusDays(1).toString("yyyyMMdd"));
        keep.setAppid(name);
        keep.setAppid_key(appid);
        keep.setChannel("微信");
        result.put("link", in);
        result.put("keep", keep);

        logger.info("活跃:" + in.getAct_num() + "   新增:" + in.getAdd_num() + "   次留:" + keep.getTwo_rate());
        logger.info("bannerPV：" + in0 + "\t插屏PV：" + in1 + "\t视频PV：" + in2);
        logger.info("banner收入：" + in00 + "\t插屏收入：" + in11 + "\t视频收入：" + in22);

        return result;

    }

    /**
     *
     * @param startTime 开始时间戳
     * @param endTime  结束时间戳
     * @param url 小游戏数据分析接口
     * @param metric 指标id 活跃:1000001:5 新增:1000011:3 次留:1000021:3
     * @return
     */
    public static String returnWxData(Long startTime, Long endTime, String url, String metric) {
        String param = "{\n" +
                "    \"metric\" : \"" + metric + "\",\n" +
                "    \"granularity\" : 24,\n" +
                "    \"start_time\" : " + startTime + ",\n" +
                "    \"end_time\" : " + endTime + "\n" +
                "}";

        String json1 = HttpClientUtils.getInstance().httpPost(url, param, null);
        JSONObject jsonObject1 = JSONObject.parseObject(json1);
        if (jsonObject1 == null || jsonObject1.getInteger("errcode") != null) {
            return jsonObject1.getString("errmsg");
        }
        String res =  jsonObject1.getJSONArray("data_list").getJSONObject(0).getString("metric_value");
        //可能存在没有metric_value参数,即为0
        if (BlankUtils.checkBlank(res)) {
            res = "0";
        }
        return res;
    }


    public static String getOppoApiData(String userid,String api_key,String day){
        try {
            String timestamp = (DateTime.now().getMillis()/1000)+"";
            String sign = DigestUtils.sha1DigestAsHex(userid+api_key+timestamp);
            String token = Base64.getEncoder().encodeToString((userid+","+timestamp+","+sign).getBytes("utf-8"));

            Map<String, String> headMap = new HashMap<>();
            headMap.put("content-type","application/x-www-form-urlencoded");
            String param = "userId="+userid+"&token="+token+"&timestamp="+timestamp+"&startTime="+day+"&endTime="+day+"";

            String url = "https://uapi.ads.heytapmobi.com/union/api/report/posQuery";
            String httpGet = HttpClientUtils.getInstance().httpGet(url+"?"+param, headMap);
            return httpGet;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws InterruptedException {
        String cookie = "passport_csrf_token=cc3f82d728519602baaba546ee1c5b63; _tea_utm_cache_2018=undefined; odin_tt=495c2fec3b1db61fd11501516e850d731fe4d69d887a63e9e5480c43f7574430fabfc25261c8d3301eb653a15d082b54ddd85808d68f61477afab77fe7bbcf56; sid_guard_microapp=adc5c63556729dc75ca7f41fcac0cc71%7C1672799404%7C5184000%7CSun%2C+05-Mar-2023+02%3A30%3A04+GMT; uid_tt_microapp=f25afabdb92bb545cea886bcaf0a1558; uid_tt_ss_microapp=f25afabdb92bb545cea886bcaf0a1558; sid_tt_microapp=adc5c63556729dc75ca7f41fcac0cc71; sessionid_microapp=adc5c63556729dc75ca7f41fcac0cc71; sessionid_ss_microapp=adc5c63556729dc75ca7f41fcac0cc71; sid_ucp_v1_microapp=1.0.0-KDk3YTliMWY1NWRiZWI4MTBiYWMyYWVhYTY2Mjk5NzM1YjRjMGYzZTgKGQjo8aDT4fXhBxCsydOdBhjVCiAMOAFA6wcaAmxmIiBhZGM1YzYzNTU2NzI5ZGM3NWNhN2Y0MWZjYWMwY2M3MQ; ssid_ucp_v1_microapp=1.0.0-KDk3YTliMWY1NWRiZWI4MTBiYWMyYWVhYTY2Mjk5NzM1YjRjMGYzZTgKGQjo8aDT4fXhBxCsydOdBhjVCiAMOAFA6wcaAmxmIiBhZGM1YzYzNTU2NzI5ZGM3NWNhN2Y0MWZjYWMwY2M3MQ; x-jupiter-uuid=16727994047512288; MONITOR_WEB_ID=f27bea88-9a48-476d-bc87-801bc5463a14; MONITOR_DEVICE_ID=d5020365-604f-42c1-9b02-cbc950bef820; ttwid=1%7CaWHxy1SfMptqJ23L4Ze0SihGoC6qpsgkQtbD-qDEF8Y%7C1672801546%7Cfe9ec1305f18e99a316a231a4a66b6ebbd9734ce3f9af177ff2ad50ccee358ba; s_v_web_id=verify_lcpkxd0d_mVs58Uos_HrIb_4JLs_AW9Q_BQKpXeQ7HT4U; csrf_session_id=ecf77a0a80579346c16f5f9520d4a882";
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("cookie", cookie);
        //banner
        String banner = "20037,30037,30037,28043,80037,40043";
        //激励视频
        String video = "20038,30038,30038,28042,26038,3589830,80038,16038,40042";
        //插屏
        String plaque = "20043,28044,80043,40044";
        headerMap.put("cookie", cookie);
        String url = "https://microapp.bytedance.com/platform_api/v1/app/getAppList?type=2";
        String result = HttpRequest.get(url, headerMap);
        String startDate = "2022-12-01";
        String endDate = "2022-12-03";
        List<String> dateList = DateUtil.getDays(startDate, endDate);
        List<AppchannelAppidVo> sinkList = new ArrayList<AppchannelAppidVo>();
        Map<String, AppchannelAppidVo> dataMap = new LinkedHashMap<>();
        Map<String, AppchannelAppidVo> bannerDataMap = new HashMap<>();
        Map<String, AppchannelAppidVo> videoDataMap = new HashMap<>();
        Map<String, AppchannelAppidVo> plaqueDataMap = new HashMap<>();
        if (!BlankUtils.checkBlank(result)) {
            JSONObject resultJson = JSONObject.parseObject(result);
            JSONObject dataJson = resultJson.getJSONObject("data");
            JSONArray appArray = dataJson.getJSONArray("appList");
            //先初始化数据
            for (Object obj : appArray) {

                JSONObject appInfoDataJson = (JSONObject) obj;
                JSONObject appInfoJson = appInfoDataJson.getJSONObject("appInfo");
                String appid = appInfoJson.getString("appId");
                String appname = appInfoJson.getString("name");
                String state = appInfoJson.getString("state");
                //只有状态为1的才进行数据查询
                if ("1".equals(state)) {
                    for (String date : dateList) {
                        AppchannelAppidVo app = new AppchannelAppidVo();
                        String dataKey = date + "_" + appid;
                        app.setAppid(appname);
                        app.setAppid_key(appid);
                        app.setTdate(date);
                        app.setChannel("字节");
                        app.setBan_pv("0");
                        app.setBan_income("0");
                        app.setScreen_pv("0");
                        app.setScreen_income("0");
                        app.setVideo_pv("0");
                        app.setVideo_income("0");
                        dataMap.put(dataKey, app);
                    }
                }
            }
            for (Object obj : appArray) {
                Integer page = 1;
                Integer pageSize = 31;

                JSONObject appInfoDataJson = (JSONObject) obj;
                JSONObject appInfoJson = appInfoDataJson.getJSONObject("appInfo");
                String appid = appInfoJson.getString("appId");
                String state = appInfoJson.getString("state");
                if ("1".equals(state)) {
                    String bannerIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + banner + "&hostId=0";
                    String incomeResult = HttpRequest.get(bannerIncomeUrl, headerMap);
                    if (!BlankUtils.checkBlank(incomeResult)) {
                        JSONObject incomeResultJson = JSONObject.parseObject(incomeResult);
                        JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                        JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                        for (Object incomeObj : incomeResultDataArray) {
                            AppchannelAppidVo app = new AppchannelAppidVo();
                            JSONObject incomeData = (JSONObject) incomeObj;
                            String pv = incomeData.getString("show");
                            String income = incomeData.getString("realCost");
                            String date = incomeData.getString("date");
                            app.setBan_pv(pv);
                            app.setBan_income(income);
                            String dataKey = date + "_" + appid;
                            bannerDataMap.put(dataKey, app);
                        }
                    }

                    String videoIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + video + "&hostId=0";
                    String videoIncomeResult = HttpRequest.get(videoIncomeUrl, headerMap);
                    if (!BlankUtils.checkBlank(videoIncomeResult)) {
                        JSONObject incomeResultJson = JSONObject.parseObject(videoIncomeResult);
                        JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                        JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                        for (Object incomeObj : incomeResultDataArray) {
                            AppchannelAppidVo app = new AppchannelAppidVo();
                            JSONObject incomeData = (JSONObject) incomeObj;
                            String pv = incomeData.getString("show");
                            String income = incomeData.getString("realCost");
                            String date = incomeData.getString("date");
                            app.setVideo_pv(pv);
                            app.setVideo_income(income);
                            String dataKey = date + "_" + appid;
                            videoDataMap.put(dataKey, app);
                        }
                    }

                    String plaqueIncomeUrl = "https://microapp.bytedance.com/platform_api/game/pay/v1/" + appid + "/settlement/traffic/ad_data?startDate=" + startDate + "&endDate=" + endDate + "&page=" + page + "&size=" + pageSize + "&reqType=1&ritId=" + plaque + "&hostId=0";
                    String plaqueIncomeResult = HttpRequest.get(plaqueIncomeUrl, headerMap);
                    if (!BlankUtils.checkBlank(plaqueIncomeResult)) {
                        JSONObject incomeResultJson = JSONObject.parseObject(plaqueIncomeResult);
                        JSONObject incomeResultDataJson = incomeResultJson.getJSONObject("data");
                        JSONArray incomeResultDataArray = incomeResultDataJson.getJSONArray("adData");
                        for (Object incomeObj : incomeResultDataArray) {
                            AppchannelAppidVo app = new AppchannelAppidVo();
                            JSONObject incomeData = (JSONObject) incomeObj;
                            String pv = incomeData.getString("show");
                            String income = incomeData.getString("realCost");
                            String date = incomeData.getString("date");
                            app.setScreen_pv(pv);
                            app.setScreen_income(income);
                            String dataKey = date + "_" + appid;
                            plaqueDataMap.put(dataKey, app);
                        }
                    }
                }
            }
            //遍历汇总数据
            for (Map.Entry<String, AppchannelAppidVo> each : dataMap.entrySet()) {
                AppchannelAppidVo data = each.getValue();
                String key = each.getKey();
                //banner收入
                if (bannerDataMap.get(key) != null) {
                    AppchannelAppidVo bannerData = bannerDataMap.get(key);
                    data.setBan_income(bannerData.getBan_income());
                    data.setBan_pv(bannerData.getBan_pv());
                }
                //video收入
                if (videoDataMap.get(key) != null) {
                    AppchannelAppidVo videoData = videoDataMap.get(key);
                    data.setVideo_income(videoData.getVideo_income());
                    data.setVideo_pv(videoData.getVideo_pv());
                }
                //plaque收入
                if (plaqueDataMap.get(key) != null) {
                    AppchannelAppidVo plaqueData = plaqueDataMap.get(key);
                    data.setScreen_income(plaqueData.getScreen_income());
                    data.setScreen_pv(plaqueData.getScreen_pv());
                }

                BigDecimal incomeData = new BigDecimal("0");
                if (!BlankUtils.checkBlank(data.getBan_income())) {
                    incomeData.add(new BigDecimal(data.getBan_income()));
                }
                if (!BlankUtils.checkBlank(data.getScreen_income())) {
                    incomeData.add(new BigDecimal(data.getScreen_income()));
                }
                if (!BlankUtils.checkBlank(data.getVideo_income())) {
                    incomeData.add(new BigDecimal(data.getVideo_income()));
                }
                data.setIncome(incomeData.doubleValue() + "");
                sinkList.add(data);
            }

        }
    }

}
