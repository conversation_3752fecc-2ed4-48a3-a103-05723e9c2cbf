package com.wbgame.common.strategyEnum;

import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;

public enum Table {
    ROI,
    CHANNEL_SPEND,
    SPEND;

    public static Table of(@NotNull String value) {
        return Table.valueOf(value.toUpperCase());
    }

    @NotNull
    @Contract(pure = true)
    public String out() {
        return this.name().toLowerCase();
    }
}
