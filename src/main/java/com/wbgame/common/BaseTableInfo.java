package com.wbgame.common;

import java.util.Date;

/**
 * @description: 对象操作表基础信息
 * @author: huangmb
 * @date: 2021/06/08
 **/
public class BaseTableInfo {

    private Date createTime;

    private Date updateTime;

    private String createStr;

    private String updateStr;

    private String createOwner;

    private String updateOwner;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateStr() {
        return createStr;
    }

    public void setCreateStr(String createStr) {
        this.createStr = createStr;
    }

    public String getUpdateStr() {
        return updateStr;
    }

    public void setUpdateStr(String updateStr) {
        this.updateStr = updateStr;
    }

    public String getCreateOwner() {
        return createOwner;
    }

    public void setCreateOwner(String createOwner) {
        this.createOwner = createOwner;
    }

    public String getUpdateOwner() {
        return updateOwner;
    }

    public void setUpdateOwner(String updateOwner) {
        this.updateOwner = updateOwner;
    }
}
