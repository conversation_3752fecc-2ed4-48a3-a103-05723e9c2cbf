package com.wbgame.service.clean.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.ManagePlatformMapper;
import com.wbgame.pojo.clean.ManagePlatform;
import com.wbgame.pojo.clean.ManagePlatformVO;
import com.wbgame.service.clean.IManagePlatformService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/10/20
 * @class: ManagePlatformServiceImpl
 * @description:
 */
@Service
public class ManagePlatformServiceImpl implements IManagePlatformService {

    @Autowired
    private ManagePlatformMapper managePlatformMapper;


    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteManagePlatform(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure(Constants.ParamError)
                : ResultUtils.success(managePlatformMapper.deleteManagePlatform(idList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertManagePlatform(ManagePlatform record) {

        return ResultUtils.success(managePlatformMapper.insertManagePlatform(record));
    }

    @Override
    public Result<PageResult<ManagePlatformVO>> selectManagePlatform(ManagePlatform managePlatform) {

        PageHelper.startPage(managePlatform.getStart(), managePlatform.getLimit());
        List<ManagePlatformVO> list = managePlatformMapper.selectManagePlatform(managePlatform);
        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

    @Override
    public Result<ManagePlatformVO> selectManagePlatformById(ManagePlatform managePlatform) {

        List<ManagePlatformVO> list = managePlatformMapper.selectManagePlatform(managePlatform);

        if (ObjectUtils.isEmpty(list)) {

            return ResultUtils.success();
        }
        ManagePlatformVO vo = list.get(0);
        String res = (String) vo.getRes();
        String manifestPlaceHolder = (String) vo.getManifestPlaceHolder();
        String defaultConfig = (String) vo.getDefaultConfig();
        String sdkFiles = (String) vo.getSdkFiles();


        vo.setRes(JSON.parseObject(res, Map.class));
        vo.setManifestPlaceHolder(JSON.parseObject(manifestPlaceHolder, Map.class));
        vo.setDefaultConfig(JSON.parseObject(defaultConfig, Map.class));
        vo.setSdkFiles(JSON.parseObject(sdkFiles, List.class));
        return ResultUtils.success(vo);
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateManagePlatform(ManagePlatform record) {

        return ResultUtils.success(managePlatformMapper.updateManagePlatform(record));
    }
}
