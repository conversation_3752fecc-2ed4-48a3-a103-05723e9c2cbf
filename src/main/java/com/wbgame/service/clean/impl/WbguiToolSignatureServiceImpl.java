package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.WbguiToolSignatureMapper;
import com.wbgame.pojo.clean.WbguiToolSignature;
import com.wbgame.service.clean.IWbguiToolSignatureService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/26 026
 * @class: WbguiToolSignatureServiceImpl
 * @description:
 */
@Service
public class WbguiToolSignatureServiceImpl implements IWbguiToolSignatureService {

    @Autowired
    private WbguiToolSignatureMapper wbguiToolSignatureMapper;


    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteWbguiToolSignature(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure(Constants.ParamError)
                : ResultUtils.success(wbguiToolSignatureMapper.deleteWbguiToolSignature(idList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertWbguiToolSignature(WbguiToolSignature record) {

        if (wbguiToolSignatureMapper.selectAppIdExits(record.getAppid()) != null) {

            return ResultUtils.failure("该产品已有配置");
        }
        return ResultUtils.success(wbguiToolSignatureMapper.insertWbguiToolSignature(record));
    }

    @Override
    public Result<PageResult<WbguiToolSignature>> selectWbguiToolSignature(WbguiToolSignature wbguiToolSignature) {

        PageHelper.startPage(wbguiToolSignature.getStart(), wbguiToolSignature.getLimit());
        List<WbguiToolSignature> list = wbguiToolSignatureMapper.selectWbguiToolSignature(wbguiToolSignature);
        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateWbguiToolSignature(WbguiToolSignature record) {

        Integer getId = wbguiToolSignatureMapper.selectAppIdExits(record.getAppid());
        if (getId != null && !getId.equals(record.getId())) {

            return ResultUtils.failure("该产品已有配置");
        }
        return ResultUtils.success(wbguiToolSignatureMapper.updateWbguiToolSignature(record));
    }

    @Override
    public Result<List<WbguiToolSignature>> getSignList() {
        return ResultUtils.success(wbguiToolSignatureMapper.getSignList());
    }

    @Override
    public Result<WbguiToolSignature> selectToolByAppId(String appid) {
        return ResultUtils.success(wbguiToolSignatureMapper.selectToolByAppId(appid));
    }
}
