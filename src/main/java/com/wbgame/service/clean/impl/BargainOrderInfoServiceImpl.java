package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.BargainOrderInfoMapper;
import com.wbgame.pojo.clean.chop.BargainOrderInfo;
import com.wbgame.service.clean.IBargainOrderInfoService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/11/16 016
 * @class: BargainOrderInfoServiceImpl
 * @description:
 */
@Service
public class BargainOrderInfoServiceImpl implements IBargainOrderInfoService {

    @Autowired
    private BargainOrderInfoMapper orderInfoMapper;

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteOrderInfo(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure(Constants.ParamError)
                : ResultUtils.success(orderInfoMapper.deleteOrderInfo(idList));

    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertOrderInfo(BargainOrderInfo record) {

        return ResultUtils.success(orderInfoMapper.insertOrderInfo(record));
    }

    @Override
    public Result<PageResult<BargainOrderInfo>> selectOrderInfo(BargainOrderInfo orderInfo) {

        PageHelper.startPage(orderInfo.getStart(), orderInfo.getLimit());
        List<BargainOrderInfo> list = orderInfoMapper.selectOrderInfo(orderInfo);

        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateOrderInfo(BargainOrderInfo record) {

        return ResultUtils.success(orderInfoMapper.updateOrderInfo(record));
    }
}
