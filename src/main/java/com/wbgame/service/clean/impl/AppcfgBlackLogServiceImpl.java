package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.AppcfgBlackLogMapper;
import com.wbgame.mapper.clean.master.CleanAppOutsideFunctionMapper;
import com.wbgame.pojo.clean.AppcfgBlackLog;
import com.wbgame.service.clean.IAppcfgBlackLogService;
import com.wbgame.utils.CamelCaseUtils;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2023/02/09 009
 * @class: AppcfgBlackLogServiceImpl
 * @description:
 */
@Service
public class AppcfgBlackLogServiceImpl implements IAppcfgBlackLogService {

    @Autowired
    private AppcfgBlackLogMapper appcfgBlackLogMapper;

    @Autowired
    private CleanAppOutsideFunctionMapper functionMapper;

    @Override
    public Result<PageResult<AppcfgBlackLog>> selectByExample(AppcfgBlackLog example) {

        example.setEnd_date(example.getEnd_date() + " 23:59:59");
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));
        PageHelper.startPage(example.getStart(), example.getLimit());
        List<AppcfgBlackLog> list = appcfgBlackLogMapper.selectByExample(example);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    public Result<Integer> deleteIp(List<String> ipList) {

        appcfgBlackLogMapper.deleteLogByIpList(ipList);
        int i = functionMapper.deleteSafeMarkConfigByMarkId(ipList);
        return ResultUtils.success(i);
    }
}
