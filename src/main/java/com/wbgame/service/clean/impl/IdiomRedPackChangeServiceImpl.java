package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.IdiomRedpackChangeMapper;
import com.wbgame.pojo.clean.IdiomRedpackChangeDTO;
import com.wbgame.pojo.clean.IdiomRedpackChangeVO;
import com.wbgame.service.clean.IdiomRedPackChangeService;
import com.wbgame.utils.CamelCaseUtils;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/22
 * @class: IdiomRedPackChangeServiceImpl
 * @description:
 */
@Service
public class IdiomRedPackChangeServiceImpl implements IdiomRedPackChangeService {


    private IdiomRedpackChangeMapper redpackChangeMapper;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    public void setRedpackChangeMapper(IdiomRedpackChangeMapper redpackChangeMapper) {
        this.redpackChangeMapper = redpackChangeMapper;
    }

    @Override
    public Result<PageResult<IdiomRedpackChangeVO>> selectIdiomRedpackChange(IdiomRedpackChangeDTO dto) {


        String group = CamelCaseUtils.humpToUnderline(dto.getGroup());
        dto.setGroup(group);
        dto.setSelectGroup(group);
//        if (group.contains("change_type")) {
//
//            dto.setSelectGroup(group + ",(case change_type when 9 then current_level when 10 then current_level when 11 then concat( '关卡_', current_level, '_', is_high_ad ) when 2 then concat( '任务_', task_sn ) end ) level_high_task");
//        }

        PageHelper.startPage(dto.getStart(), dto.getLimit());
        String start_date = dto.getStart_date();
        String end_date = dto.getEnd_date();

        LocalDate startDate = LocalDate
                .of(Integer.parseInt(start_date.substring(0, 4)), Integer.parseInt(start_date.substring(5, 7)), Integer.parseInt(start_date.substring(8)));

        LocalDate endDate = LocalDate
                        .of(Integer.parseInt(end_date.substring(0, 4)), Integer.parseInt(end_date.substring(5, 7)), Integer.parseInt(end_date.substring(8)))
                        .plusDays(1);


        dto.setStart_date(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() + "");
        dto.setEnd_date(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() + "");
        List<IdiomRedpackChangeVO> list = redpackChangeMapper.selectIdiomRedpackChange(active, dto);

        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

}
