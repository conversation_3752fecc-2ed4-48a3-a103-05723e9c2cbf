package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.IdiomUserWithdrawDTO;
import com.wbgame.pojo.clean.IdiomUserWithdrawVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/20
 * @class: IdiomUserWithdraw
 * @description:
 */
public interface IdiomUserWithdrawService {

    Result<Integer> deleteByPrimaryKey(List<Long> id);

    Result<Integer> insertIdiomUserWithdraw(IdiomUserWithdrawDTO record);

    Result<Integer> updateIdiomUserWithdraw(IdiomUserWithdrawDTO record);

    Result<PageResult<IdiomUserWithdrawVO>> selectIdiomUserWithdraw(IdiomUserWithdrawDTO dto);

    List<IdiomUserWithdrawVO> export(IdiomUserWithdrawDTO dto);
}
