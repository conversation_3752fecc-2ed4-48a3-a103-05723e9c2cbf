package com.wbgame.service.clean;


import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.VivoDefaultConfig;
import com.wbgame.utils.PageResult;

import java.util.List;

public interface IVivoDefaultConfigService {

    Result<Integer> deleteVivoConfig(List<String> prjidList);

    Result<Integer> insertVivoConfig(VivoDefaultConfig record);

    Result<PageResult<VivoDefaultConfig>> selectVivoConfig(VivoDefaultConfig example);

    Result<Integer> updateVivoConfig(VivoDefaultConfig record);

    Result<Integer> batchUpdateVivoConfig(VivoDefaultConfig record);


}