package com.wbgame.service.clean;

import com.wbgame.pojo.clean.WifiProductConfigDTO;

/**
 * @authoer: zhangY
 * @createDate: 2022/6/20 11:32
 * @class: IWifiProductConfigService
 * @description:
 */
public interface IWifiProductConfigService {

    String deleteById(Integer id);
    String insertProductConfig(WifiProductConfigDTO productConfig);
    String updateProductConfigById(WifiProductConfigDTO productConfig);

    String selectProductConfigAllList(WifiProductConfigDTO productConfigDTO);

    String produceSort(String ids, String cha);

    String commoditySortingQuery(String cha);
}
