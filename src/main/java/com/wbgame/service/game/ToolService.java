package com.wbgame.service.game;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.game.config.BatchFeishuExcelTaskVo;
import com.wbgame.pojo.game.config.FeishuExcelTaskVo;
import com.wbgame.pojo.game.report.MGEventNameVo;
import com.wbgame.pojo.game.report.MGEventReportVo;
import com.wbgame.pojo.game.report.ToolEventVo;
import com.wbgame.pojo.game.report.query.MGEventReportQueryVo;
import com.wbgame.utils.PageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ToolService
 * @Description 工具大数据
 * @Date 2022/6/6 12:23
 */
public interface ToolService {

    List<ToolEventVo> getToolEventList(Map<String,String> map);

    List<ToolEventVo> getToolEventListLimit(Map<String,String> map);

    List<ToolEventVo> getToolEventDataList(Map<String,String> map);

    ToolEventVo getToolEventDataSum(Map<String,String> map);

    List<MGEventReportVo> getMicGameEventReportList(MGEventReportQueryVo vo);

    MGEventReportVo getMicGameEventReportSum(MGEventReportQueryVo vo);

    List<MGEventNameVo> getMicGameEventList(MGEventReportQueryVo vo);

    /** 发送飞书excel*/
    Map<String,String> sendFSExcel(FeishuExcelTaskVo vo);
    
    /** 批量发送飞书excel*/
    Map<String,String> bacthSendFSExcel(BatchFeishuExcelTaskVo vo,int bid);
    
    /** 更新飞书发送状态*/
    int updateFSExcelSendState(FeishuExcelTaskVo vo);

    /** 查询飞书excel*/
    List<FeishuExcelTaskVo> getFSExcelList(FeishuExcelTaskVo vo);

    /** 保存飞书excel*/
    int saveFSExcel(FeishuExcelTaskVo vo);
    
    /** 保存批量发送飞书记录excel*/
    int batchSaveFSExcel(List<FeishuExcelTaskVo> list);
    
    /** 保存批量发送飞书记录excel*/
    int saveBatchSFRecord(BatchFeishuExcelTaskVo vo);

    /** 更新飞书excel*/
    int updateFSExcel(FeishuExcelTaskVo vo);

    /** 删除飞书excel*/
    int delFSExcel(String id);

    /** 删除飞书excel*/
    int deleteBatch(String id);
    
    List<JSONObject> selectMGEventReportTable(MGEventReportQueryVo query);

    List<String> selectMGEventReportData(MGEventReportQueryVo query);
    
    /** 查询飞书批量发送模板*/
    Result<List<BatchFeishuExcelTaskVo>> getBatchTemplete(String create_user,Integer start,Integer limit,String page_mark);
    
    /** 查询需要批量发送的组合模板*/
    List<BatchFeishuExcelTaskVo> getBatchTemplete();
    
    /** 通过组合模本id查找组合模本信息及子模板内容*/
    BatchFeishuExcelTaskVo getBatchTepleteById(int bid);
    
    void updateBatchTemplete(BatchFeishuExcelTaskVo vo);
    void deleteBatchExcelByBid(int bid);
}
