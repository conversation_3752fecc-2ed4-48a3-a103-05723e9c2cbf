package com.wbgame.service.game;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.CustomerServiceDTO;
import com.wbgame.pojo.game.userinfo.FeedbackVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 用户反馈--海外
 * @Date 2024/8/22 11:33
 */
public interface HwUserFeedbackInfoService {

    /**
     * 根据条件查询海外 玩家反馈信息数据
     * @param dto 查询条件
     * @return 查询结果
     */
    Result<List<FeedbackVo>> selectFeedbackList(CustomerServiceDTO dto);

    /**
     * 根据指定id删除玩家反馈信息数据
     * @param id 主键id
     * @return 删除结果
     */
    Result<Integer> delFeedback(String id);

    /**
     * 根据指定id更新玩家反馈数据状态操作
     * @param id 玩家反馈id
     * @param username 当前操作人
     * @return 处理结果
     */
    Result<Integer> updateFeedback(CustomerServiceDTO dto);

    /**
     * 导出海外玩家反馈数据
     * @param response HttpServletResponse
     * @param dto 导出查询参数
     */
    void exportHwFeedBackList(HttpServletResponse response, CustomerServiceDTO dto);
}
