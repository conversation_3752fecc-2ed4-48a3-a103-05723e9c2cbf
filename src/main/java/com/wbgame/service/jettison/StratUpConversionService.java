package com.wbgame.service.jettison;


import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.jettison.param.StratUpConversionParam;
import com.wbgame.pojo.jettison.vo.StratUpConversionVo;
import com.wbgame.utils.PageResult;

public interface StratUpConversionService {
	public Result<PageResult<StratUpConversionVo>>  list( StratUpConversionParam param) ;
	
	Result<String> export(StratUpConversionParam param, HttpServletResponse response);
	
	public Result<Map<String,List<StratUpConversionVo>>>  chart( StratUpConversionParam param) ;
}
