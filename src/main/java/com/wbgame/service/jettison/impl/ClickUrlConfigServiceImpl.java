package com.wbgame.service.jettison.impl;

import com.wbgame.mapper.tfxt.ClickUrlConfigMapper;
import com.wbgame.pojo.jettison.param.ClickUrlConfigParam;
import com.wbgame.pojo.jettison.vo.ClickUrlConfigVo;
import com.wbgame.pojo.jettison.vo.GameNameVo;
import com.wbgame.service.jettison.ClickUrlConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/17 16:55
 */
@Service
public class ClickUrlConfigServiceImpl implements ClickUrlConfigService {

    @Resource
    private ClickUrlConfigMapper clickUrlConfigMapper;

    @Override
    public List<ClickUrlConfigVo> selectClickUrlConfig(ClickUrlConfigParam param) {
        return clickUrlConfigMapper.selectClickUrlConfig(param);
    }

    @Override
    public void add(ClickUrlConfigVo param) {
        clickUrlConfigMapper.add(param);
    }

    @Override
    public void update(ClickUrlConfigVo param) {
        clickUrlConfigMapper.update(param);
    }

    @Override
    public void delete(List<Integer> ids) {
        clickUrlConfigMapper.delete(ids);
    }

    @Override
    public void batchAdd(List<ClickUrlConfigVo> list) {
        clickUrlConfigMapper.batchAdd(list);
    }

    @Override
    public ClickUrlConfigVo getClickUrlConfig(ClickUrlConfigParam param) {
        return clickUrlConfigMapper.getClickUrlConfig(param);
    }

    @Override
    public List<GameNameVo> getGameNames(GameNameVo param) {
        return clickUrlConfigMapper.getGameNames(param);
    }
}
