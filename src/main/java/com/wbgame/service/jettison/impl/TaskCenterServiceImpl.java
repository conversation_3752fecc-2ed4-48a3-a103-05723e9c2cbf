package com.wbgame.service.jettison.impl;


import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wbgame.mapper.master.jettison.TaskCenterMapper;
import com.wbgame.pojo.jettison.TaskCenterDto;
import com.wbgame.pojo.jettison.vo.TaskDetailVo;
import com.wbgame.service.jettison.TaskCenterService;
/**
 * desc:自动投放功能之任务中心service
 * createBy:xugx
 * date：2022-11-29
 */
@Service
public class TaskCenterServiceImpl implements TaskCenterService {
	@Autowired
	private TaskCenterMapper taskCenterMapper;

	@Override
	public List<TaskCenterDto> taskList(TaskCenterDto vo) throws Exception {
		return taskCenterMapper.taskList(vo);
	}

	@Override
	public List<TaskDetailVo> getDetail(int batchId) throws Exception {
		return taskCenterMapper.getDetail(batchId);
	}
}
