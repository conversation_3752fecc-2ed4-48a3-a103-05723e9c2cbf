package com.wbgame.service.jettison;

import com.wbgame.pojo.jettison.vo.TfChannelVo;

import java.util.List;
import java.util.Map;

public interface DataSourceService {

    /**
     * 查询投放账号集合
     * @param account
     * @return
     */
    List<Map<String, Object>> getTfAccounts(String account);

    /**
     * 查询投放子渠道
     * @return
     */
    List<TfChannelVo> getTfChannels();

    /**
     * 按媒体查询账号集合
     * @param media
     * @return
     */
    List<Map<String, Object>> getTfAccountListByMedia(String media,String putUser,String groupId);
}
