package com.wbgame.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.wbgame.pojo.CityVo;
import com.wbgame.pojo.MoneyMsgVo;
import com.wbgame.pojo.RewardInfoVo;
import com.wbgame.pojo.SysConfigVo;
import com.wbgame.pojo.UserInfoVo;
import com.wbgame.pojo.WordVo;
import com.wbgame.pojo.WxIconPushVo;
import com.wbgame.pojo.WxSharePushVo;
import com.wbgame.pojo.WxVideoPushVo;
import com.wbgame.pojo.XyxIconPushVo;

public interface WorldService {
	
	// 查询系统配置
	SysConfigVo selectSysConfigVo(Map<String, Object> map);
	
    // 用户，奖励
    UserInfoVo selectUserInfoVo(String wx_id);
    int insertUserInfoVo(UserInfoVo user);
    int insertUserInfoVoByAttr(Map<String, Object> map);
    int updateInitUserInfoVo();
    int insertRewardInfoVo(RewardInfoVo ri);
    
    @Cached(name="WorldService.wordInfo", expire = 3600, cacheType = CacheType.LOCAL)
    List<WordVo> selectWordInfo();
    @Cached(name="WorldService.wordNum", expire = 3600, cacheType = CacheType.LOCAL)
    int selectWordNum();
    
    @Cached(name="WorldService.getProvinceList", cacheType = CacheType.LOCAL)
    public List<String> getProvinceList();
    @Cached(name="WorldService.getCityList", cacheType = CacheType.LOCAL)
    public List<String> getCityList();
    
    @Cached(name="WorldService.selectWxPushFilter", cacheType = CacheType.LOCAL)
    public Map<String, Map<String, Object>> selectWxPushFilter();
    @CacheInvalidate(name="WorldService.selectWxPushFilter")
    public String invalidateWxPushFilter();
    
    // 微信互推配置
    @Cached(name="WorldService.selectWxIconPushByAppid.", cacheType = CacheType.LOCAL)
    List<WxIconPushVo> selectWxIconPushByAppid(int appid);
    @CacheInvalidate(name="WorldService.selectWxIconPushByAppid.")
    String invalidateWxIconPushByAppid(int appid);
    
    @Cached(name="WorldService.selectWxLinkPushAll", cacheType = CacheType.LOCAL)
    Map<String, Map<String, Object>> selectWxLinkPushAll();
    @CacheInvalidate(name="WorldService.selectWxLinkPushAll")
    String invalidateWxLinkPushAll();
    
    @CacheRefresh(refresh = 180, stopRefreshAfterLastAccess = 1200, timeUnit = TimeUnit.SECONDS)
    @Cached(name="WorldService.selectXyxIconPushByAttr", cacheType = CacheType.LOCAL)
    List<XyxIconPushVo> selectXyxIconPushByAttr(Map<String, Object> map);
    
    @CacheRefresh(refresh = 180, stopRefreshAfterLastAccess = 1200, timeUnit = TimeUnit.SECONDS)
    @Cached(name="WorldService.selectWxCustomMessageMap", cacheType = CacheType.LOCAL)
    Map<String, Object> selectWxCustomMessageMap();
    
    
    // 微信App配置信息
    Map<String, Map<String, Object>> selectWxAppConfigMap();
    
    // 小程序分享信息
    @Cached(name="WorldService.selectWxSharePushAll", cacheType = CacheType.LOCAL)
    List<WxSharePushVo> selectWxSharePushAll();
    @CacheInvalidate(name="WorldService.selectWxSharePushAll")
    String invalidateWxSharePushAll();
    
    // 获取图标ID对应的图标-类型和名称
 	Map<String, Map<String, Object>> selectWxIconGnameAll();
 	
 	List<WxVideoPushVo> selectWxVideoPushAll(Map<String, Object> map);
 	
 	// 微信奖金池基础配置信息
 	Map<String, Map<String, Object>> selectWxBonusConfig();
 	// 微信助力红包基础配置信息
 	Map<String, Map<String, Object>> selectWxHelpConfig();
 	// 飞刀手基础配置信息
 	Map<String, Map<String, Object>> selectWxFdsConfig();
 	// 微信小程序内功能开关
  	Map<String, Map<String, Object>> selectWxPowerSwitch();
  	Map<String, Map<String, Object>> selectWxPageSwitch();
  	// 微信小程序内模板消息内容
  	Map<String, Map<String, Object>> selectWxCustomTemplate();
  	// 公众号发送红包的参数
  	Map<String, Map<String, String>> selectWxRedPackConfig();
  	// 小程序支付的参数配置
  	Map<String, Map<String, String>> selectWxMpPayConfig();
  	
 	// 奖金提现记录列表
 	List<MoneyMsgVo> selectWxMoneyMsgList();
 	//数据校验百分比
 	Double selectAdMsgPer();
 	
 	// 广告互推屏蔽配置
 	@Cached(name="WorldService.getPushAdconfigConfig", cacheType = CacheType.LOCAL)
    Map<String, Map<String, Object>> getPushAdconfigConfig();
    @CacheInvalidate(name="WorldService.getPushAdconfigConfig")
    String invalidatePushAdconfigConfig();
    
    String[] getGeoIpInfo(String reqip);

    //接口版本配置
    @Cached(name="WorldService.getInterfaceVersionConfig", cacheType = CacheType.LOCAL)
    Map<String, Map<String, Object>> getInterfaceVersionConfig();

    @CacheInvalidate(name="WorldService.getInterfaceVersionConfig")
    String invalidateInterfaceVersionConfig();
}
