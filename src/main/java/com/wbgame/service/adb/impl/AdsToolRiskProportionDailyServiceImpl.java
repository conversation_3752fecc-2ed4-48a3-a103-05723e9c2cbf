package com.wbgame.service.adb.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.AdsToolRiskProportionDailyMapper;
import com.wbgame.pojo.clean.AdsToolRiskProportionDailyDTO;
import com.wbgame.pojo.clean.AdsToolRiskProportionDailyVO;
import com.wbgame.service.adb.IAdsToolRiskProportionDailyService;
import com.wbgame.utils.CamelCaseUtils;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/28 028
 * @class: AdsToolRiskProportionDailyServiceImpl
 * @description:
 */
@Service
public class AdsToolRiskProportionDailyServiceImpl implements IAdsToolRiskProportionDailyService {

    @Autowired
    private AdsToolRiskProportionDailyMapper toolRiskProportionDailyMapper;

    @Override
    public Result<PageResult<AdsToolRiskProportionDailyVO>> selectAdsToolPopCreateDaily(AdsToolRiskProportionDailyDTO example) {

        example.setGroup(CamelCaseUtils.humpToUnderline(example.getGroup()));
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));

        PageHelper.startPage(example.getStart(), example.getLimit());
        List<AdsToolRiskProportionDailyVO> list = toolRiskProportionDailyMapper.selectRiskProportionDaily(example);

        for (AdsToolRiskProportionDailyVO vo : list) {

            vo.setProportion(vo.getProportion() + "%");
        }

        return ResultUtils.success(PageResult.page(list), toolRiskProportionDailyMapper.countRiskProportionDaily(example));
    }

    @Override
    public List<AdsToolRiskProportionDailyVO> export(AdsToolRiskProportionDailyDTO example) {

        example.setGroup(CamelCaseUtils.humpToUnderline(example.getGroup()));
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));

        return toolRiskProportionDailyMapper.selectRiskProportionDaily(example);
    }
}
