package com.wbgame.service.adb.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.AdsToolPopCreateDailyMapper;
import com.wbgame.pojo.clean.AdsToolPopCreateDailyDTO;
import com.wbgame.pojo.clean.AdsToolPopCreateDailyVO;
import com.wbgame.service.adb.IAdsToolPopCreateDailyService;
import com.wbgame.utils.CamelCaseUtils;
import com.wbgame.utils.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/26 026
 * @class: AdsToolPopCreateDailyServiceImpl
 * @description:
 */
@Service
public class AdsToolPopCreateDailyServiceImpl implements IAdsToolPopCreateDailyService {

    @Autowired
    private AdsToolPopCreateDailyMapper toolPopCreateDailyMapper;

    @Override
    public Result<PageResult<AdsToolPopCreateDailyVO>> selectAdsToolPopCreateDaily(AdsToolPopCreateDailyDTO example) {

        String group = CamelCaseUtils.humpToUnderline(example.getGroup());
        example.setGroup(group);
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));

        // 拼接条件
        if (!StringUtils.isBlank(group)) {

//            example.setGroup_b(group.replaceAll(",scenes", ""));

            // group_b : a a1,b b1,c c1
            StringBuilder group_b = new StringBuilder();
            StringBuilder linkSB = new StringBuilder();

            // [cln1, cln2, cln3]
            String[] groupArr = group.split(",");
//            example.setGroupByB(group.replaceAll(",scenes", ""));

            for (int i = 0; i < groupArr.length; i++) {

                String cln = groupArr[i];
                group_b.append(cln).append(" ").append(cln).append(1).append(","); // c c1,

                linkSB.append("b.").append(cln).append(1); // b.c1
                if (i == groupArr.length - 1) {

                    continue;
                }
                linkSB.append(",");

            }
            String groupBStr = group_b.toString();
            example.setGroup_b(groupBStr);

            // associateCondition： a b临时表on 关联条件
            StringBuilder associateConditionSb = new StringBuilder();
            String[] split = linkSB.toString().split(",");
            for (int i = 0; i < split.length; i++) {

                // 拼接 on 连接条件 a.cln=b.cln
                // 和groupArr 每个索引的值结尾多了 1 : cln  cln1

                associateConditionSb.append("and ").append(" a.").append(groupArr[i]).append("=").append(split[i]).append(" ");
            }
            example.setAssociateCondition(associateConditionSb.toString());

        }


        PageHelper.startPage(example.getStart(), example.getLimit());
        List<AdsToolPopCreateDailyVO> list = toolPopCreateDailyMapper.selectByAdsToolPopCreateDaily(example);

        for (AdsToolPopCreateDailyVO vo : list) {

            vo.setCreatingProportion(vo.getCreatingProportion() + "%");
            vo.setCreateProportion(vo.getCreateProportion() + "%");
//            vo.setCreatingPerCapitaPv(vo.getCreatingPerCapitaPv() + "%");
//            vo.setCreatePerCapitaPv(vo.getCreatePerCapitaPv() + "%");
            vo.setCreationSuccessRate(vo.getCreationSuccessRate() + "%");
        }
        return ResultUtils.success(PageResult.page(list), toolPopCreateDailyMapper.countByAdsToolPopCreateDaily(example));
    }

    @Override
    public List<AdsToolPopCreateDailyVO> export(AdsToolPopCreateDailyDTO example) {

        example.setGroup(CamelCaseUtils.humpToUnderline(example.getGroup()));
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));
        List<AdsToolPopCreateDailyVO> list = toolPopCreateDailyMapper.selectByAdsToolPopCreateDaily(example);

        for (AdsToolPopCreateDailyVO vo : list) {

            vo.setCreatingProportion(vo.getCreatingProportion() + "%");
            vo.setCreateProportion(vo.getCreateProportion() + "%");
//            vo.setCreatingPerCapitaPv(vo.getCreatingPerCapitaPv() + "%");
//            vo.setCreatePerCapitaPv(vo.getCreatePerCapitaPv() + "%");
            vo.setCreationSuccessRate(vo.getCreationSuccessRate() + "%");
        }
        return list;
    }
}
