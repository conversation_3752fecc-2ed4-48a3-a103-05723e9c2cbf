package com.wbgame.service.adv2.module;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.controller.adv2.CSJBatchAdCodeController;
import com.wbgame.pojo.adv2.CSJAdcodeVo;
import com.wbgame.pojo.adv2.OppoAdVo;
import com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.CommonAdcodeModuleVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wbgame.controller.adv2.AdCodeAppConfigController.CSJ_PLATFORM;
import static com.wbgame.controller.adv2.AdCodeAppConfigController.OPPO_PLATFORM;

/**
 * <AUTHOR>
 * @date 2023/11/10
 * @description csj 模板service
 **/
@Service
public class CsjModuleService extends BaseModuleService {

    @Resource
    private com.wbgame.controller.adv2.CSJBatchAdCodeController CSJBatchAdCodeController;
    @Override
    public void createModule(Map<String, Object> map) {
        CSJAdcodeModuleVo vo = new CSJAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.insertCSJModule(vo);
    }

    @Override
    public List<Map<String, Object>> findModule() {
        return advCommonAdCodeMapper.selectCSJModule();
    }

    @Override
    public void modifyModule(Map<String, Object> map) {
        CSJAdcodeModuleVo vo = new CSJAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.updateCSJModule(vo);
    }

    @Override
    public int deleteModule(int id) {
        return advCommonAdCodeMapper.deleteCSJModule(id);
    }

    @Override
    public List<Map<String, Object>> selectModuleById(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return advCommonAdCodeMapper.selectCSJModule();
        }
        return advCommonAdCodeMapper.selectBatchCSJModule(ids);
    }

    @Override
    public List<?> buildVo(JSONObject vo, String appid, String channel, String appName, String adExtensionName,String groupName) {
        CSJAdcodeModuleVo csjAdcodeModuleVo = null;
        ArrayList<JSONObject> resList = new ArrayList<>();
        try {
            csjAdcodeModuleVo = CSJAdcodeModuleVo.mapToObj(vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<CSJAdcodeVo> csjAdcodeVos = csjAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,groupName);
        return csjAdcodeVos;
    }

    @Override
    public List<JSONObject> handleAdcode(JSONObject vo, String userName, HttpServletRequest request, String appid,
                                         String channel, String appName, String adExtensionName) {
        CSJAdcodeModuleVo csjAdcodeModuleVo = null;
        ArrayList<JSONObject> resList = new ArrayList<>();
        try {
            csjAdcodeModuleVo = CSJAdcodeModuleVo.mapToObj(vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<CSJAdcodeVo> csjAdcodeVos = csjAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,null);
        String moduleName = csjAdcodeModuleVo.getModule_name();
        csjAdcodeVos.forEach(x -> {
            Object res = CSJBatchAdCodeController.handleAdcdoe(request, x);
            JSONObject json = (JSONObject) res;
            if (json.getIntValue("ret") == 0) {
                json.put("error", CSJ_PLATFORM + moduleName + x.getAdExtensionName());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public List<JSONObject> handleAdcode(String userName, HttpServletRequest request, List<?> vos) {
        List<CSJAdcodeVo> csjAdcodeVos = (List<CSJAdcodeVo>) vos;
        ArrayList<JSONObject> resList = new ArrayList<>();
        csjAdcodeVos.forEach(x -> {
            Object res = CSJBatchAdCodeController.handleAdcdoe(request, x);
            JSONObject json = (JSONObject) res;
            if (json.getIntValue("ret") == 0) {
                json.put("error", CSJ_PLATFORM + x.getAd_slot_name());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public JSONObject createPrepareDetect(List<?> vos) {
        List<CSJAdcodeVo> csjAdVos = (List<CSJAdcodeVo>) vos;
        ArrayList<AdsidField> adsidFields = new ArrayList<>();
        for (CSJAdcodeVo vo : csjAdVos) {
            AdsidField adsidField = new AdsidField(CSJ_PLATFORM, vo.getSdk_ad_type(), vo.getAppid(), vo.getAdExtensionName(), vo.getAd_slot_name());
            adsidFields.add(adsidField);
        }
        return commonPrepareDetect(adsidFields);
    }
}
