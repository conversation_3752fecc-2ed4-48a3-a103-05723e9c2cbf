package com.wbgame.service.budgetWarning.tableImpl;

import com.wbgame.common.strategyEnum.Dimension;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.pojo.jettison.vo.BudgetWarningIndexVo;
import com.wbgame.service.budgetWarning.BaseTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/1
 * @description 消耗细分表的查询 实现类
 **/
@Service
@Slf4j
public class SpendTableService extends BaseTableService {

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;
    @Override
    public List<Map<String, Object>> aggregateBySql(String field, String conditionSql, String groupBy, Map<String, BudgetWarningIndexVo> indexMap) {
        String select = selectDataProcess(field, indexMap);
        Dimension of = Dimension.of(groupBy);
        groupBy = selectAggProcess(groupBy);
        String selectGroupBy = null;
//        // 重命名为 appid 为了跟roi表匹配
        if (of.equals(Dimension.APPID)) {
            selectGroupBy = "app appid";
        } else if (of.equals(Dimension.ACCOUNT)) {
            selectGroupBy = "app appid, account";
        } else if (of.equals(Dimension.CAMPAIGN_NAME)) {
            selectGroupBy = "app appid, account, campaign campaign_name";
        } else if (of.equals(Dimension.CAMPAIGN_ID)) {
            selectGroupBy = "app appid, account, campaign campaign_name, campaignId campaign_id";
        } else if (of.equals(Dimension.GROUP_NAME)) {
            selectGroupBy = "app appid, account, groupName group_name";
        } else if (of.equals(Dimension.planId)) {
            selectGroupBy = "app appid, account, planId, planName";
        }

        if (conditionSql.contains("appid")) {
            conditionSql = conditionSql.replace("appid", "app");
        }
        groupBy = groupBy.replace("appid", "app");
        groupBy = groupBy.replace("campaign_name", "campaignId");
        groupBy = groupBy.replace("group_name", "groupName");

        String sql = "select " + selectGroupBy + ", " + select +
                " from dnwx_adt.dn_report_spend_china a " +
                " left join dnwx_adt.dn_report_spend_oppo_realtime b on a.`day`=b.tdate and a.campaignId=b.campaign_id " +
                " left join dnwx_adt.oppo_group_info c on a.groupId=c.adGroupId "
                + conditionSql + groupBy;
        log.info("最终查询sql: {}", sql);
        return dnwxBiAdtMapper.queryListMap(sql);
    }
}
