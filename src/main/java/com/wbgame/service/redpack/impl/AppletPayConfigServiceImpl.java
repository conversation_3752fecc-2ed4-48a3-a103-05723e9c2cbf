package com.wbgame.service.redpack.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.master.AppInfoByCategoryMapper;
import com.wbgame.mapper.redpack.AppletPayConfigMapper;
import com.wbgame.pojo.operate.TtAppletPayConfig;
import com.wbgame.pojo.operate.TtAppletPayConfigDTO;
import com.wbgame.service.redpack.AppletPayConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: xiaoxh
 * @createDate: 2024/08/05 17:40
 * @class: AppletPayConfigServiceImpl
 * @description: 抖音小程序支付配置
 */
@Service
public class AppletPayConfigServiceImpl implements AppletPayConfigService {

    @Autowired
    private AppletPayConfigMapper appletPayConfigMapper;
    @Autowired
    private AppInfoByCategoryMapper appInfoByCategoryMapper;


    /**
     * 根据appid集合删除 抖音小程序支付配置 数据
     * @param appids appis
     * @return 删除结果，删除成功个数
     */
    @Override
    public Result<Integer> deleteAppletPayConfig(List<String> appids) {
        return CollectionUtils.isEmpty(appids) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(appletPayConfigMapper.deleteByAppids(appids));
    }

    /**
     * 新增数据至 抖音小程序支付配置表中
     * @param config 新增数据
     * @return 新增个数结果
     */
    @Override
    public Result<Integer> insertAppletPayConfig(TtAppletPayConfig config) {
        if (!ObjectUtils.isEmpty(appletPayConfigMapper.selectExist(config.getAppid()))) {
            return ResultUtils.failure("该应用已配置");
        }
        appletPayConfigMapper.insertAppletPayConfig(config);
        return ResultUtils.success();
    }


    /**
     * 查询 抖音小程序支付配置 表数据
     * @param example 查询条件
     * @return 查询结果
     */
    @Override
    public Result<PageResult<TtAppletPayConfig>> selectAppletPayConfig(TtAppletPayConfigDTO example) {
        //封装应用类型
        List<Integer> appTypeList = example.getAppTypeList();
        if (CollectionUtils.isEmpty(example.getAppidList()) && !CollectionUtils.isEmpty(appTypeList)) {
            List<String> categoryAppidList = appInfoByCategoryMapper.getAppidList(appTypeList);
            example.setAppidList(categoryAppidList);
        }
        PageHelper.startPage(example.getStart(), example.getLimit());
        List<TtAppletPayConfig> resultList = appletPayConfigMapper.selectAppletPayConfig(example);
        return ResultUtils.success(PageResult.page(resultList));
    }


    /**
     * 根据id 更新 抖音小程序支付配置 数据
     * @param record 更新的数据
     * @return 更新结果
     */
    @Override
    public Result<Integer> updateAppletPayConfig(TtAppletPayConfig record) {
        appletPayConfigMapper.updateAppletPayConfig(record);
        return ResultUtils.success();
    }
}
