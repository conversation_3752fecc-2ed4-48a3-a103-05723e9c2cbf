package com.wbgame.service.impl;

import com.google.common.collect.Lists;
import com.wbgame.base.BaseErrorEnum;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.adb.UmengMonitorMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.PlatformAdDataRequestParam;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.advert.UmengOverseaReportVo;
import com.wbgame.pojo.advert.oversea.BasePageResultResponse;
import com.wbgame.pojo.advert.oversea.BaseResult;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.service.AdService;
import com.wbgame.service.PartnerService;
import com.wbgame.service.UmengOverseaService;
import com.wbgame.service.game.AppService;
import com.wbgame.utils.*;
import com.wbgame.utils.compare.CommonCompareUtils;
import com.wbgame.utils.compare.GapCompareUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/24 14:29
 */
@Service
@Slf4j
public class UmengOverseaServiceImpl implements UmengOverseaService {


    /**
     * 需要替换的变化率字段
     */
    private final static String DEFINED_RATE_FIELDS = "dau_arpu,native_banner_ecpm,banner_ecpm,plaque_video_ecpm,native_new_plaque_ecpm,native_msg_ecpm,native_splash_ecpm,suspend_icon_ecpm,splash_ecpm,system_splash_ecpm,native_plaque_ecpm,video_ecpm,plaque_ecpm,native_new_banner_ecpm,total_banner_pv,total_splash_pv,total_video_pv,total_plaque_pv,total_suspend_icon_pv,total_plaque_video_pv,total_native_msg_pv,avgnum,daily_duration,total_banner_click,total_splash_click,total_video_click,total_plaque_click,total_suspend_icon_click,total_plaque_video_click,total_native_msg_click,all_total_pv,all_total_click,total_banner_arpu,total_splash_arpu,total_video_arpu,total_native_msg_arpu,total_plaque_arpu,total_plaque_video_arpu,total_suspend_icon_arpu,all_total_ctr,all_total_request," +
            "total_splash_request,total_plaque_request,total_plaque_video_request,total_banner_request,total_video_request,total_native_msg_request,total_suspend_icon_request,all_total_avg_fill,total_splash_avg_fill,total_plaque_avg_fill,total_plaque_video_avg_fill,total_banner_avg_fill,total_video_avg_fill,total_native_msg_avg_fill,total_suspend_icon_avg_fill,all_total_fill,total_system_splash_fill,total_splash_fill,total_native_splash_fill,total_plaque_fill,total_native_new_plaque_fill,total_plaque_video_fill,total_banner_fill,total_native_new_banner_fill,total_video_fill,total_native_msg_fill,total_suspend_icon_fill,plaque_video_cpc,native_msg_cpc,native_new_banner_cpc,banner_cpc,native_new_plaque_cpc,video_cpc,native_splash_cpc,plaque_cpc,native_plaque_cpc,native_banner_cpc,suspend_icon_cpc,splash_cpc";


    @Autowired
    private AdService adService;

    @Autowired
    private YyhzMapper yyhzMapper;

    @Autowired
    private UmengMonitorMapper umengMonitorMapper;

    @Autowired
    private AppService appService;

    @Autowired
    private PartnerService partnerService;


    @Override
    public BaseResult<?> selectPlatformAdDataList(PlatformAdDataRequestParam param) {

        // 判断环比同比
        if (StringUtils.isEmpty(param.getDimension())) {
            BaseResult result = new BaseResult();
            result.setRet(BaseErrorEnum.FAIL.getCode());
            result.setMsg("请选择环比同比");
            return result;
        }
        // 处理页码
        int pageNo = (param.getStart() / param.getLimit()) + 1;
        // 处理表名
        String tableName = "dnwx_bi.umeng_ad_income_oversea";
        if ("2".equals(param.getDataSource())) {
            tableName = "oppo_ad_income_oversea";
            BaseResult result = new BaseResult();
            result.setRet(BaseErrorEnum.SUCCESS.getCode());
            result.setMsg(BaseErrorEnum.SUCCESS.getMsg());
            result.setTotal(0);
            return result;
        }
        param.setTableName(tableName);

        // 处理排序字段
        String order_str = DataTransUtils.generateOrderStrParam("b", param.getOrder_str());
        param.setOrder_str(order_str);

        // 处理维度字段
        String group = generateGroupParam(param.getGroup());
        param.setGroup(group);

        // 处理match_str
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if (StringUtils.isNotEmpty(param.getPrjid_group_id())) {
            Map<String, String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", param.getPrjid_group_id());

            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);
            if (groupList != null && !groupList.isEmpty()) {

                String collect = groupList.stream().filter(act -> StringUtils.isNotEmpty(act.getOrDefault("prjid", "").toString()))
                        .map(act -> act.getOrDefault("prjid", "").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> StringUtils.isNotEmpty(act.getOrDefault("chastr", "").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr", "").toString()).split(",")))
                        .map(str -> "'" + str + "'")
                        .collect(Collectors.joining(","));

                String match_str = null;
                if (StringUtils.isNotEmpty(collect)) {
                    // 项目id的集合时，需要转换为appid#cha_id
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    for (String prjid : collect.split(",")) {
                        Map<String, Object> act = prjMap.get(prjid);
                        if (null != act) {
                            String match_appid_chaid = act.get("appid") + "#" + act.get("cha_id");

                            if (StringUtils.isEmpty(collect2)) {
                                collect2 = "'" + match_appid_chaid + "'";
                            } else {
                                collect2 += ",'" + match_appid_chaid + "'";
                            }
                        }
                    }
                }
                if (StringUtils.isNotEmpty(collect2)) {
                    match_str = String.format(" concat(c.appid,'#',c.channel) in (%s) ", collect2);
                }
                param.setMatch_str("(" + match_str + ")");
            }
        }
        // 处理汇总数据
        UmengOverseaReportVo total = null;
        if (StringUtils.isEmpty(param.getCustom_date())) {
            //自定义时间段汇总不进行汇总数据查询
            if ("2".equals(param.getDataSource())) {
                //total = integratedMapper.selectPlatformAdDataFromOppoSum(param);
                total = new UmengOverseaReportVo();
            } else {
                total = umengMonitorMapper.getUmengAdIncomeOverseaSum(param);
            }
        }
        if (total != null) {
            total.setAvgnum(total.getAvgnum());
            total.setDaily_duration(com.wbgame.utils.StringUtils.secondsToHHmmss(total.getDaily_duration()));
        }
        List<UmengOverseaReportVo> fList = getUmengAdIncomeReportVos(param.getStart_date(), param.getEnd_date(), group, param.getIs_x(), param.getDimension(), param);
        PageForList<UmengOverseaReportVo> pager = new PageForList<>(pageNo, param.getLimit(), fList);
        // 返回值处理
        BaseResult<BasePageResultResponse<?>> result = new BaseResult<>();
        result.setRet(BaseErrorEnum.SUCCESS.getCode());
        result.setMsg(BaseErrorEnum.SUCCESS.getMsg());

        BasePageResultResponse<?> response = new BasePageResultResponse<>();
        response.setList(pager.getResultList());
        response.setTotalSize((long) pager.getTotalRows());
        result.setData(response);

        result.setTotal(total);
        return result;
    }

    @Override
    public void exportPlatformAdDataList(HttpServletResponse response, PlatformAdDataRequestParam param) {
        // 处理表名
        String tableName = "dnwx_bi.umeng_ad_income_oversea";
        if ("2".equals(param.getDataSource())) {
            tableName = "dnwx_bi.oppo_ad_income";
        }
        param.setTableName(tableName);

        // 处理排序字段
        String order_str = DataTransUtils.generateOrderStrParam("b", param.getOrder_str());
        param.setOrder_str(order_str);

        // 处理维度字段
        String group = generateGroupParam(param.getGroup());
        param.setGroup(group);

        // 处理match_str
        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if (StringUtils.isNotEmpty(param.getPrjid_group_id())) {
            Map<String, String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", param.getPrjid_group_id());

            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);
            if (groupList != null && !groupList.isEmpty()) {

                String collect = groupList.stream().filter(act -> StringUtils.isNotEmpty(act.getOrDefault("prjid", "").toString()))
                        .map(act -> act.getOrDefault("prjid", "").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> StringUtils.isNotEmpty(act.getOrDefault("chastr", "").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr", "").toString()).split(",")))
                        .map(str -> "'" + str + "'")
                        .collect(Collectors.joining(","));

                String match_str = null;
                if (StringUtils.isNotEmpty(collect)) {
                    // 项目id的集合时，需要转换为appid#cha_id
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    for (String prjid : collect.split(",")) {
                        Map<String, Object> act = prjMap.get(prjid);
                        if (null != act) {
                            String match_appid_chaid = act.get("appid") + "#" + act.get("cha_id");
                            if (StringUtils.isEmpty(collect2)) {
                                collect2 = "'" + match_appid_chaid + "'";
                            } else {
                                collect2 += ",'" + match_appid_chaid + "'";
                            }
                        }
                    }
                }
                if (StringUtils.isNotEmpty(collect2)) {
                    match_str = String.format(" concat(c.appid,'#',c.channel) in (%s) ", collect2);
                }
                param.setMatch_str("(" + match_str + ")");
            }
        }
        List<UmengOverseaReportVo> list = getUmengAdIncomeReportVos(param.getStart_date(), param.getEnd_date(), group, param.getIs_x(), param.getDimension(), param);
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            log.error("exportPlatformAdDataList error:", e);
            Asserts.fail("自定义列导出异常");
        }
        String fileName = param.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, list, head, fileName);

    }


    /**
     * 组装group参数
     *
     * @param group
     * @return
     */
    private String generateGroupParam(String group) {
        if (StringUtils.isNotEmpty(group)) {
            String[] groups = group.split(",");
            StringBuffer stringBuffer = new StringBuffer();
            for (int i = 0; i < groups.length; i++) {
                if (i != groups.length - 1) {
                    stringBuffer.append("b.").append(groups[i]).append(",");
                } else {
                    stringBuffer.append("b.").append(groups[i]);
                }
            }
            group = stringBuffer.toString();
        }
        return group;
    }

    private List<UmengOverseaReportVo> getUmengAdIncomeReportVos(String start_date, String end_date, String group, String is_x, String dimension, PlatformAdDataRequestParam paramMap) {
        List<UmengOverseaReportVo> fList = new ArrayList<>();
        String customDate = paramMap.getCustom_date();
        if (StringUtils.isEmpty(customDate)) {
            //非自定义时间段
            /* 区分为1-友盟计算数据时，使用adb源查询 */
            if ("2".equals(paramMap.getDataSource())) {
                //fList = integratedMapper.selectPlatformAdDataListFromOppo(paramMap);
                fList = Collections.emptyList();
            } else {
                fList = umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap);
            }
        } else {
            //为自定义时间段 格式： 2024-10-10,2024-10-15;2024-11-03,2024-11-05
            String[] split = customDate.split(";");
            for (String data : split) {
                String[] dateSplit = data.split(",");

                paramMap.setStart_date(dateSplit[0]);
                paramMap.setEnd_date(dateSplit[1]);

                if ("2".equals(paramMap.getDataSource())) {
                    //fList.addAll(integratedMapper.selectPlatformAdDataListFromOppo(paramMap));
                } else {
                    fList.addAll(umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap));
                }
            }
        }
        // 【新增】新增banner比例调整的后台页面
        if ("b.tdate,b.appkey,b.channel".equals(group)) {
            String query = String.format("select IFNULL(CONCAT(tdate,max_appid,max_cha),'1') mapkey,max_adsid from dn_tuning_adconfig_info where tdate BETWEEN '%s' AND '%s' ", start_date, end_date);
            Map<String, Map<String, Object>> adMap = adService.queryListMapOfKey(query);
            fList.forEach(act -> {
                String key = act.getTdate() + act.getAppid() + act.getChannel();
                Map<String, Object> act2 = adMap.get(key);
                if (act2 != null) {
                    String[] split = act2.getOrDefault("max_adsid", "").toString().split("_");
                    if (split.length == 4 && split[1].equals("banner")) {
                        act.setBanner_show_rate("90");
                        act.setNew_banner_show_rate("10");
                    } else {
                        act.setBanner_show_rate("10");
                        act.setNew_banner_show_rate("90");
                    }
                }
                act.setSum_pv_banner(act.getSum_pv_banner() + "%");
                act.setSum_pv_new_banner(act.getSum_pv_new_banner() + "%");
            });
        }
        // 计算指定字段的同步环比，自统计gap值
        compareFieldsDataRate(dimension, paramMap, fList);
        // 临时功能名称Map查询
        Map<String, Map<String, Object>> tempNameMap = umengMonitorMapper.getTempNameForSingleid();

        //查询是否是x模式
        Map<String, String> isXParamMap = new HashMap<>();
        isXParamMap.put("group", paramMap.getGroup());
        isXParamMap.put("start_date", paramMap.getStart_date());
        isXParamMap.put("end_date", paramMap.getEnd_date());
        List<UmengAdIncomeReportVo> isXList = appService.isXList(isXParamMap);
        Map<String, UmengAdIncomeReportVo> xMap = new HashMap<>();
        isXList.forEach(t -> {
            String key = t.getTdate() + "_" + t.getAppid() + "_" + t.getChannel();
            xMap.put(key, t);
        });
        fList.forEach(t -> {
            String tdate = t.getTdate();
            //处理日期格式
            if (!StringUtils.isEmpty(group) && group.contains("tdate")) {
                Date date = DateUtils.to_yyyy_MM_dd(tdate);
                String week = DateUtil.dateToWeek(date);
                t.setTdate(tdate + "(" + week + ")");
            } else if (!StringUtils.isEmpty(group) && group.contains("week")) {
                String[] split = tdate.split("-");
                if (split.length >= 2) {
                    int year = Integer.parseInt(split[0]);
                    int week = Integer.parseInt(split[1]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year, week);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year, week);
                    t.setTdate(year + "年第" + week + "周:" + firstDayOfWeek + "至" + lastDayOfWeek);
                }
            } else if (!StringUtils.isEmpty(group) && group.contains("beek")) {
                String[] split = tdate.split("-");
                if (split.length >= 3) {
                    int year = Integer.parseInt(split[0]);
                    int week1 = Integer.parseInt(split[1]);
                    int week2 = Integer.parseInt(split[2]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year, week1);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year, week2);
                    t.setTdate(year + "年第" + week1 + "-" + week2 + "周:" + firstDayOfWeek + "至" + lastDayOfWeek);
                }
            }
            //处理avgnum
            String avgnum = t.getAvgnum();
            t.setAvgnum(strToTwoPercent(avgnum));
            //处理在线时长
            String dailyDuration = t.getDaily_duration();
            t.setDaily_duration(com.wbgame.utils.StringUtils.secondsToHHmmss(dailyDuration));
            t.setAll_total_ctr(t.getAll_total_ctr() + "%");
            t.setBanner_ctr(t.getBanner_ctr() + "%");
            t.setPlaque_ctr(t.getPlaque_ctr() + "%");
            t.setSplash_ctr(t.getSplash_ctr() + "%");
            t.setVideo_ctr(t.getVideo_ctr() + "%");
            t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
            t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
            t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
            t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
            t.setNative_new_banner_ctr(t.getNative_new_banner_ctr() + "%");
            t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
            t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
            t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");
            //填充率封装百分号
            t.setAll_total_fill(t.getAll_total_fill() + "%");
            t.setTotal_system_splash_fill(t.getTotal_system_splash_fill() + "%");
            t.setTotal_splash_fill(t.getTotal_splash_fill() + "%");
            t.setTotal_native_splash_fill(t.getTotal_native_splash_fill() + "%");
            t.setTotal_plaque_fill(t.getTotal_plaque_fill() + "%");
            t.setTotal_native_new_plaque_fill(t.getTotal_native_new_plaque_fill() + "%");
            t.setTotal_plaque_video_fill(t.getTotal_plaque_video_fill() + "%");
            t.setTotal_banner_fill(t.getTotal_banner_fill() + "%");
            t.setTotal_native_new_banner_fill(t.getTotal_native_new_banner_fill() + "%");
            t.setTotal_video_fill(t.getTotal_video_fill() + "%");
            t.setTotal_native_msg_fill(t.getTotal_native_msg_fill() + "%");
            t.setTotal_suspend_icon_fill(t.getTotal_suspend_icon_fill() + "%");
            //处理是否x模式
            String xappid = t.getAppid();
            String xkey = tdate + "_" + xappid + "_" + t.getChannel();
            UmengAdIncomeReportVo x_vo = xMap.get(xkey);
            if (null != x_vo) {
                t.setIs_x(x_vo.getIs_x());
            } else {
                t.setIs_x("0");
            }

            //功能标识条件搜索需展示搜索的功能名称(单模块)
            if (!StringUtils.isEmpty(paramMap.getTemp_id())) {
                try {
                    String[] tempIds = t.getTemp_id().split("\\|");
                    String[] tempNames = t.getTemp_name().split("\\|");
                    for (int i = 0; i < tempIds.length; i++) {
                        if (tempIds[i].contains(paramMap.getTemp_id())) {
                            t.setTemp_single_name(tempNames[i]);
                        }

                        Map<String, Object> tempObject = tempNameMap.get(paramMap.getTemp_id());
                        if (tempObject != null) {
                            t.setTemp_name(tempObject.get("tempStr").toString());
                        }
                    }
                } catch (Exception e) {
                    log.error("功能名称(单模块)匹配失败:id={},name={}", t.getTemp_id(), t.getTemp_name());
                }
            }
        });
        //  增加国家码解析 -20250325
        if (!CollectionUtils.isEmpty(fList)) {
            Map<String, Map<String, Object>> countryMap = partnerService.selectDimCountryMap();
            for (UmengOverseaReportVo vo : fList) {
                if (!countryMap.containsKey(vo.getCountry())) continue;
                Map<String, Object> map = countryMap.get(vo.getCountry());
                vo.setCountry(map.get("country_name") + "");
            }
        }
        //根据前端筛选条件 是否是x模式进行过滤
        if (!StringUtils.isBlank(is_x)) {
            if ("1".equals(is_x)) {
                fList = fList.stream().filter(f -> "1".equals(f.getIs_x())).collect(Collectors.toList());
            } else {
                fList = fList.stream().filter(f -> !"1".equals(f.getIs_x())).collect(Collectors.toList());
            }
        }
        return fList;
    }


    /**
     * 计算指定字段的同比环比，自统计gap值
     *
     * @param dimension
     * @param paramMap
     * @param fList
     */
    private void compareFieldsDataRate(String dimension, PlatformAdDataRequestParam paramMap, List<UmengOverseaReportVo> fList) {
        if (CollectionUtils.isEmpty(fList)) return;
        //分组group
        String group = paramMap.getGroup();
        //自定义时间段
        String customDate = paramMap.getCustom_date();
        //数据来源 source： 1-媒体，2-自统计
        String source = paramMap.getSource();
        final String DATA_SOURCE_MEDIA = "1";

        if (StringUtils.isEmpty(customDate) && (StringUtils.isEmpty(group) ||
                (!group.contains("tdate") && !group.contains("week")
                        && !group.contains("beek") && !group.contains("month")))) {
            //说明当前无日期维度数据
            return;
        }
        //维度
        String keySet = "appkey,channel";
        if (!group.contains("channel") && !group.contains("appkey")) {
            keySet = "";
        } else if (!group.contains("channel")) {
            keySet = "appkey";
        } else if (!group.contains("appkey")) {
            keySet = "channel";
        }
        boolean oppoDataSourceFlag = "2".equals(paramMap.getDataSource());
        String gapKeySet = keySet;
        //keySet = oppoDataSourceFlag ? keySet : StringUtils.isEmpty(keySet) ? "source" : keySet + ",source";
        keySet = StringUtils.isEmpty(keySet) ? "source" : keySet + ",source";
        String start_date = paramMap.getStart_date();
        String end_date = paramMap.getEnd_date();
        //数据来源是否包含 媒体
        boolean mediaDataSourceFlag = StringUtils.isEmpty(source) || DATA_SOURCE_MEDIA.equals(source);
        if (mediaDataSourceFlag && group.contains("tdate")) {
            List<UmengOverseaReportVo> sList = null;
            //按日维度数据计算同比环比
            paramMap.setSource(DATA_SOURCE_MEDIA);
            if ("week".equals(dimension)) {
                int lastDay = 7;
                String startDate = DateTime.parse(start_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                String endDate = DateTime.parse(end_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                paramMap.setStart_date(startDate);
                paramMap.setEnd_date(endDate);
                if (oppoDataSourceFlag) {
                    //sList = integratedMapper.selectPlatformAdDataListFromOppo(paramMap);
                    sList = Collections.emptyList();
                } else {
                    sList = umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap);
                }
                try {
                    CommonCompareUtils.compare(fList, sList, keySet,
                            DEFINED_RATE_FIELDS, UmengOverseaReportVo::getTdate, lastDay);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            } else if ("yesterday".equals(dimension)) {
                int lastDay = 1;
                String startDate = DateTime.parse(start_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                String endDate = DateTime.parse(end_date, DateTimeFormat.forPattern("yyyy-MM-dd")).minusDays(lastDay).toString("yyyy-MM-dd");
                paramMap.setStart_date(startDate);
                paramMap.setEnd_date(endDate);

                if (oppoDataSourceFlag) {
                    //sList = integratedMapper.selectPlatformAdDataListFromOppo(paramMap);
                    sList = Collections.emptyList();
                } else {
                    sList = umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap);
                }
                try {
                    CommonCompareUtils.compare(fList, sList, keySet,
                            DEFINED_RATE_FIELDS, UmengOverseaReportVo::getTdate, lastDay);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        //获取查询数据去重后的 日期数据
        // 按周，双周，月 计算环比操作，无同比
        if (mediaDataSourceFlag && (group.contains("week") || group.contains("beek") || group.contains("month"))) {
            List<String> tdateList = fList.stream().map(UmengOverseaReportVo::getTdate).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            //根据周/月 获取对应的起始结束时间
            String startDate = tdateList.get(0);
            String endDate = tdateList.get(tdateList.size() - 1);
            if (group.contains("week")) {
                //week: 2024-44
                startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(startDate, -1, "week"));
                endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(endDate, -1, "week"));
            } else if (group.contains("beek")) {
                //beek: "2024-43-44", 周一 至 周日 为一周
                String start = startDate.substring(0, 7);
                String end = endDate.substring(0, 5) + endDate.substring(8, 10);
                startDate = WeekDateUtils.getWeekStartDate(WeekDateUtils.plusDate(start, -2, "week"));
                endDate = WeekDateUtils.getWeekEndDate(WeekDateUtils.plusDate(end, -2, "week"));
            } else {
                //month: 2024-10
                startDate = WeekDateUtils.getMonthStartDay(startDate, -1);
                endDate = WeekDateUtils.getMonthEndDay(endDate, -1);
            }
            paramMap.setStart_date(startDate);
            paramMap.setEnd_date(endDate);
            paramMap.setSource(DATA_SOURCE_MEDIA);

            List<UmengOverseaReportVo> sList = null;
            if (oppoDataSourceFlag) {
                //sList = integratedMapper.selectPlatformAdDataListFromOppo(paramMap);
                sList = Collections.emptyList();
            } else {
                sList = umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap);
            }
            Function<UmengOverseaReportVo, String> function = reportVo -> {
                if (group.contains("week")) {
                    //week: 2024-44
                    return WeekDateUtils.plusDate(reportVo.getTdate(), -1, "week");
                } else if (group.contains("beek")) {
                    //beek: "2024-43-44"
                    String yearWeek = reportVo.getTdate().substring(0, 7);
                    String previousWeek = WeekDateUtils.plusDate(yearWeek, -2, "week");
                    String lasWeek = WeekDateUtils.plusDate(yearWeek, -1, "week").substring(5, 7);
                    return previousWeek + "-" + lasWeek;
                } else {
                    //month：2024-10
                    return WeekDateUtils.plusDate(reportVo.getTdate(), -1, "month");
                }
            };
            try {
                //当所选维度 keySet 为空时，keyList 集合需要保证为空集合
                List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                CommonCompareUtils.compare(fList, sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), UmengOverseaReportVo::getTdate, function);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        //自定义时间计算环比
        if (mediaDataSourceFlag && !StringUtils.isEmpty(customDate)) {
            // 自定义时间段计算环比操作
            //根据 tdate进行分组操作
            Map<String, List<UmengOverseaReportVo>> flistMap = fList.stream().collect(Collectors.groupingBy(UmengOverseaReportVo::getTdate));
            //对 时间段进行倒序排序操作
            List<String> sortTdateList = flistMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sortTdateList) && sortTdateList.size() >= 2) {
                //只有存在两个时间段以上才进行比较对应的环比操作，时间大的比较时间小的
                for (int i = 0; i < sortTdateList.size() - 1; i++) {
                    List<UmengOverseaReportVo> list = flistMap.get(sortTdateList.get(i)).stream().filter(data -> "媒体".equals(data.getSource())).collect(Collectors.toList());
                    List<UmengOverseaReportVo> sList = flistMap.get(sortTdateList.get(i + 1)).stream().filter(data -> "媒体".equals(data.getSource())).collect(Collectors.toList());
                    try {
                        List<String> keyList = StringUtils.isEmpty(keySet) ? Lists.newArrayList() : Arrays.asList(keySet.split(","));
                        CommonCompareUtils.compare(list, sList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), data -> "custom", data -> "custom");
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        //下面为自统计gap值计算
        if (!DATA_SOURCE_MEDIA.equals(source)) {
            //计算自统计rate
            // 计算自统计的gap值 公式: gap = (自统计-渠道)/自统计
            Map<String, List<UmengOverseaReportVo>> collect = fList.stream().collect(Collectors.groupingBy(umengAdIncomeReport -> StringUtils.isEmpty(umengAdIncomeReport.getSource()) ? "媒体" : umengAdIncomeReport.getSource()));
            //查询的数据不存在自统计，不需要进行gap值计算
            if (!collect.containsKey("自统计")) return;
            //渠道数据获取
            List<UmengOverseaReportVo> mediaList = new ArrayList<>();
            if (mediaDataSourceFlag) {
                mediaList = collect.getOrDefault("媒体", Collections.emptyList());
            } else {
                //封装计算gap值的渠道数据的查询参数
                paramMap.setSource(DATA_SOURCE_MEDIA);
                if (StringUtils.isEmpty(customDate)) {
                    paramMap.setStart_date(start_date);
                    paramMap.setEnd_date(end_date);
                    if (oppoDataSourceFlag) {
                        //mediaList = integratedMapper.selectPlatformAdDataListFromOppo(paramMap);
                        mediaList = Collections.emptyList();
                    } else {
                        mediaList = umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap);
                    }
                } else {
                    //为自定义时间段 格式： 2024-10-10,2024-10-15;2024-11-03,2024-11-05
                    String[] split = customDate.split(";");
                    for (String data : split) {
                        String[] dateSplit = data.split(",");
                        paramMap.setStart_date(dateSplit[0]);
                        paramMap.setEnd_date(dateSplit[1]);
                        if (oppoDataSourceFlag) {
                            //mediaList.addAll(integratedMapper.selectPlatformAdDataListFromOppo(paramMap));
                        } else {
                            mediaList.addAll(umengMonitorMapper.getUmengAdIncomeOverseaList(paramMap));
                        }
                    }
                }
            }
            //自统计数据
            List<UmengOverseaReportVo> selfList = collect.get("自统计");
            try {
                List<String> keyList = StringUtils.isEmpty(gapKeySet) ? Lists.newArrayList() : Arrays.asList(gapKeySet.split(","));
                GapCompareUtils.compare(selfList, mediaList, keyList, Arrays.asList(DEFINED_RATE_FIELDS.split(",")), UmengOverseaReportVo::getTdate, UmengOverseaReportVo::getTdate);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 保留两位小数-百分比
     *
     * @param data
     * @return
     */
    private String strToTwoPercent(String data) {
        try {
            BigDecimal twoPercent = new BigDecimal(data)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.UNNECESSARY);
            data = twoPercent.toString() + "%";
        } catch (Exception e) {

        }
        return data;
    }


}
