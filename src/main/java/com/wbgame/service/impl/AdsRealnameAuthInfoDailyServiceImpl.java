package com.wbgame.service.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.AdsRealnameAuthInfoDailyMapper;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.pojo.AdsRealnameAuthInfoDaily;
import com.wbgame.pojo.AppInfo;
import com.wbgame.service.IAdsRealnameAuthInfoDailyService;
import com.wbgame.utils.CamelCaseUtils;
import com.wbgame.utils.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/12/05 005
 * @class: AdsRealnameAuthInfoDailyServiceImpl
 * @description:
 */
@Service
public class AdsRealnameAuthInfoDailyServiceImpl implements IAdsRealnameAuthInfoDailyService {

    @Autowired
    private AdsRealnameAuthInfoDailyMapper authInfoDailyMapper;

    @Autowired
    private CleanYdMapper cleanYdMapper;

    @Override
    public Result<PageResult<AdsRealnameAuthInfoDaily>> selectAuthInfo(AdsRealnameAuthInfoDaily example) {

        String group = example.getGroup();
        example.setGroup(CamelCaseUtils.humpToUnderline(group));
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));

        PageHelper.startPage(example.getStart(), example.getLimit());

        List<AdsRealnameAuthInfoDaily> list = authInfoDailyMapper.selectAuthInfo(example);
        Map<Integer, AppInfo> appMap = cleanYdMapper.selectAllAppInfoList();
        if (!StringUtils.isBlank(group) && group.contains("appid")) {

            for (AdsRealnameAuthInfoDaily vo : list) {

                AppInfo appInfo = appMap.get(Integer.valueOf(vo.getAppid()));

                if (appInfo != null) {

                    vo.setAppname(appInfo.getAppName());
                }
            }
        }

        for (AdsRealnameAuthInfoDaily vo : list) {

            vo.setPassingRate(vo.getPassingRate() + "%");
            vo.setAuthorRate(vo.getAuthorRate() + "%");
        }

        return ResultUtils.success(PageResult.page(list), authInfoDailyMapper.countAuthInfo(example));
    }

    @Override
    public List<AdsRealnameAuthInfoDaily> export(AdsRealnameAuthInfoDaily example) {

        String group = example.getGroup();
        example.setGroup(CamelCaseUtils.humpToUnderline(group));
        example.setOrder_str(CamelCaseUtils.humpToUnderline(example.getOrder_str()));

        List<AdsRealnameAuthInfoDaily> list = authInfoDailyMapper.selectAuthInfo(example);
        Map<Integer, AppInfo> appMap = cleanYdMapper.selectAllAppInfoList();

        if (!StringUtils.isBlank(group) && group.contains("appid")) {

            for (AdsRealnameAuthInfoDaily vo : list) {

                AppInfo appInfo = appMap.get(Integer.valueOf(vo.getAppid()));

                if (appInfo != null) {

                    vo.setAppname(appInfo.getAppName());
                }
            }
        }

        for (AdsRealnameAuthInfoDaily vo : list) {

            vo.setPassingRate(vo.getPassingRate() + "%");
            vo.setAuthorRate(vo.getAuthorRate() + "%");
        }

        return list;
    }
}
