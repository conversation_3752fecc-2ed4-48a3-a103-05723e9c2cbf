package com.wbgame.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wbgame.common.Asserts;
import com.wbgame.mapper.clean.master.UmengSynLogMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.mobile.BatchUpateMchParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.wbgame.mapper.master.YdMapper;
import com.wbgame.mapper.slave2.YdSlave2Mapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.custom.CsjBaseInfoVo;
import com.wbgame.pojo.custom.CsjBaseLogVo;
import com.wbgame.pojo.mobile.DnwxAllparamConfig;
import com.wbgame.service.YdService;

@Service("ydService")
public class YdServiceImpl implements YdService {

	@Autowired
	private YdMapper ydMapper;
	
	@Autowired
	private TfxtMapper tfxtMapper;
	
	@Autowired
	private YdSlave2Mapper ydSlave2Mapper;

	@Autowired
	private UmengSynLogMapper umengSynLogMapper;

	@Transactional(readOnly=false)
	@Override
	public int addUserMaintain(UserMaintain record) throws DataAccessException{
		return ydMapper.insert(record);
	}

	@Override
	public int editUserMaintain(UserMaintain record) {
		return ydMapper.updateByPrimaryKey(record);
	}

	@Override
	public List<UserMaintain> selectUserMaintain(UserMaintain record) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("loginId", record.getLoginId());
		
		return ydMapper.selectListByParam(map);
	}

	@Override
	public List<RedPackConfigVo> selectRedPack(Map<String, Object> paramMap) {
		return ydMapper.selectRedPack(paramMap);
	}

	@Override
	public List<RedPackConfig> selectNowRedPack(Map<String, Object> paramMap) {
		return ydMapper.selectNowRedPack(paramMap);
	}

	@Override
	public int deleteRedpackAct(Integer pid) {
		return ydSlave2Mapper.deleteRedpackAct(pid);
	}

	@Override
	public int addRedpackAct(RedpackActInfo record) {
		return ydSlave2Mapper.insertRedpackAct(record);
	}

	@Override
	public List<RedpackActInfo> selectRedpackAct(RedpackActInfo record) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("pid", record.getPid());
		return ydSlave2Mapper.selectRedpackAct(map);
	}

	@Override
	public int updateRedpackAct(RedpackActInfo redpackActInfo) {
		return ydSlave2Mapper.updateRedpackAct(redpackActInfo);
	}

	@Override
	public int deleteRedpackConfig(Integer appid) {
		return ydSlave2Mapper.deleteRedpackConfig(appid);
	}

	@Override
	public int addRedpackConfig(RedpackConfigInfo redpackActInfo) {
		return ydSlave2Mapper.insertRedpackConfig(redpackActInfo);
	}

	@Override
	public List<RedpackConfigInfo> selectRedpackConfig(Map<String, Object> map) {
		return ydSlave2Mapper.selectRedpackConfig(map);
	}

	@Override
	public int updateRedpackConfig(RedpackConfigInfo record) {
		return ydSlave2Mapper.updateRedpackConfig(record);
	}

	@Override
	public NOParam selectNOParam() {
		return ydMapper.selectNOParam();
	}

	@Override
	public int updateNOParam(NOParam o) {
		return ydMapper.updateNOParam(o);
	}

	@Override
	public int batchInsertWithdraw(List<DiamondWithdrawInfo> list) {
		return ydSlave2Mapper.batchInsertWithdraw(list);
	}

	@Override
	public int deleteRedpackdrawConfig(RedpackdrawConfigInfo record) {
		return ydSlave2Mapper.deleteRedpackdrawConfig(record);
	}

	@Override
	public int insertRedpackdrawConfig(RedpackdrawConfigInfo record) {
		return ydSlave2Mapper.insertRedpackdrawConfig(record);
	}

	@Override
	public List<RedpackdrawConfigInfo> selectRedpackdrawConfig(
			Map<String, Object> map) {
		return ydSlave2Mapper.selectRedpackdrawConfig(map);
	}

	@Override
	public int updateRedpackdrawConfig(RedpackdrawConfigInfo record) {
		return ydSlave2Mapper.updateRedpackdrawConfig(record);
	}

	@Override
	public List<RedPackConfig> selectWithdrawDetails(Map<String, Object> map) {
		return ydSlave2Mapper.selectWithdrawDetails(map);
	}

	@Override
	public List<DiamondWithdrawInfo> selectRedpackdrawVo(Map<String, Object> map) {
		return ydSlave2Mapper.selectRedpackdrawVo(map);
	}

	@Override
	public int deleteHongBaoInfo(HongBaoInfo record) {
		return ydMapper.deleteHongBaoInfo(record);
	}
	
	@Transactional(readOnly=false)
	@Override
	public int insertHongBaoInfo(HongBaoInfo record)throws DataAccessException {
		return ydMapper.insertHongBaoInfo(record);
	}

	@Override
	public List<HongBaoInfo> selectHongBaoInfo(HongBaoInfo record) {
		return ydMapper.selectHongBaoInfo(record);
	}

	@Override
	public int updateHongBaoInfo(HongBaoInfo record) {
		return ydMapper.updateHongBaoInfo(record);
	}

	@Override
	public int deleteRedpackWxconfig(RedpackWxconfigInfo record) {
		return ydSlave2Mapper.deleteRedpackWxconfig(record);
	}

	@Override
	public int insertRedpackWxconfig(RedpackWxconfigInfo record) throws DataAccessException {
		return ydSlave2Mapper.insertRedpackWxconfig(record);
	}

	@Override
	public List<RedpackWxconfigInfo> selectRedpackWxconfig(RedpackWxconfigInfo record) {
		return ydSlave2Mapper.selectRedpackWxconfig(record);
	}

	@Override
	public int updateRedpackWxconfig(RedpackWxconfigInfo record) {
		return ydSlave2Mapper.updateRedpackWxconfig(record);
	}

	@Override
	public int deleteRusConfigInfo(RusConfigInfo record) {
		return ydMapper.deleteRusConfigInfo(record);
	}

	@Override
	public int insertRusConfigInfo(RusConfigInfo record)throws DataAccessException {
		return ydMapper.insertRusConfigInfo(record);
	}

	@Override
	public List<RusConfigInfo> selectRusConfigInfo(RusConfigInfo record) {
		return ydMapper.selectRusConfigInfo(record);
	}

	@Override
	public int updateRusConfigInfo(RusConfigInfo record) throws Exception {
		return ydMapper.updateRusConfigInfo(record);
	}

	@Override
	public int addRusConfigList(List<RusConfigInfo> list) {
		return ydMapper.addRusConfigList(list);
	}

	@Override
	public List<RedbalanceVo> selectRedbalanceVo(RedbalanceVo redbalanceVo) {
		return ydMapper.selectRedbalanceVo(redbalanceVo);
	}

	@Override
	public List<RedPackConfig> selectWithdrawDetailsNew(Map<String, Object> map) {
		return ydMapper.selectWithdrawDetailsNew(map);
	}

	@Override
	public List<RedPackConfig> selectWithdrawDetailsMaster() {
		return ydMapper.selectWithdrawDetailsMaster();
	}

	@Override
	public List<RedPackConfig> selectWithdrawDetailsSlave2() {
		return ydSlave2Mapper.selectWithdrawDetailsSlave2();
	}
	@Override
	public List<RedPackConfig> selectWithdrawDetailsSlave2ForQQ() {
		return ydSlave2Mapper.selectWithdrawDetailsSlave2ForQQ();
	}
	@Override
	public List<Map<String, Object>> selectWithdrawRealTimeForQQ(Map<String, Object> map) {
		return ydSlave2Mapper.selectWithdrawRealTimeForQQ(map);
	}

	@Override
	public int insertWithdrawRealTimeForQQ(Map<String, Object> map) {
		return ydMapper.insertWithdrawRealTimeForQQ(map);
	}
	@Override
	public int updateWithdrawRealTimeForQQ(Map<String, Object> map) {
		return ydMapper.updateWithdrawRealTimeForQQ(map);
	}
	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return ydMapper.queryListMap(sql);
	}
	
	
	@Override
	public int insertRedPackAuditDetail(List<RedPackConfig> list) {
		return ydMapper.insertRedPackAuditDetail(list);
	}
	@Override
	public int insertRedPackAuditDetailTwo(List<RedPackConfig> list) {
		return ydMapper.insertRedPackAuditDetailTwo(list);
	}

	@Override
	public List<RedPackConfig> selectRedPackAuditDetail(Map<String, Object> map) {
		return ydMapper.selectRedPackAuditDetail(map);
	}

	@Override
	public List<RedPackConfig> selectQQRedPackAuditDetail(
			Map<String, Object> map) {
		return ydMapper.selectQQRedPackAuditDetail(map);
	}

	@Override
	public List<RedPackConfig> selectWxNowRedPack(Map<String, Object> paramMap) {
		return ydMapper.selectWxNowRedPack(paramMap);
		
	}

	@Override
	public List<AloneProjectInfo> selectAloneProjectInfo(
			AloneProjectInfo aloneProjectInfo) {
		
		return ydMapper.selectAloneProjectInfo(aloneProjectInfo);
	}

	@Override
	public int insertAloneProjectInfo(AloneProjectInfo aloneProjectInfo) {
		return ydMapper.insertAloneProjectInfo(aloneProjectInfo);
	}

	@Override
	public int updateAloneProjectInfo(AloneProjectInfo aloneProjectInfo) {
		return ydMapper.updateAloneProjectInfo(aloneProjectInfo);
	}

	@Transactional(value = "slaveTransactionManager")
	@Override
	public int updateDnChannelInfo(DnChannelInfo dnChannelInfo) {
		int i = ydMapper.updateDnChannelInfo(dnChannelInfo);
		try {
			tfxtMapper.updateDnChannelInfo(dnChannelInfo);
		}catch (Exception e) {

		}
		return i;
	}

	@Transactional(value = "slaveTransactionManager")
	@Override
	public int insertDnChannelInfo(DnChannelInfo dnChannelInfo) {
		int i = ydMapper.insertDnChannelInfo(dnChannelInfo);
		try {
			tfxtMapper.insertDnChannelInfo(dnChannelInfo);
		}catch (Exception e) {

		}
		return i;
	}

	@Override
	public List<DnChannelInfo> selectDnChannelInfo(DnChannelInfo dnChannelInfo) {
		return ydMapper.selectDnChannelInfo(dnChannelInfo);
	}

	@Override
	public List<DnChannelType> selectDnChannelType() {
		return ydMapper.selectDnChannelType();
	}

	@Override
	public int updateAppIncome(AppChannelAppidInfo appChannelAppidInfo) {
		return ydMapper.updateAppIncome(appChannelAppidInfo);
	}

	@Override
	public List<DnAdConfigVo> selectDnAdConfigVo(DnAdConfigVo dnAdConfigVo) {
		return ydMapper.selectDnAdConfigVo(dnAdConfigVo);
	}

	@Override
	public int updateAdinfoKeys(List<DnAdConfigVo> list) {
		return ydMapper.updateAdinfoKeys(list);
	}

	@Override
	public int insertDnAdConfigVo(List<DnAdConfigVo> list) {
		return ydMapper.insertDnAdConfigVo(list);
	}

	@Override
	public int updateDnAdConfigVo(DnAdConfigVo dnAdConfigVo) {
		return ydMapper.updateDnAdConfigVo(dnAdConfigVo);
	}

	@Override
	public int updateBatchDnAdConfigVo(List<DnAdConfigVo> list) {
		return ydMapper.updateBatchDnAdConfigVo(list);
	}

	@Override
	public List<DnwxMmparamConfig> selectDnwxMmparamConfig(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.selectDnwxMmparamConfig(dnwxMmparamConfig);
	}

	@Override
	public int deleteDnwxMmparamConfig(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.deleteDnwxMmparamConfig(dnwxMmparamConfig);
	}
	
	@Override
	public int insertDnwxMmparamConfig(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.insertDnwxMmparamConfig(dnwxMmparamConfig);
	}
	
	@Override
	public int updateDnwxMmparamConfig(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.updateDnwxMmparamConfig(dnwxMmparamConfig);
	}
	
	@Override
	public int insertDnwxAllparamConfig(DnwxAllparamConfig all) {
		return ydMapper.insertDnwxAllparamConfig(all);
	}
	@Override
	public int updateDnwxAllparamConfig(DnwxAllparamConfig all) {
		return ydMapper.updateDnwxAllparamConfig(all);
	}
	@Override
	public int deleteDnwxAllparamConfig(DnwxAllparamConfig all) {
		return ydMapper.deleteDnwxAllparamConfig(all);
	}
	
	public List<DnwxMmparamConfig> selectDnwxMmparamConfigTwo(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.selectDnwxMmparamConfigTwo(dnwxMmparamConfig);
	}
	@Override
	public int deleteDnwxMmparamConfigTwo(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.deleteDnwxMmparamConfigTwo(dnwxMmparamConfig);
	}
	@Override
	public int insertDnwxMmparamConfigTwo(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.insertDnwxMmparamConfigTwo(dnwxMmparamConfig);
	}
	@Override
	public int updateDnwxMmparamConfigTwo(DnwxMmparamConfig dnwxMmparamConfig) {
		return ydMapper.updateDnwxMmparamConfigTwo(dnwxMmparamConfig);
	}

	@Override
	public WbguiFormconfig selectWbguiFormconfig(WbguiFormconfig wbguiFormconfig) {
		return ydMapper.selectWbguiFormconfig(wbguiFormconfig);
	}

	@Override
	public int updateAssetMaintenance(AssetMaintenance assetMaintenance) {
		return ydMapper.updateAssetMaintenance(assetMaintenance);
	}

	@Override
	public int insertAssetMaintenance(AssetMaintenance assetMaintenance) {
		return ydMapper.insertAssetMaintenance(assetMaintenance);
	}

	@Override
	public List<AssetMaintenance> selectAssetMaintenance(AssetMaintenance assetMaintenance) {
		return ydMapper.selectAssetMaintenance(assetMaintenance);
	}

	@Override
	public int deleteAssetMaintenance(AssetMaintenance assetMaintenance) {
		return ydMapper.deleteAssetMaintenance(assetMaintenance);
	}

	@Override
	public int editDnChaDauTotal(List<DnAvgPriceVo> list) {
		return ydMapper.editDnChaDauTotal(list);
	}
	
	@Override
	public List<String> tfxtQueryListString(String sql) {
		return tfxtMapper.queryListString(sql);
	}
	@Override
	public Map<String, Map<String, Object>> tfxtQueryListMapOfKey(String sql) {
		return tfxtMapper.queryListMapOfKey(sql);
	}

	@Override
	public int insertCsjBaseInfoList(List<CsjBaseInfoVo> list) {
		return tfxtMapper.insertCsjBaseInfoList(list);
	}
	@Override
	public int insertCsjBaseLogList(List<CsjBaseLogVo> list) {
		return tfxtMapper.insertCsjBaseLogList(list);
	}

	@Override
	public int deleteDnwxInterfaceConfig(DnwxInterfaceConfig record) {
		return ydMapper.deleteDnwxInterfaceConfig(record);
	}

	@Override
	public int insertDnwxInterfaceConfig(DnwxInterfaceConfig record) {
		return ydMapper.insertDnwxInterfaceConfig(record);
	}

	@Override
	public List<DnwxInterfaceConfig> selectDnwxInterfaceConfig(DnwxInterfaceConfig record) {
		return ydMapper.selectDnwxInterfaceConfig(record);
	}

	@Override
	public int updateDnwxInterfaceConfig(DnwxInterfaceConfig record) {
		return ydMapper.updateDnwxInterfaceConfig(record);
	}

	@Override
	public List<AppCategoryVo> selectAppCategoryList(Map map) {
		return ydMapper.selectAppCategory(map);
	}

	@Override
	public List<AppCategoryVo> selectAppCategoryListByName(Map map) {
		return ydMapper.selectAppCategoryByName(map);
	}

	@Override
	public int insertAppCategory(AppCategoryVo appCategoryVo) {
		return ydMapper.insertAppCategory(appCategoryVo);
	}

	@Override
	public int deleteAppCategory(AppCategoryVo appCategoryVo) {
		return ydMapper.deleteAppCategory(appCategoryVo);
	}

	@Override
	public int updateAppCategory(AppCategoryVo appCategoryVo) {
		return ydMapper.updateAppCategory(appCategoryVo);
	}

    @Override
    public int deleteDnChannelInfo(String cha_id) {
        return ydMapper.deleteDnChannelInfo(cha_id);
    }

    @Override
    public void updateChaStatu(DnChannelInfo dnChannelInfo) {
		ydMapper.updateChaStatu(dnChannelInfo);
    }

	@Override
	@Transactional(value = "slave2TransactionManager")
	public void insertRedpackdrawConfigByPid(String pid,String old) {
		//查询复制前pid的数据
		Map map = new HashMap();
		map.put("pid",old);
		List<RedpackdrawConfigInfo> list = ydSlave2Mapper.selectRedpackdrawConfig(map);
		//查询被复制的pid是否村咋子
		map.put("pid",pid);
		List<RedpackdrawConfigInfo> exist = ydSlave2Mapper.selectRedpackdrawConfig(map);
		if (exist != null && exist.size() != 0) {
			Asserts.fail("复制的项目id已存在");
		}
		//循环插入数据
		for (RedpackdrawConfigInfo r : list) {
			//修改为复制后的pid
			r.setPid(pid);
			ydSlave2Mapper.insertRedpackdrawConfig(r);
		}
	}

    @Override
	@Transactional(value = "slave2TransactionManager")
    public void batchInsertDnChannelTypeToTfxt(Map<String, Object> paramMap) {
		tfxtMapper.execSql("delete from dn_channel_type");
		tfxtMapper.batchExecSql(paramMap);
    }

	@Override
	@Transactional(value = "tfxtTransactionManager")
	public void batchInsertDnChannelInfoToTfxt(Map<String, Object> paramMap) {
		tfxtMapper.execSql("delete from dn_channel_info");
		tfxtMapper.batchExecSql(paramMap);
	}

	@Override
	@Transactional(value = "cleanTransactionManager")
	public void batchInsertDnChannelInfoToClean(Map<String, Object> paramMap) {
		umengSynLogMapper.execSql("delete from dn_channel_info");
		umengSynLogMapper.batchExecSql(paramMap);
	}

    @Override
    public List<RedPackConfig> selectHbUserWithdrawLogSum() {
        return ydSlave2Mapper.selectHbUserWithdrawLogSum();
    }

    @Override
    public List<Map<String, Object>> selectExamineDevice(Map<String, Object> param) {
		return ydMapper.selectExamineDevice(param);
    }

    @Override
	public int updateNofee(NOParam o) {
		return ydMapper.updateNofee(o);
	}

	@Override
	public int insertDnwxRuleAllConfig(DnwxAllparamConfig all) {
		return ydMapper.insertDnwxRuleAllConfig(all);
	}
	@Override
	public int updateDnwxRuleAllConfig(DnwxAllparamConfig all) {
		return ydMapper.updateDnwxRuleAllConfig(all);
	}
	@Override
	public int deleteDnwxRuleAllConfig(DnwxAllparamConfig all) {
		return ydMapper.deleteDnwxRuleAllConfig(all);
	}

	@Override
	public void batchUpateHongBaoInfoMch(HongBaoInfo param) {
		ydMapper.batchUpateHongBaoInfoMch(param);
	}

}
