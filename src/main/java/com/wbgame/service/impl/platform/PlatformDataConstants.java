package com.wbgame.service.impl.platform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.RefeshCacheConstant;
import com.wbgame.common.constants.RobotConstants;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.service.impl.PlatformDataServiceImpl;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.platform.BeanUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.Consts;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname PlatformDataConstants
 * @Description 平台数据常量值
 * @Date 2023/4/26 11:41
 */
@Component
public class PlatformDataConstants {

    private static final Logger logger = LoggerFactory.getLogger(PlatformDataConstants.class);

    private static RedisTemplate<String, Object> injectRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init() {
        injectRedisTemplate = redisTemplate;
    }



    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 8, 10, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

    /** 告警群id*/
    private static final String SDK_NOTICE_GROUP_CHAT_ID = "oc_abaaec074b476321827c5a22fb435ede";

    private static final String VIVO_GROUP_CHAT_ID = "oc_16dc65b3a6722d6d5b0baef1cb8d379f";


    /** 发送告警消息*/
    private static final String FS_GROUP_URL = "https://edc.vigame.cn:6115/fs/sendGroupMsg";


    public static class PLATFORM {
        public static String VIVO_APPINFO_URL = "https://dev.vivo.com.cn/webapi/app/list-page?cnName=&appType=%s&currentPageNum=%s&appStatus=0&timestamp=%s";

        public static String XIAOMI_APPINFO_URL = "https://dev.mi.com/uiueapi/myitems/myitemsV1?pageSize=10&searchingKeywords=&pageNum=%s&statusType=0&namespaceValue=0&mideveloper_ph=&userId=%s";
        public static String XIAOMI_APPINFO_URL_NEW = "https://dev.mi.com/pltapi/uiue/myitems/myitemsV1?pageNum=%s&pageSize=10&mideveloper_ph=&userId=%s";

        public static String HUAWEI_APPINFO_URL = "https://connect-api.cloud.huawei.com/api/";

        public static String OPPO = "oppo";
        public static String VIVO = "vivo";
        public static String XIAOMI = "xiaomi";
        public static String HUAWEI = "huawei";
        public static String HONOR = "honor";

        public static Map<String,String> HEADER = new HashMap<String,String>(){{
            put("oppo","qinguozhen,linxm");
            put("vivo","zhenghy,hehp");
            put("huawei","zhangmt,hehp");
            put("xiaomi","liangap");
            put("honor","zhangmt,yangxl");
        }};
    }

    public static class PAGE {

    }

    public static class RESERVATION {

    }


    public static class APPINFO {

        /**
         put("0","未提交");
         put("1","已上线");
         put("2","已下线");
         put("3","未发布");
         put("4","自动化审核中");
         put("5","审核中");
         put("6","审核驳回");
         put("7","定时发布");
         put("8","资质审核");
         put("9","资质驳回");
         put("10","资质通过");
         put("11","已冻结");
         put("12","报备成功");
         put("13","撤销上线");
         put("14","测试中");
         put("15", "升级中");
         put("16", "下线审核");
         put("17", "未定义");
         put("18", "下线驳回");
         put("19", "审核通过");
         put("20", "运营打回");
         put("21", "运营通过");
         put("22", "其他");
         */
        public static final String ONLINE_STATE = "1";
        //已下线状态
        public static final String OFFLINE_STATE = "2";
        public static final String PUBLISH_STATE = "7";
        public static final String REJECT_STATE = "6";
        public static final String REJECT_TWO_STATE = "20";
        //下线驳回
        public static final String OFFLINE_REJECT_STATE = "18";

        public static final String SYNC_APPINFO_STATE_INIT = "0";
        public static final String SYNC_APPINFO_STATE_SUCCESS = "1";
        public static final String SYNC_APPINFO_STATE_TOKEN_EXPARED = "2";
        public static final String SYNC_APPINFO_STATE_UNKOWN_ERROR = "3";
        public static final String SYNC_APPINFO_STATE_NOMATCH = "4";

        /** 是否请求SDK修改需求单接口*/
        public static final String POST_SDK = "1";

        //平台对应动能应用状态 https://vimedia.feishu.cn/docx/Z1j6dsRAsoREqsxNfv8c7Vefnkc

    }

    /**
     * 渠道产品id关联配置的产品状态转换成小游戏的编辑状态的对应关系
     * key：产品状态
     * value：小游戏状态变更状态，即小游戏状态变更api需要的格式
     */
    private static final Map<String,String> miniGameStatusChange = new HashMap<String,String>(){{
        //上线状态
        put("1","1");
        //审核中
        put("5","2");
        //被驳回
        put("6","3");
        //已下架
        put("2","4");
    }};

    /**
     * 获取token
     * 注：
     * 中国站点的domain是connect-api.cloud.huawei.com;欧洲站点的domain是:connect-api-dre.cloud;
     * 亚太站点的domain是:connect-api-dra.cloud;俄罗斯站点的domain是:connect-api-drru.cloud;
     */
    public static String getHwApiToken(String client_id, String client_secret) {
        /* 添加先从缓存获取逻辑 */
        String token = (String)injectRedisTemplate.opsForValue().get("huawei_api_access_token:"+client_id);
        if(token != null){
            return token;
        }
        try {
            HttpPost post = new HttpPost("https://connect-api.cloud.huawei.com/api/oauth2/v1/token");
            JSONObject keyString = new JSONObject();
            keyString.put("client_id", client_id);
            keyString.put("client_secret", client_secret);
            keyString.put("grant_type", "clieant_credentials");
            StringEntity entity = new StringEntity(keyString.toString(), Charset.forName("UTF-8"));
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            post.setEntity(entity);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(response.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();
                JSONObject object = JSON.parseObject(result);
                token = object.getString("access_token");
            }
            post.releaseConnection();
            httpClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(token != null){
            // 将token存入缓存，有效期20小时
            injectRedisTemplate.opsForValue().set("huawei_api_access_token:"+client_id, token, 20, TimeUnit.HOURS);
        }
        return token;
    }

    /**
     * 通知sdk并根据返回值告警
     * @param platformAppInfoVo
     * @return
     */
    public static boolean sendSdkOnlineMessage(List<PlatformAppInfoVo> list) {
        String url = RefeshCacheConstant.SDK_NOTICE_URL;
        //复制
        List<PlatformAppInfoVo> sendList = BeanUtils.copyList(list);
        //按包名分组取时间最新的一条
        sendList = sendList
                .stream()
                .filter(t->!BlankUtils.checkBlank(t.getPackagename())&&!BlankUtils.checkBlank(t.getBindEndTime()))
                .collect(Collectors.groupingBy(PlatformAppInfoVo::getPackagename, Collectors.maxBy(Comparator.comparing(PlatformAppInfoVo::getBindEndTime))))
                .values().stream()
                .map(Optional::get)
                .collect(Collectors.toList());
        for (PlatformAppInfoVo each : sendList) {
            threadPoolExecutor.submit(() -> {
                try {
                    Map<String, Object> paramsMap = new HashMap<>();
                    paramsMap.put("packageName", each.getPackagename());
                    paramsMap.put("versionName", each.getVersion());
                    paramsMap.put("channelTag", each.getChannel());
                    paramsMap.put("state", each.getState());
                    paramsMap.put("stateMsg", "");
                    paramsMap.put("appid", each.getAppid());

                    Map<String, String> headerMap = new HashMap<>();
                    headerMap.put("Content-Type", "application/json");

                    PlatformDataConstants.logger.info("sendSdkOnlineMessage httpPostJson:\n"+JSON.toJSONString(paramsMap));
                    String result = HttpRequest.httpPostJsonTwo(url, paramsMap, headerMap);
                    if (BlankUtils.checkBlank(result)) {
                        sendWarnMessage(each, "接口未响应");
                    } else {
                        JSONObject data = JSONObject.parseObject(result);
                        PlatformDataConstants.logger.info("sendSdkOnlineMessage params:"
                                + each.getAppid() + "-"
                                + each.getState() + "-"
                                + each.getCname() + "-"
                                + each.getTaccount() + "-"
                                + each.getTappid() + "-"
                                + each.getPackagename() + "-"
                                + each.getChannel() + "-"
                                + each.getVersion() + ",\nresult:" + data.toJSONString());
                        if (!"200".equals(data.getString("code"))) {
                            /* 客户端修改问题时不需要发送告警 -张炳杰.******** */
                            sendWarnMessage(each, data.getString("msg"));
                        }
                    }
                } catch (Exception e) {
                    PlatformDataConstants.logger.error("sendSdkOnlineMessage error: params:"+JSON.toJSONString(each),e);
                    sendWarnMessage(each,"后台处理异常");
                }
            });

        }
        return true;
    }

    /**
     * 通知sdk并根据返回值告警-驳回状态
     * @param list
     * @return
     */
    public static boolean sendSdkRejectMessage(List<PlatformAppInfoVo> list) {
        String url = RefeshCacheConstant.SDK_REJECT_NOTICE_URL;
        //复制
        List<PlatformAppInfoVo> sendList = BeanUtils.copyList(list);
        //按包名分组取时间最新的一条
        sendList = sendList
                .stream()
                .filter(t->!BlankUtils.checkBlank(t.getPackagename())&&!BlankUtils.checkBlank(t.getBindEndTime()))
                .collect(Collectors.groupingBy(PlatformAppInfoVo::getPackagename, Collectors.maxBy(Comparator.comparing(PlatformAppInfoVo::getBindEndTime))))
                .values().stream()
                .map(Optional::get)
                .collect(Collectors.toList());


        // 先进行去重操作，然后发送被驳回产品的内容告警
       /* sendList.forEach(vo -> {
            if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                    || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                // 立即送到飞书告警
                PlatformDataConstants.sendWarnMessage(vo, "识别到状态为被驳回");
            }
        });*/

        for (PlatformAppInfoVo each : sendList) {
            threadPoolExecutor.submit(() -> {
                try {
                    Map<String, Object> paramsMap = new HashMap<>();
                    paramsMap.put("packageName", each.getPackagename());
                    paramsMap.put("versionName", each.getVersion());
                    paramsMap.put("channelTag", each.getChannel());
                    paramsMap.put("state", each.getState());
                    // 驳回原因
                    paramsMap.put("stateMsg", (each.getRefuse_reason()!=null?each.getRefuse_reason():""));
                    paramsMap.put("appid", each.getAppid());

                    Map<String, String> headerMap = new HashMap<>();
                    headerMap.put("Content-Type", "application/json");
                    System.out.println(each.getAppid()+"\t"+"sendSdkRejectMessage httpPostJson:\n"+JSON.toJSONString(paramsMap));

                    String result = HttpRequest.httpPostJsonTwo(url, paramsMap, headerMap);
                    if (BlankUtils.checkBlank(result)) {
                        sendWarnMessage(each, "接口未响应");
                    } else {
                        JSONObject data = JSONObject.parseObject(result);
                        PlatformDataConstants.logger.info("sendSdkRejectMessage params:"
                                + each.getAppid() + "-"
                                + each.getState() + "-"
                                + each.getCname() + "-"
                                + each.getTaccount() + "-"
                                + each.getTappid() + "-"
                                + each.getPackagename() + "-"
                                + each.getChannel() + "-"
                                + each.getVersion() + ",\nresult:" + data.toJSONString());
                        if (!"200".equals(data.getString("code"))) {
                            /* 客户端修改问题时不需要发送告警 -张炳杰.********
                            sendWarnMessage(each, data.getString("msg")); */
                        }
                    }
                } catch (Exception e) {
                    PlatformDataConstants.logger.error("sendSdkRejectMessage error: params:"+JSON.toJSONString(each),e);
                    sendWarnMessage(each,"后台处理异常");
                }
            });

        }
        return true;
    }

    /**
     * 发送告警消息至飞书群
     * @param platformAppInfoVo
     * @return
     */
    public static boolean sendWarnMessage(PlatformAppInfoVo platformAppInfoVo,String responseMsg){

        Map<String,String> paramsMap = new HashMap<>();

        StringBuffer sb = new StringBuffer();
        sb.append("\\n");
        sb.append("平台:").append("   ").append(platformAppInfoVo.getPlatform()).append("\\n");
        sb.append("动能产品id:").append("   ").append(platformAppInfoVo.getAppid()).append("\\n");
        sb.append("平台产品id:").append("   ").append(platformAppInfoVo.getTappid()).append("\\n");
        sb.append("平台产品名称:").append("   ").append(platformAppInfoVo.getTappname()).append("\\n");
        sb.append("子渠道:").append("   ").append(platformAppInfoVo.getChannel()).append("\\n");
        sb.append("包名:").append("   ").append(platformAppInfoVo.getPackagename()).append("\\n");
        sb.append("版本号:").append("   ").append(platformAppInfoVo.getVersion()).append("\\n");
        sb.append("提示信息:").append("   ").append(responseMsg);

        String msg = Base64.encodeBase64String(sb.toString().getBytes());
        String userName = "";
        if (PLATFORM.HEADER.get(platformAppInfoVo.getPlatform())!=null){
            userName = PLATFORM.HEADER.get(platformAppInfoVo.getPlatform());
        }

        /* 异常状态告警，直接转为孙文凤 -20241203 */
        if("52".equals(platformAppInfoVo.getApp_category()) || "48".equals(platformAppInfoVo.getApp_category())
            || "工具iaa-E组".equals(platformAppInfoVo.getApp_category()) || "工具iap-D组".equals(platformAppInfoVo.getApp_category())){

            userName = "sunwf,kangxy";
        }

        paramsMap.put("msg",msg);
        paramsMap.put("userName",userName);
        paramsMap.put("chatId",SDK_NOTICE_GROUP_CHAT_ID);
        paramsMap.put("robot","robot5");

        String result = HttpRequest.httpPost(FS_GROUP_URL, paramsMap,new HashMap<>());
        logger.info("sendWarnMessage result:"+result);
        return true;
    }


    /**
     * 小游戏api：修改小游戏飞书的状态，并返回备注信息
     *
     * @param each 小游戏信息
     * @return 备注信息
     */
    public static String sendSdkMiniGameOnlineMessage(PlatformAppInfoVo each) {
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "*/*");
            //请求地址及参数封装
            PlatformDataConstants.logger.info("sendSdkMiniGameOnlineMessage appid={},version={},state={}", each.getAppid(), each.getVersion(), each.getState());
            String url = String.format(RefeshCacheConstant.SDK_MINI_GAME_EDIT_URL, each.getAppid(), each.getVersion(),miniGameStatusChange.get(each.getState()));
            String result = HttpRequest.get(url, headerMap);
            if (BlankUtils.checkBlank(result)) {
                sendWarnMessage(each, "接口未响应");
                return "接口未响应";
            }
            JSONObject resultObj = JSONObject.parseObject(result);
            PlatformDataConstants.logger.info("sendSdkMiniGameOnlineMessage params:"
                    + each.getAppid() + "-"
                    + each.getState() + "-"
                    + each.getCname() + "-"
                    + each.getTaccount() + "-"
                    + each.getTappid() + "-"
                    + each.getPackagename() + "-"
                    + each.getChannel() + "-"
                    + each.getVersion() + ",\nresult:" + resultObj.toJSONString());
            if (!"200".equals(resultObj.getString("code"))) {
                //客户端修改问题时不需要发送告警 -张炳杰.********
                sendWarnMessage(each, resultObj.getString("msg"));
                return resultObj.getString("msg");
            }
            //获取备注字段-remarke
            JSONObject dataObj = resultObj.getJSONObject("data");
            if (dataObj == null) {
                return Strings.EMPTY;
            }
            return dataObj.getString("remarke");
        } catch (Exception e) {
            PlatformDataConstants.logger.error("sendSdkMiniGameOnlineMessage error: params:" + JSON.toJSONString(each), e);
            sendWarnMessage(each, "后台处理异常");
            return "后台处理异常";
        }
    }


    /**
     * 对已下线的渠道产品配置数据发送告警至 vivo出包上线处理群
     *
     * @param offlineList 产品下线数据
     * @param appInfoMap  产品id,命名map,用于告警时appid转换成产品名称
     * @return 告警结果，true：告警发送成功，false：告警发送失败
     */
    public static boolean sendOfflineMessage(List<PlatformAppInfoVo> offlineList, Map<String, Map<String, Object>> appInfoMap) {
        try {
            //消息发送
            for (PlatformAppInfoVo appInfoVo : offlineList) {
                //发送消息模板
                String template = "\n" + "【产品名称】：%s\n" + "【渠道名称】：%s\n" + "【子渠道】：%s\n" + "【版本号】：%s\n" + "【状态】：%s\n";
                //消息数据封装
                String appName = null;
                if (appInfoMap.containsKey(appInfoVo.getAppid())) {
                    appName = (String) appInfoMap.get(appInfoVo.getAppid()).get("app_name");
                }
                String format = String.format(template, appName, appInfoVo.getTappname(), appInfoVo.getChannel(), appInfoVo.getVersion(), "已下线");
                // 下线产品发送 至 vivo出包上线处理群  red
                FeishuUtils.sendMsgCardToGroupRobot(VIVO_GROUP_CHAT_ID, RobotConstants.CONFIG_TOOL_ROBOT, format, "下线信息", "red", "zhenghy,wust,wumy");
            }
            return true;
        } catch (Exception e) {
            logger.error("sendOfflineMessage error: ",e);
            return false;
        }
    }

}
