package com.wbgame.mapper.redpack;

import com.wbgame.pojo.HbRiskLimitConfig;
import com.wbgame.pojo.mobile.HbFunctionSwitch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface HbFunctonSwitchMapper {


    List<HbFunctionSwitch> selectFunctonSwitchs(HbFunctionSwitch param);

    int addFunctonSwitch(HbFunctionSwitch param);

    int updateFunctonSwitch(HbFunctionSwitch param);

    int deleteFunctonSwitch(String pid);
}
