package com.wbgame.mapper.master.product;

import com.wbgame.pojo.product.SingularLandingConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description Singular落地页配置Mapper接口
 * @Date 2025/01/27
 */
public interface SingularLandingConfigMapper {

    /**
     * 查询配置列表
     *
     * @param config 查询条件
     * @return 配置列表
     */
    List<SingularLandingConfig> queryList(@Param("config") SingularLandingConfig config);

    /**
     * 根据ID查询配置
     *
     * @param id 主键ID
     * @return 配置信息
     */
    SingularLandingConfig queryById(@Param("id") Long id);

    /**
     * 根据SDK Key查询配置
     *
     * @param sdkKey SDK Key
     * @return 配置信息
     */
    SingularLandingConfig queryBySdkKey(@Param("sdkKey") String sdkKey);

    /**
     * 根据Bundle ID查询配置
     *
     * @param bundleId Bundle ID
     * @return 配置信息
     */
    SingularLandingConfig queryByBundleId(@Param("bundleId") String bundleId);

    /**
     * 新增配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int insert(@Param("config") SingularLandingConfig config);

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int update(@Param("config") SingularLandingConfig config);

    /**
     * 删除配置
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除配置
     *
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 检查SDK Key是否已存在
     *
     * @param sdkKey SDK Key
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的记录数
     */
    int checkSdkKeyExists(@Param("sdkKey") String sdkKey, @Param("excludeId") Long excludeId);

    /**
     * 检查Bundle ID是否已存在
     *
     * @param bundleId Bundle ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的记录数
     */
    int checkBundleIdExists(@Param("bundleId") String bundleId, @Param("excludeId") Long excludeId);
}
