package com.wbgame.mapper.tfxt;

import com.wbgame.pojo.jettison.param.AutoCoverParam;
import com.wbgame.pojo.jettison.param.CreativeMaterialParam;
import com.wbgame.pojo.jettison.param.HonorMaterialParam;
import com.wbgame.pojo.jettison.param.OppoMaterialParam;
import com.wbgame.pojo.jettison.vo.CreativeMaterialVo;
import com.wbgame.pojo.jettison.vo.HonorMaterialRule;
import com.wbgame.pojo.jettison.vo.OppoMaterialRule;
import com.wbgame.pojo.jettison.vo.VivoMaterialRule;

import java.util.List;

/**
 * @Description 创意素材库查询
 * <AUTHOR>
 * @Date 2024/10/30 18:24
 */
public interface CreativeMaterialMapper {

    List<CreativeMaterialVo> selectCreativeMaterials(CreativeMaterialParam param);

    List<VivoMaterialRule> selectVivoMaterialRule(AutoCoverParam param);

    List<OppoMaterialRule> selectOppoMaterialRule(OppoMaterialParam param);

    List<HonorMaterialRule> selectHonorMaterialRule(HonorMaterialParam param);
}
