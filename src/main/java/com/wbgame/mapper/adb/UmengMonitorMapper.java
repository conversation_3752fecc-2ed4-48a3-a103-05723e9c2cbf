package com.wbgame.mapper.adb;

import com.wbgame.pojo.PlatformAdDataRequestParam;
import com.wbgame.pojo.UmengAdIncomeVo;
import com.wbgame.pojo.UmengMonitorVo;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.advert.UmengOverseaReportVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface UmengMonitorMapper {
    List<UmengMonitorVo> selectUmengTaskLog(UmengMonitorVo param);

    List<String> getTasks();


    /** 渠道产品广告数据查询 */
    List<UmengAdIncomeReportVo> getUmengAdIncomeList(Map<String,String> paramMap);

    /** 渠道产品广告数据查询汇总 */
    UmengAdIncomeReportVo getUmengAdIncomeSum(Map<String,String> paramMap);


    // 渠道产品广告数据
    public int insertUmengAdIncomeList(List<UmengAdIncomeVo> list);
    // oppo渠道产品广告数据
    public int insertOppoAdIncomeList(List<UmengAdIncomeVo> list);

    @MapKey("mapkey")
    @Select("select singleid mapkey, date, CONCAT(date,'_',ifnull(cha, ''),'_',ifnull(tempName, '')) tempStr from dnwx_bi.wbgui_tempmodule")
    Map<String,Map<String,Object>> getTempNameForSingleid();

    void insertUmengAdIncomeOverseaList(List<UmengAdIncomeVo> dataList);

    UmengOverseaReportVo getUmengAdIncomeOverseaSum(PlatformAdDataRequestParam param);

    List<UmengOverseaReportVo> getUmengAdIncomeOverseaList(PlatformAdDataRequestParam param);

}
