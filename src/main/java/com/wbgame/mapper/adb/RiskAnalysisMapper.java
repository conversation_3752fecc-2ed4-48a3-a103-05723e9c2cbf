package com.wbgame.mapper.adb;

import com.wbgame.pojo.advert.RiskAnalysisActiveDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRatioDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRegDataVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname RiskAnalysisMapper
 * @Description TODO
 * @Date 2022/2/28 17:34
 */
public interface RiskAnalysisMapper {

    List<RiskAnalysisRegDataVo> getRegRiskAnalysis(Map map);

    RiskAnalysisRegDataVo getRegRiskAnalysisSum(Map map);

    List<RiskAnalysisActiveDataVo> getActiveRiskAnalysis(Map map);

    RiskAnalysisActiveDataVo getActiveRiskAnalysisSum(Map map);

    List<RiskAnalysisRatioDataVo> getRatioRiskAnalysis(Map map);

    RiskAnalysisRatioDataVo getRatioRiskAnalysisSum(Map map);
}
