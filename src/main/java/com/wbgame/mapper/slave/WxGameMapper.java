package com.wbgame.mapper.slave;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.wbgame.pojo.AppChannelAppidInfo;
import com.wbgame.pojo.AppExchangePermissions;
import com.wbgame.pojo.AppExchangeVolume;
import com.wbgame.pojo.AppExchangeVolumeTemInfo;
import com.wbgame.pojo.AppExchangeVolumeTotalVo;
import com.wbgame.pojo.AppExchangeVolumeVo;
import com.wbgame.pojo.GameAdInfo;

public interface WxGameMapper {
    int deleteGameAdInfo(GameAdInfo gameAdInfo);
    int insertGameAdInfo(List<GameAdInfo> list);
    //合作方查询界面
    List<GameAdInfo> selectGameAdInfo(Map<String,Object> map);
    GameAdInfo selectSumGameAdInfo(Map<String,Object> map);
    int updateGameAdInfo(GameAdInfo gameAdInfo);
    //小游戏广告数据信息查询
    List<GameAdInfo>selectGameAdInfoList(Map<String,Object> map);
    //手动同步
    int sycGameAdInfo (List<GameAdInfo> list);
    
    
    /*//小游戏-换量数据查询-卖量收入报表-卖sell
    List<AppExchangeVolume> selectAppExchangeVolume(Map<String,Object> map);
    int insertAppExchangeVolume(List<AppExchangeVolume> list);
    int updateAppExchangeVolume(AppExchangeVolume appExchangeVolume);
    
    //小游戏-换量数据查询-卖量收入报表-买buy
    List<AppExchangeVolume> selectAppExchangeVolumeBuy(Map<String,Object> map);
    int insertAppExchangeVolumeBuy(List<AppExchangeVolume> list);
    int updateAppExchangeVolumeBuy(AppExchangeVolume appExchangeVolume);
    
    String [] selectGameNameSell();
    String [] selectAppidSell();
    String [] selectOrgChannelSell();
    String [] selectOrgGameNameSell();
    String [] selectChannelNameSell();
    String [] selectPricingTypeSell();
    AppExchangeVolume selectAppExchangeSell(Map<String,Object> map);
    AppExchangeVolume selectAppExchangeBuy(Map<String,Object> map);
    String [] selectGameNameBuy();
    String [] selectAppidBuy();
    String [] selectOrgChannelBuy();
    String [] selectOrgGameNameBuy();
    String [] selectChannelNameBuy();
    String [] selectChannelName();
    String [] selectPricingTypeBuy();
    
    //小游戏-渠道产品数据导入
    int insertAppChannelAppidInfo(List<AppChannelAppidInfo> list);
    List<AppChannelAppidInfo> selectAppChannelAppidInfo(Map<String,Object> map);
   
    
    int updateAppChannelAppidInfo(AppChannelAppidInfo appChannelAppidInfo);
    //小游戏-换量汇总查询
    List<AppExchangeVolumeVo>selectAppExchangeVolumeVo(Map<String,Object> map);
    
    //小游戏-换量汇总查询
    List<AppExchangeVolumeTotalVo> selectAppExchangeVolumeTotal(Map<String,Object> map);
    //小游戏-换量汇总查询 手动修改 投放买量 投放支出
    int updateAppExchangeVolumeTemInfo(AppExchangeVolumeTemInfo appExchangeVolumeTemInfo);
	
	int editAppExchangeName (@Param("map")Map<String,String> map);
	
	int deleteAppExchangeVolumeSell(@Param("ids")String ids);
	int deleteAppExchangeVolumeBuy(@Param("ids")String ids);
	
	//页面权限
	int updateAppExchangePermissions(AppExchangePermissions appExchangePermissions);
	int deleteAppExchangePermissions(AppExchangePermissions appExchangePermissions);
	int insertAppExchangePermissions(AppExchangePermissions appExchangePermissions);
	List<AppExchangePermissions> selectAppExchangePermissions(AppExchangePermissions appExchangePermissions);*/
    
}