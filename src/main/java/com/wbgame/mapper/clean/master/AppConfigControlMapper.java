package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.AppConfigControl;
import com.wbgame.pojo.clean.AppConfigControlVO;

import java.util.List;

public interface AppConfigControlMapper {

    int deleteAppConfig(List<Integer> idList);

    int insertAppConfig(AppConfigControl record);

    List<AppConfigControlVO> selectAppConfig(AppConfigControl example);

    int updateAppConfig(AppConfigControl record);

    Integer duplicateCheck(AppConfigControl control);
}