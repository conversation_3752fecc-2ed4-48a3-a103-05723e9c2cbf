package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.toonstory.ToonProduct;
import com.wbgame.pojo.clean.toonstory.ToonProductDTO;
import com.wbgame.pojo.clean.toonstory.ToonProductVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ToonProductMapper {


    int deleteToonProduct(List<Long> idList);

    int insertToonProduct(ToonProduct record);

    List<ToonProductVO> selectToonProduct(ToonProductDTO dto);

    int updateToonProduct(ToonProduct record);

    int updateByPrimaryKey(ToonProduct record);

    /**
     * 查找商品名是否被占用
     */
    @Select("select id from gj_b.toon_product where product_name = #{productName} limit 1")
    Long selectByProductName(String productName);

    /**
     * 修改排序
     */
    int updateProductSort(List<ToonProduct> relList);

    int updateStatus(ToonProduct product);


    /**
     * 刷新缓存用
     */
    List<ToonProductVO> selectCache(String cha);


    List<ToonProductVO> selectToonProductByCha(String cha);
}