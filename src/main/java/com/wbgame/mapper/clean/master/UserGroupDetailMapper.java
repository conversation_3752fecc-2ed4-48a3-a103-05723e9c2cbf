package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.UserGroupDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserGroupDetailMapper {

    int deleteUserGroupDetail(List<Long> configId);

    int insertUserGroupDetail(List<UserGroupDetail> record);

    /**
     * 根据国家查询对应分组
     *
     * @param example
     * @return
     */
    List<UserGroupDetail> selectUserGroupDetail(List<UserGroupDetail> example);

    /**
     * 根据configId查询关联数据
     * @param configId
     * @return
     */
    List<UserGroupDetail> selectUserGroupDetailById(Long configId);

    int updateUserGroupDetail(UserGroupDetail record);

    /**
     * 根据 产品+渠道/项目id + 地区获取数据
     */
    List<UserGroupDetail> selectUserGroupDetailCountry(

            @Param("appId") String appId,
            @Param("prjId") String prjId,
            @Param("cha") String cha,
            @Param("country") String country
    );

}