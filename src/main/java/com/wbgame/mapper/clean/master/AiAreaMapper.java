package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.aipaint.AiArea;
import com.wbgame.pojo.clean.aipaint.AiAreaDTO;
import com.wbgame.pojo.clean.aipaint.AiAreaVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AiAreaMapper {

    int insertAiArea(AiArea record);

    List<AiAreaVO> selectAiAreaMapper(AiAreaDTO dto);

    int updateAiArea(AiArea record);

    /**
     * 获取运营地区名是否存在
     */
    @Select("select id from gj_b.face_plus_ai_area where area_name = #{areaName} limit 1")
    Long selectOperationAreaNameLinkAppId(@Param("areaName") String areaName);

    /**
     * 获取运营地区下拉列表
     */
    @Select("select id, area_name areaName from gj_b.face_plus_ai_area")
    List<AiAreaVO> getAreaName();

    /**
     * 根据ID获取数据
     */
    @Select("select 1 from gj_b.face_plus_ai_area where id = #{id}")
    Integer getAreaById(Long id);
}