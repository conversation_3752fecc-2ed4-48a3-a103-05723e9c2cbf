package com.wbgame.mapper.clean.master;

import com.wbgame.pojo.clean.SuperSafeControlV3;
import com.wbgame.pojo.clean.SuperSafeControlV3DTO;
import com.wbgame.pojo.clean.SuperSafeControlV3VO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SuperSafeControlV3Mapper {
    

    int deleteByIdList(List<Integer> idList);

    int insertSuperSafe(SuperSafeControlV3 superSafeControlV3);
    

    int updateSuperSafeControlV3(@Param("idList") List<Integer> idList, @Param("super") SuperSafeControlV3 superSafeControlV3);
    

    int updateByPrimaryKey(SuperSafeControlV3 superSafeControlV3);

    int checkForUniqueness(SuperSafeControlV3DTO superSafeControlV3DTO);

    List<SuperSafeControlV3VO> selectByCondition(SuperSafeControlV3DTO superSafeControlV3DTO);
}