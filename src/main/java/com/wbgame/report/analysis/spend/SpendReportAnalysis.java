package com.wbgame.report.analysis.spend;

import lombok.NoArgsConstructor;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date
 * @description
 **/
@NoArgsConstructor
public class SpendReportAnalysis {

    public static StatisticSpendReport differ(StatisticSpendReport old, StatisticSpendReport n) throws IllegalAccessException {
        StatisticSpendReport statisticSpendReport = new StatisticSpendReport();
        Field[] fields = old.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            if ("String".equals(((Class<?>) field.getGenericType()).getSimpleName())) {
                field.set(statisticSpendReport, field.get(old));
            } else if ("Integer".equals(((Class<?>) field.getGenericType()).getSimpleName())) {
                if ("type".equals(field.getName())) {
                    continue;
                }
                field.set(statisticSpendReport, (int)field.get(old) - (int)field.get(n));
            } else if ("Double".equals(((Class<?>) field.getGenericType()).getSimpleName())){
                field.set(statisticSpendReport, (double)field.get(old) - (double)field.get(n));
            }
        }
        statisticSpendReport.setType(2);
        return statisticSpendReport;
    }

}
