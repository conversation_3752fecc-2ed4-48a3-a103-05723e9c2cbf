package com.wbgame.task;

import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.service.AdService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BigdataTask测试类
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class BigdataTaskTest {

    private static final Logger logger = LoggerFactory.getLogger(BigdataTaskTest.class);

    @Mock
    private AdMapper adMapper;

    @Mock
    private HaiwaiCfgMapper haiwaiCfgMapper;

    @Mock
    private AdService adService;

    @InjectMocks
    private BigdataTask bigdataTask;

    /**
     * 测试批量写入变现数据汇总报表方法
     */
    @Test
    public void testBatchInsertExtendRevenueReport() {
        // 准备测试数据
        List<Map<String, String>> mockDataList = new ArrayList<>();
        Map<String, String> mockData = new HashMap<>();
        mockData.put("date", "2023-12-01");
        mockData.put("dnappid", "test_app_001");
        mockData.put("agent", "test_agent");
        mockData.put("placement_type", "banner");
        mockData.put("request_count", "1000");
        mockData.put("return_count", "800");
        mockData.put("pv", "600");
        mockData.put("revenue", "150.50");
        mockDataList.add(mockData);

        // 模拟haiwaiCfgMapper.queryListMapOne返回数据
        when(haiwaiCfgMapper.queryListMapOne(anyString())).thenReturn(mockDataList);

        // 模拟adMapper.batchExecSql执行成功
        when(adMapper.batchExecSql(any(Map.class))).thenReturn(1);

        // 执行测试方法
        try {
            bigdataTask.batchInsertExtendRevenueReport();
            logger.info("测试批量写入变现数据汇总报表方法执行成功");
        } catch (Exception e) {
            logger.error("测试批量写入变现数据汇总报表方法执行失败", e);
        }

        // 验证方法调用
        verify(haiwaiCfgMapper, times(1)).queryListMapOne(anyString());
        verify(adMapper, times(1)).batchExecSql(any(Map.class));
    }

    /**
     * 测试批量写入变现数据汇总报表方法 - 无数据情况
     */
    @Test
    public void testBatchInsertExtendRevenueReportWithNoData() {
        // 模拟haiwaiCfgMapper.queryListMapOne返回空数据
        when(haiwaiCfgMapper.queryListMapOne(anyString())).thenReturn(new ArrayList<>());

        // 执行测试方法
        try {
            bigdataTask.batchInsertExtendRevenueReport();
            logger.info("测试批量写入变现数据汇总报表方法（无数据）执行成功");
        } catch (Exception e) {
            logger.error("测试批量写入变现数据汇总报表方法（无数据）执行失败", e);
        }

        // 验证方法调用
        verify(haiwaiCfgMapper, times(1)).queryListMapOne(anyString());
        verify(adMapper, never()).batchExecSql(any(Map.class));
    }

    /**
     * 测试批量写入变现数据汇总报表方法 - 异常情况
     */
    @Test
    public void testBatchInsertExtendRevenueReportWithException() {
        // 模拟haiwaiCfgMapper.queryListMapOne抛出异常
        when(haiwaiCfgMapper.queryListMapOne(anyString())).thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试方法
        try {
            bigdataTask.batchInsertExtendRevenueReport();
            logger.info("测试批量写入变现数据汇总报表方法（异常情况）执行完成");
        } catch (Exception e) {
            logger.error("测试批量写入变现数据汇总报表方法（异常情况）捕获异常", e);
        }

        // 验证方法调用
        verify(haiwaiCfgMapper, times(1)).queryListMapOne(anyString());
        verify(adMapper, never()).batchExecSql(any(Map.class));
    }
}
