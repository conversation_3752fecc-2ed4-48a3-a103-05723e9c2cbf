package com.wbgame.service;

import com.wbgame.pojo.PermissionApprovalResponseVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 权限申请审批服务测试
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PermissionApprovalServiceTest {

    @Autowired
    private PermissionApprovalService permissionApprovalService;

    @Test
    public void testSubmitPermissionApplication() {
        // 创建测试请求
        PermissionApprovalResponseVO request = new PermissionApprovalResponseVO();
        request.setApplicantUsername("caow"); // 需要替换为实际存在的用户名
//        request.setApplicationType(1); // 1-界面权限
//        request.setApplicationMark("userRiskReport"); // 测试页面标识
        request.setApplicationType(2); // 1-界面权限
        request.setApplicationMark("13665"); // 测试页面标识
        request.setApplicationDetail("申请测试页面访问权限");

        try {
            // 执行权限申请
            PermissionApprovalResponseVO response = permissionApprovalService.submitPermissionApplication(request);
            
            // 输出结果
            System.out.println("申请人：" + response.getApplicantUsername());
            System.out.println("申请人昵称：" + response.getApplicantNickName());
            System.out.println("申请人部门：" + response.getApplicantDepartmentName());
            System.out.println("申请类型：" + response.getApplicationType());
            System.out.println("申请内容：" + response.getApplicationMark());
            System.out.println("审批流程：" + response.getApprovalProcess());
            
            if (response.getApprovers() != null) {
                System.out.println("审批人数量：" + response.getApprovers().size());
                response.getApprovers().forEach(approver -> {
                    System.out.println("审批人：" + approver.getNick_name() +
                                     "(" + approver.getLogin_name() + "), " +
                                     "级别：" + approver.getApprovalLevel() + ", " +
                                     "有权限：" + approver.getHasPermission());
                });
            }
            
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
